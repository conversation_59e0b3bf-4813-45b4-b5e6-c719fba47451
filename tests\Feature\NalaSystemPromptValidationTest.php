<?php

namespace Tests\Feature;

use App\Http\Controllers\Nala\ChatController;
use App\Http\Controllers\Nala\TutorController;
use App\Models\Category;
use App\Models\Course;
use App\Models\MembershipPlan;
use App\Models\Role;
use App\Models\TutorProfile;
use App\Models\User;
use App\Services\NalaPromptTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class NalaSystemPromptValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $tutor;
    protected $tutorProfile;
    protected $course;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => Role::USER]);
        Role::create(['name' => Role::TUTOR]);
        
        // Create membership plan
        $membershipPlan = MembershipPlan::create([
            'name' => 'Free Plan',
            'slug' => 'free',
            'price' => 0,
            'duration_months' => 12,
            'nala_prompts' => 10,
        ]);

        // Create category
        $category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Programming courses',
        ]);

        // Create a tutor user
        $this->tutor = User::create([
            'name' => 'Real Tutor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'current_membership_id' => $membershipPlan->id,
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
        ]);
        $this->tutor->syncRoles([Role::USER, Role::TUTOR]);

        // Create tutor profile
        $this->tutorProfile = TutorProfile::create([
            'user_id' => $this->tutor->id,
            'public_name' => 'Real Tutor Name',
            'public_name_slug' => 'real-tutor-name',
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
            'bio' => 'Experienced developer and educator',
            'status' => 'approved',
        ]);

        // Create a student user
        $this->user = User::create([
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'current_membership_id' => $membershipPlan->id,
        ]);
        $this->user->syncRoles([Role::USER]);

        // Create a published course
        $this->course = Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Real Course Title',
            'slug' => 'real-course-title',
            'description' => 'A real course description',
            'level' => 'beginner',
            'price' => 100000,
            'status' => 'published',
            'published_at' => now(),
        ]);
    }

    public function test_chat_controller_system_prompt_includes_data_validation_instructions()
    {
        // Authenticate the user for this test
        $this->actingAs($this->user);

        // Mock the NalaPromptTrackingService and bind it to the container
        $mockService = $this->createMock(NalaPromptTrackingService::class);
        $this->app->instance(NalaPromptTrackingService::class, $mockService);
        $chatController = app(ChatController::class);

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($chatController);
        $method = $reflection->getMethod('buildRouteSpecificSystemPrompt');
        $method->setAccessible(true);

        // Mock user profile data
        $userProfile = [
            'basic_info' => [
                'name' => 'Test Student',
                'email' => '<EMAIL>'
            ]
        ];

        // Mock conversation object
        $conversation = (object) ['started_route' => 'user.courses'];

        $systemPrompt = $method->invoke($chatController, 'user_dashboard', $userProfile, $conversation, null);
        
        // Verify the system prompt includes data validation instructions
        $this->assertStringContainsString('JANGAN PERNAH buat atau sebutkan nama tutor yang tidak ada dalam data konteks', $systemPrompt);
        $this->assertStringContainsString('JANGAN PERNAH gunakan contoh nama seperti \'Kak Sandhika Galih\'', $systemPrompt);
        $this->assertStringContainsString('HANYA gunakan nama tutor yang benar-benar ada dalam data konteks yang diberikan', $systemPrompt);
        $this->assertStringContainsString('JANGAN PERNAH buat informasi palsu tentang status publikasi kursus atau ujian', $systemPrompt);
        $this->assertStringContainsString('HANYA gunakan informasi yang benar-benar tersedia dalam data konteks', $systemPrompt);
    }

    public function test_tutor_controller_system_prompt_includes_data_validation_instructions()
    {
        $tutorController = new TutorController();
        
        // Use reflection to access the private method
        $reflection = new \ReflectionClass($tutorController);
        $method = $reflection->getMethod('buildTutorSystemPrompt');
        $method->setAccessible(true);
        
        // Mock user profile and tutor data
        $userProfile = [
            'basic_info' => [
                'name' => 'Test Student'
            ]
        ];
        
        $tutorData = [
            'tutor_info' => [
                'name' => 'Real Tutor Name',
                'job_title' => 'Senior Developer',
                'company' => 'Tech Company'
            ],
            'stats' => [
                'total_courses' => 1,
                'total_exams' => 0
            ],
            'courses' => [
                [
                    'title' => 'Real Course Title',
                    'level' => 'beginner',
                    'status' => 'published',
                    'is_published' => true
                ]
            ]
        ];
        
        $systemPrompt = $method->invoke($tutorController, $userProfile, $tutorData, 'free');
        
        // Verify the system prompt includes data validation instructions
        $this->assertStringContainsString('JANGAN PERNAH buat informasi yang tidak ada dalam data konteks', $systemPrompt);
        $this->assertStringContainsString('JANGAN PERNAH sebutkan nama tutor lain yang tidak ada dalam data', $systemPrompt);
        $this->assertStringContainsString('JANGAN PERNAH buat status publikasi kursus yang tidak sesuai data', $systemPrompt);
        $this->assertStringContainsString('JANGAN PERNAH gunakan contoh nama seperti \'Kak Sandhika Galih\'', $systemPrompt);
        $this->assertStringContainsString('HANYA gunakan informasi yang benar-benar tersedia dalam data konteks tutor', $systemPrompt);
        
        // Verify the system prompt includes course status information
        $this->assertStringContainsString('Status: DIPUBLIKASIKAN', $systemPrompt);
        $this->assertStringContainsString('STATUS PUBLIKASI: 1 dari 1 kursus telah dipublikasikan', $systemPrompt);
    }

    public function test_tutor_context_includes_course_publication_status()
    {
        // Test that the tutor context data structure includes publication status
        $tutorContext = [
            'tutor' => [
                'id' => $this->tutor->id,
                'name' => $this->tutorProfile->public_name,
                'slug' => $this->tutorProfile->public_name_slug,
            ],
            'courses' => [
                [
                    'id' => $this->course->id,
                    'title' => $this->course->title,
                    'status' => $this->course->status,
                    'is_published' => $this->course->status === 'published',
                    'published_at' => $this->course->published_at->format('Y-m-d H:i:s'),
                ]
            ]
        ];
        
        // Verify the context includes publication status
        $this->assertEquals('published', $tutorContext['courses'][0]['status']);
        $this->assertTrue($tutorContext['courses'][0]['is_published']);
        $this->assertNotNull($tutorContext['courses'][0]['published_at']);
        
        // Verify no fictional tutor names
        $this->assertEquals('Real Tutor Name', $tutorContext['tutor']['name']);
        $this->assertNotEquals('Kak Sandhika Galih', $tutorContext['tutor']['name']);
        $this->assertNotEquals('Sandhika Galih', $tutorContext['tutor']['name']);
    }

    public function test_system_prompt_prevents_fictional_data_generation()
    {
        // Authenticate the user for this test
        $this->actingAs($this->user);

        // Mock the NalaPromptTrackingService and bind it to the container
        $mockService = $this->createMock(NalaPromptTrackingService::class);
        $this->app->instance(NalaPromptTrackingService::class, $mockService);
        $chatController = app(ChatController::class);

        // Use reflection to access the private method
        $reflection = new \ReflectionClass($chatController);
        $method = $reflection->getMethod('buildRouteSpecificSystemPrompt');
        $method->setAccessible(true);

        $userProfile = [
            'basic_info' => [
                'name' => 'Test Student'
            ]
        ];

        // Mock conversation object
        $conversation = (object) ['started_route' => 'user.courses'];

        $systemPrompt = $method->invoke($chatController, 'user_dashboard', $userProfile, $conversation, null);
        
        // Verify the prompt explicitly forbids common fictional names
        $forbiddenInstructions = [
            'JANGAN PERNAH buat atau sebutkan nama tutor yang tidak ada dalam data konteks',
            'JANGAN PERNAH gunakan contoh nama seperti \'Kak Sandhika Galih\'',
            'JANGAN PERNAH buat informasi palsu tentang status publikasi',
            'HANYA gunakan nama tutor yang benar-benar ada dalam data konteks',
            'HANYA gunakan informasi yang benar-benar tersedia dalam data konteks'
        ];
        
        foreach ($forbiddenInstructions as $instruction) {
            $this->assertStringContainsString($instruction, $systemPrompt, 
                "System prompt should include instruction: {$instruction}");
        }
    }
}
