<?php

namespace Tests\Feature;

use App\Http\Controllers\Nala\UserProfileController;
use App\Models\Category;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\ExamAttempt;
use App\Models\LessonProgress;
use App\Models\MembershipPlan;
use App\Models\Role;
use App\Models\TutorProfile;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NalaUserDashboardContextTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $tutor;
    protected $course;
    protected $userProfileController;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => Role::USER]);
        Role::create(['name' => Role::TUTOR]);
        
        // Create membership plan
        $membershipPlan = MembershipPlan::create([
            'name' => 'Free Plan',
            'slug' => 'free',
            'price' => 0,
            'duration_months' => 12,
            'nala_prompts' => 10,
        ]);

        // Create category
        $category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Programming courses',
        ]);

        // Create a tutor user
        $this->tutor = User::create([
            'name' => 'Test Tutor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'current_membership_id' => $membershipPlan->id,
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
        ]);
        $this->tutor->syncRoles([Role::USER, Role::TUTOR]);

        // Create tutor profile
        TutorProfile::create([
            'user_id' => $this->tutor->id,
            'public_name' => 'Test Tutor Public',
            'public_name_slug' => 'test-tutor-public',
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
            'bio' => 'Experienced developer and educator',
        ]);

        // Create a student user
        $this->user = User::create([
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'current_membership_id' => $membershipPlan->id,
            'job_title' => 'Junior Developer',
            'company' => 'Startup Inc',
            'skills' => ['PHP', 'Laravel', 'JavaScript'],
        ]);
        $this->user->syncRoles([Role::USER]);

        // Create a course
        $this->course = Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Laravel Fundamentals',
            'slug' => 'laravel-fundamentals',
            'description' => 'Learn Laravel from scratch',
            'level' => 'beginner',
            'price' => 100000,
            'status' => 'published',
        ]);

        // Create course enrollment
        CourseEnrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $this->course->id,
            'status' => 'active',
            'amount_paid' => 100000,
            'enrolled_at' => now()->subDays(10),
        ]);

        $this->userProfileController = new UserProfileController();
    }

    public function test_user_profile_controller_fetches_real_data()
    {
        $profile = $this->userProfileController->buildUserProfile($this->user);

        // Test basic info
        $this->assertEquals('Test Student', $profile['basic_info']['name']);
        $this->assertEquals('<EMAIL>', $profile['basic_info']['email']);

        // Test professional info
        $this->assertEquals('Junior Developer', $profile['professional_info']['job_title']);
        $this->assertEquals('Startup Inc', $profile['professional_info']['company']);
        $this->assertEquals(['PHP', 'Laravel', 'JavaScript'], $profile['professional_info']['skills']);

        // Test learning data exists
        $this->assertArrayHasKey('learning_data', $profile);
        $this->assertArrayHasKey('enrolled_courses', $profile['learning_data']);
        $this->assertArrayHasKey('statistics', $profile['learning_data']);
        $this->assertArrayHasKey('favorite_tutors', $profile['learning_data']);
    }

    public function test_user_dashboard_context_includes_real_course_data()
    {
        $context = $this->userProfileController->buildUserDashboardContext($this->user);

        // Test user info
        $this->assertEquals('Test Student', $context['user_info']['name']);
        $this->assertEquals('free', $context['user_info']['membership_level']);

        // Test enrolled courses
        $enrolledCourses = $context['enrolled_courses'];
        $this->assertCount(1, $enrolledCourses);
        
        $course = $enrolledCourses->first();
        $this->assertEquals('Laravel Fundamentals', $course['title']);
        $this->assertEquals('beginner', $course['level']);
        $this->assertEquals('active', $course['status']);
        $this->assertEquals('Test Tutor Public', $course['tutor_name']);
        $this->assertEquals('test-tutor-public', $course['tutor_slug']);

        // Test favorite tutors
        $favoriteTutors = $context['favorite_tutors'];
        $this->assertCount(1, $favoriteTutors);

        $tutor = $favoriteTutors->first();

        $this->assertEquals('Test Tutor Public', $tutor['name']);
        $this->assertEquals('test-tutor-public', $tutor['slug']);
        $this->assertEquals('Senior Developer', $tutor['job_title']);
        $this->assertEquals('Tech Company', $tutor['company']);
        $this->assertEquals(1, $tutor['courses_with_user']);
    }

    public function test_user_dashboard_context_includes_learning_statistics()
    {
        $context = $this->userProfileController->buildUserDashboardContext($this->user);

        $stats = $context['learning_stats'];
        $this->assertEquals(1, $stats['total_enrolled']);
        $this->assertEquals(0, $stats['completed_courses']);
        $this->assertEquals(0, $stats['total_learning_hours']);
        $this->assertEquals(0, $stats['exam_attempts']);
        $this->assertEquals(0, $stats['passed_exams']);
    }

    public function test_guest_user_gets_appropriate_context()
    {
        $context = $this->userProfileController->buildUserDashboardContext(null);

        $this->assertEquals('Pengunjung', $context['user_info']['name']);
        $this->assertEquals('guest', $context['user_info']['membership_level']);
        $this->assertEquals(0, $context['learning_stats']['total_enrolled']);
        $this->assertCount(0, $context['enrolled_courses']);
        $this->assertCount(0, $context['favorite_tutors']);
        $this->assertArrayHasKey('recommendations', $context);
        $this->assertEquals('register', $context['recommendations']['cta']);
    }

    public function test_user_recommendations_are_personalized()
    {
        $context = $this->userProfileController->buildUserDashboardContext($this->user);

        $recommendations = $context['recommendations'];
        $this->assertIsArray($recommendations);

        // Should have course completion recommendation since user has active course
        $courseCompletionRec = collect($recommendations)->firstWhere('type', 'course_completion');
        $this->assertNotNull($courseCompletionRec);
        $this->assertStringContainsString('1 kursus yang sedang berjalan', $courseCompletionRec['message']);

        // Should have tutor-based recommendation
        $tutorRec = collect($recommendations)->firstWhere('type', 'tutor_courses');
        $this->assertNotNull($tutorRec);
        $this->assertEquals('Test Tutor Public', $tutorRec['tutor_name']);
        $this->assertEquals('test-tutor-public', $tutorRec['tutor_slug']);
    }

    public function test_nala_receives_accurate_tutor_data_for_user_courses()
    {
        $context = $this->userProfileController->buildUserDashboardContext($this->user);

        // Verify that only real tutors from enrolled courses are included
        $enrolledCourses = $context['enrolled_courses'];
        $this->assertCount(1, $enrolledCourses);

        $course = $enrolledCourses->first();
        $this->assertEquals('Test Tutor Public', $course['tutor_name']);
        $this->assertEquals('test-tutor-public', $course['tutor_slug']);

        // Verify favorite tutors only include real tutors
        $favoriteTutors = $context['favorite_tutors'];
        $this->assertCount(1, $favoriteTutors);

        $tutor = $favoriteTutors->first();
        $this->assertEquals('Test Tutor Public', $tutor['name']);
        $this->assertEquals('test-tutor-public', $tutor['slug']);

        // Ensure no fictional tutors like 'Kak Sandhika Galih' are present
        $allTutorNames = $enrolledCourses->pluck('tutor_name')->merge($favoriteTutors->pluck('name'));
        $this->assertNotContains('Kak Sandhika Galih', $allTutorNames);
        $this->assertNotContains('Sandhika Galih', $allTutorNames);
    }
}
