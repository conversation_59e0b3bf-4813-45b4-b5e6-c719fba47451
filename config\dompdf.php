<?php

return [
    /*
    |--------------------------------------------------------------------------
    | DomPDF Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the DomPDF library.
    | These settings control how PDFs are generated and rendered.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Settings
    |--------------------------------------------------------------------------
    |
    | The default DomPDF settings. Some are required.
    |
    */
    "font_dir" => storage_path('fonts/'), // advised by dompdf (https://github.com/dompdf/dompdf/pull/782)

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | html5lib sends a lot of warnings, by default it is to not clutter the
    | output.
    |
    */
    "log_output_file" => false,

    /*
    |--------------------------------------------------------------------------
    | Remote File Access
    |--------------------------------------------------------------------------
    |
    | Determines whether DomPDF can access remote files (via http/ftp).
    | Set to false for security reasons.
    |
    */
    "enable_remote" => false,

    /*
    |--------------------------------------------------------------------------
    | Image Processing
    |--------------------------------------------------------------------------
    |
    | Configure image processing options. Disable GD extension requirement
    | by setting enable_php to false and using alternative image handling.
    |
    */
    "enable_php" => false,
    "enable_javascript" => false,
    "enable_html5_parser" => true,

    /*
    |--------------------------------------------------------------------------
    | Font Settings
    |--------------------------------------------------------------------------
    |
    | Configure font handling and caching.
    |
    */
    "font_cache" => storage_path('fonts/'),
    "temp_dir" => sys_get_temp_dir(),

    /*
    |--------------------------------------------------------------------------
    | Rendering Options
    |--------------------------------------------------------------------------
    |
    | Configure PDF rendering behavior.
    |
    */
    "chroot" => realpath(base_path()),
    "enable_font_subsetting" => false,
    "pdf_backend" => "CPDF",
    "default_media_type" => "screen",
    "default_paper_size" => "a4",
    "default_paper_orientation" => "portrait",
    "default_font" => "serif",
    "dpi" => 96,
    "font_height_ratio" => 1.1,
    "is_php_enabled" => false,
    "is_javascript_enabled" => false,
    "is_remote_enabled" => false,
    "is_html5_parser_enabled" => true,

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security-related options.
    |
    */
    "allowed_protocols" => [
        "file://" => ["rules" => []],
        "http://" => ["rules" => []],
        "https://" => ["rules" => []]
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Handling
    |--------------------------------------------------------------------------
    |
    | Configure how images are handled when GD extension is not available.
    | We disable all image processing that requires GD extension.
    |
    */
    "enable_css_float" => false,
    "enable_inline_php" => false,

    /*
    |--------------------------------------------------------------------------
    | Additional Image Processing Disabling
    |--------------------------------------------------------------------------
    |
    | These options ensure no image processing that requires GD extension.
    |
    */
    "debugPng" => false,
    "debugKeepTemp" => false,
    "debugCss" => false,
    "debugLayout" => false,
    "debugLayoutLines" => false,
    "debugLayoutBlocks" => false,
    "debugLayoutInline" => false,
    "debugLayoutPaddingBox" => false,
];
