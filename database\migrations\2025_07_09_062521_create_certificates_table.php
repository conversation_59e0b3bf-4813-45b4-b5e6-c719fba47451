<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('certificates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('certificate_id')->unique(); // The public certificate ID (NGMB-XXXX-XXXX-YYYY or EXAM-XXXX-XXXX-YYYY)
            $table->uuid('user_id'); // Foreign key to users table
            $table->enum('type', ['course', 'exam']); // Certificate type

            // Polymorphic relationship to course or exam
            $table->uuid('certifiable_id'); // Course ID or Exam ID
            $table->string('certifiable_type'); // App\Models\Course or App\Models\Exam

            // Certificate details
            $table->string('user_name'); // User name at time of certificate generation
            $table->string('title'); // Course title or Exam title at time of generation
            $table->string('instructor_name'); // Tutor name at time of generation
            $table->timestamp('completion_date')->nullable(); // When the course/exam was completed
            $table->timestamp('issue_date')->nullable(); // When the certificate was issued

            // Additional data for verification
            $table->json('certificate_data')->nullable(); // Store additional certificate data (score, duration, etc.)

            // Status
            $table->boolean('is_active')->default(true); // Whether certificate is still valid
            $table->text('revocation_reason')->nullable(); // Reason if certificate is revoked
            $table->timestamp('revoked_at')->nullable(); // When certificate was revoked

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index('certificate_id');
            $table->index('user_id');
            $table->index('type');
            $table->index(['certifiable_id', 'certifiable_type']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('certificates');
    }
};
