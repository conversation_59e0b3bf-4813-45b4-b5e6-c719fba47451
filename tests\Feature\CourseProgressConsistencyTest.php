<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\LessonProgress;
use App\Models\User;
use App\Traits\CourseProgressTrait;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CourseProgressConsistencyTest extends TestCase
{
    use RefreshDatabase, CourseProgressTrait;

    public function test_progress_calculation_consistency_across_controllers()
    {
        // Create test data
        $user = User::factory()->create();
        $course = Course::factory()->create();
        
        // Create enrollment
        $enrollment = CourseEnrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'active',
            'enrolled_at' => now(),
        ]);

        // Test progress calculation using trait
        $progressData = $this->calculateCourseProgress($course, $user->id);
        
        $this->assertIsArray($progressData);
        $this->assertArrayHasKey('total_lessons', $progressData);
        $this->assertArrayHasKey('completed_lessons', $progressData);
        $this->assertArrayHasKey('progress_percentage', $progressData);
        $this->assertArrayHasKey('is_enrolled', $progressData);
        $this->assertTrue($progressData['is_enrolled']);
    }

    public function test_enrollment_status_detection_for_paid_courses()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create([
            'is_free' => false,
            'price' => 100000
        ]);
        
        // Test without enrollment
        $enrollmentStatus = $this->getCourseEnrollmentStatus($course, $user->id);
        $this->assertFalse($enrollmentStatus['is_enrolled']);
        
        // Create enrollment
        CourseEnrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'status' => 'active',
            'enrolled_at' => now(),
        ]);
        
        // Test with enrollment
        $enrollmentStatus = $this->getCourseEnrollmentStatus($course, $user->id);
        $this->assertTrue($enrollmentStatus['is_enrolled']);
        $this->assertTrue($enrollmentStatus['has_paid_enrollment']);
    }

    public function test_enrollment_status_detection_for_free_courses()
    {
        $user = User::factory()->create();
        $course = Course::factory()->create([
            'is_free' => true,
            'price' => 0
        ]);
        
        // For free courses, user should be considered enrolled even without enrollment record
        $enrollmentStatus = $this->getCourseEnrollmentStatus($course, $user->id);
        $this->assertTrue($enrollmentStatus['is_enrolled']);
        $this->assertFalse($enrollmentStatus['has_paid_enrollment']);
    }

    public function test_tutor_access_detection()
    {
        $tutor = User::factory()->create();
        $course = Course::factory()->create([
            'tutor_id' => $tutor->id
        ]);
        
        $enrollmentStatus = $this->getCourseEnrollmentStatus($course, $tutor->id);
        $this->assertTrue($enrollmentStatus['is_tutor']);
        $this->assertTrue($enrollmentStatus['can_access']);
    }
}