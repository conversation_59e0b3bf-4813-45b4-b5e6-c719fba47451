<?php

namespace Tests\Feature;

use App\Http\Controllers\TutorPublicProfileController;
use App\Models\Category;
use App\Models\Course;
use App\Models\Exam;
use App\Models\MembershipPlan;
use App\Models\Role;
use App\Models\TutorProfile;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NalaTutorProfileContextTest extends TestCase
{
    use RefreshDatabase;

    protected $tutor;
    protected $tutorProfile;
    protected $publishedCourse;
    protected $draftCourse;
    protected $publishedExam;
    protected $draftExam;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => Role::USER]);
        Role::create(['name' => Role::TUTOR]);
        
        // Create membership plan
        $membershipPlan = MembershipPlan::create([
            'name' => 'Free Plan',
            'slug' => 'free',
            'price' => 0,
            'duration_months' => 12,
            'nala_prompts' => 10,
        ]);

        // Create category
        $category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Programming courses',
        ]);

        // Create a tutor user
        $this->tutor = User::create([
            'name' => 'Sari Dewi',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'current_membership_id' => $membershipPlan->id,
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
        ]);
        $this->tutor->syncRoles([Role::USER, Role::TUTOR]);

        // Create tutor profile
        $this->tutorProfile = TutorProfile::create([
            'user_id' => $this->tutor->id,
            'public_name' => 'Sari Dewi',
            'public_name_slug' => 'sari-dewi',
            'job_title' => 'Senior Developer',
            'company' => 'Tech Company',
            'bio' => 'Experienced developer and educator',
            'status' => 'approved',
        ]);

        // Create published courses
        $this->publishedCourse = Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Laravel Advanced',
            'slug' => 'laravel-advanced',
            'description' => 'Advanced Laravel concepts',
            'level' => 'advanced',
            'price' => 150000,
            'status' => 'published',
            'published_at' => now()->subDays(5),
        ]);

        // Create more published courses
        Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Vue.js Fundamentals',
            'slug' => 'vuejs-fundamentals',
            'description' => 'Learn Vue.js from scratch',
            'level' => 'beginner',
            'price' => 100000,
            'status' => 'published',
            'published_at' => now()->subDays(3),
        ]);

        Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'React Native Development',
            'slug' => 'react-native-development',
            'description' => 'Build mobile apps with React Native',
            'level' => 'intermediate',
            'price' => 200000,
            'status' => 'published',
            'published_at' => now()->subDays(1),
        ]);

        // Create draft course
        $this->draftCourse = Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Node.js Backend',
            'slug' => 'nodejs-backend',
            'description' => 'Backend development with Node.js',
            'level' => 'intermediate',
            'price' => 120000,
            'status' => 'draft',
            'published_at' => null,
        ]);

        // Create published exam
        $this->publishedExam = Exam::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Laravel Certification',
            'slug' => 'laravel-certification',
            'description' => 'Test your Laravel knowledge',
            'difficulty_level' => 'intermediate',
            'price' => 50000,
            'is_published' => true,
        ]);

        // Create draft exam
        $this->draftExam = Exam::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Vue.js Assessment',
            'slug' => 'vuejs-assessment',
            'description' => 'Test your Vue.js skills',
            'difficulty_level' => 'beginner',
            'price' => 30000,
            'is_published' => false,
        ]);
    }

    public function test_tutor_profile_context_includes_accurate_course_publication_status()
    {
        $controller = new TutorPublicProfileController();
        $response = $controller->show('sari-dewi');
        
        // Get the view data
        $viewData = $response->getData();
        $courses = $viewData['courses'];
        $stats = $viewData['stats'];
        
        // Verify that we have 3 published courses
        $this->assertEquals(3, $courses->count());
        $this->assertEquals(3, $stats['total_courses']);
        
        // Verify all returned courses are published
        foreach ($courses as $course) {
            $this->assertEquals('published', $course->status);
            $this->assertNotNull($course->published_at);
        }
        
        // Verify draft course is not included in public profile
        $courseTitles = $courses->pluck('title')->toArray();
        $this->assertNotContains('Node.js Backend', $courseTitles);
        $this->assertContains('Laravel Advanced', $courseTitles);
        $this->assertContains('Vue.js Fundamentals', $courseTitles);
        $this->assertContains('React Native Development', $courseTitles);
    }

    public function test_tutor_profile_context_includes_accurate_exam_publication_status()
    {
        $controller = new TutorPublicProfileController();
        $response = $controller->show('sari-dewi');
        
        // Get the view data
        $viewData = $response->getData();
        $exams = $viewData['exams'];
        $stats = $viewData['stats'];
        
        // Verify that we have 1 published exam
        $this->assertEquals(1, $exams->count());
        $this->assertEquals(1, $stats['total_exams']);
        
        // Verify all returned exams are published
        foreach ($exams as $exam) {
            $this->assertTrue($exam->is_published);
        }
        
        // Verify draft exam is not included in public profile
        $examTitles = $exams->pluck('title')->toArray();
        $this->assertNotContains('Vue.js Assessment', $examTitles);
        $this->assertContains('Laravel Certification', $examTitles);
    }

    public function test_tutor_context_data_structure_for_nala_ai()
    {
        // This test simulates what would be available to Nala AI
        $controller = new TutorPublicProfileController();
        $response = $controller->show('sari-dewi');
        
        $viewData = $response->getData();
        $profile = $viewData['profile'];
        $courses = $viewData['courses'];
        $exams = $viewData['exams'];
        $stats = $viewData['stats'];
        
        // Simulate the tutorContext structure that would be passed to Nala
        $tutorContext = [
            'tutor' => [
                'id' => $profile->user_id,
                'name' => $profile->public_name,
                'slug' => $profile->public_name_slug,
            ],
            'stats' => $stats,
            'courses' => $courses->map(function($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'status' => $course->status,
                    'is_published' => $course->status === 'published',
                    'published_at' => $course->published_at ? $course->published_at->format('Y-m-d H:i:s') : null,
                ];
            })->toArray(),
            'exams' => $exams->map(function($exam) {
                return [
                    'id' => $exam->id,
                    'title' => $exam->title,
                    'is_published' => $exam->is_published,
                ];
            })->toArray(),
        ];
        
        // Verify Nala would receive accurate data
        $this->assertEquals('Sari Dewi', $tutorContext['tutor']['name']);
        $this->assertEquals(3, $tutorContext['stats']['total_courses']);
        $this->assertEquals(1, $tutorContext['stats']['total_exams']);
        
        // Verify all courses in context are published
        foreach ($tutorContext['courses'] as $course) {
            $this->assertEquals('published', $course['status']);
            $this->assertTrue($course['is_published']);
            $this->assertNotNull($course['published_at']);
        }
        
        // Verify all exams in context are published
        foreach ($tutorContext['exams'] as $exam) {
            $this->assertTrue($exam['is_published']);
        }
    }
}
