/**
 * Course Learning Page JavaScript
 * Professional interactive functionality following Udemy/Coursera patterns
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initSidebarToggle();
    initProgressAnimations();
    initSmoothScrolling();
    initKeyboardNavigation();
    initResponsiveHandling();
    initLessonTracking();
    
    console.log('Course Learning Page initialized successfully');
});

/**
 * Sidebar Toggle Functionality
 */
function initSidebarToggle() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebarClose = document.getElementById('sidebarClose');
    const sidebar = document.getElementById('curriculumSidebar');
    const overlay = document.getElementById('sidebarOverlay');
    
    if (!sidebarToggle || !sidebar || !overlay) return;
    
    // Toggle sidebar on mobile
    sidebarToggle.addEventListener('click', function() {
        openSidebar();
    });
    
    // Close sidebar
    if (sidebarClose) {
        sidebarClose.addEventListener('click', function() {
            closeSidebar();
        });
    }
    
    // Close sidebar when clicking overlay
    overlay.addEventListener('click', function() {
        closeSidebar();
    });
    
    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('show')) {
            closeSidebar();
        }
    });
    
    function openSidebar() {
        sidebar.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus management for accessibility
        const firstFocusable = sidebar.querySelector('button, a, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }
    
    function closeSidebar() {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
        
        // Return focus to toggle button
        sidebarToggle.focus();
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeSidebar();
        }
    });
}

/**
 * Progress Animations
 */
function initProgressAnimations() {
    const progressRings = document.querySelectorAll('.progress-ring-fill');
    const progressBars = document.querySelectorAll('.progress-fill');
    
    // Animate progress rings
    progressRings.forEach(ring => {
        const percentage = parseFloat(ring.style.strokeDashoffset) || 0;
        animateProgressRing(ring, percentage);
    });
    
    // Animate progress bars
    progressBars.forEach(bar => {
        const width = bar.style.width || '0%';
        animateProgressBar(bar, width);
    });
}

function animateProgressRing(ring, targetPercentage) {
    const radius = 25;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference * (1 - targetPercentage / 100);
    
    ring.style.strokeDasharray = circumference;
    ring.style.strokeDashoffset = circumference;
    
    setTimeout(() => {
        ring.style.transition = 'stroke-dashoffset 1s ease-in-out';
        ring.style.strokeDashoffset = offset;
    }, 500);
}

function animateProgressBar(bar, targetWidth) {
    bar.style.width = '0%';
    
    setTimeout(() => {
        bar.style.transition = 'width 1s ease-in-out';
        bar.style.width = targetWidth;
    }, 300);
}

/**
 * Smooth Scrolling
 */
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                e.preventDefault();
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Keyboard Navigation
 */
function initKeyboardNavigation() {
    const lessonItems = document.querySelectorAll('.lesson-item');
    
    lessonItems.forEach((item, index) => {
        item.addEventListener('keydown', function(e) {
            let targetIndex;
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    targetIndex = Math.min(index + 1, lessonItems.length - 1);
                    lessonItems[targetIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    targetIndex = Math.max(index - 1, 0);
                    lessonItems[targetIndex].focus();
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    lessonItems[0].focus();
                    break;
                    
                case 'End':
                    e.preventDefault();
                    lessonItems[lessonItems.length - 1].focus();
                    break;
                    
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    item.click();
                    break;
            }
        });
    });
}

/**
 * Responsive Handling
 */
function initResponsiveHandling() {
    let resizeTimer;
    
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            handleResponsiveChanges();
        }, 250);
    });
    
    // Initial call
    handleResponsiveChanges();
}

function handleResponsiveChanges() {
    const sidebar = document.getElementById('curriculumSidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (window.innerWidth >= 1024) {
        // Desktop: ensure sidebar is visible and main content is properly positioned
        if (sidebar) {
            sidebar.classList.remove('show');
        }
        document.body.style.overflow = '';
    } else {
        // Mobile: ensure proper mobile layout
        if (mainContent) {
            mainContent.style.marginLeft = '0';
        }
    }
}

/**
 * Lesson Tracking
 */
function initLessonTracking() {
    const lessonItems = document.querySelectorAll('.lesson-item');
    
    lessonItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            
            // Add visual feedback
            const icon = this.querySelector('.lesson-icon');
            if (icon) {
                icon.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            }
            
            // Track lesson click (you can add analytics here)
            trackLessonClick(this.href);
        });
    });
}

function trackLessonClick(lessonUrl) {
    // Add your analytics tracking here
    console.log('Lesson clicked:', lessonUrl);
    
    // Example: Send to analytics service
    // analytics.track('lesson_clicked', { url: lessonUrl });
}

/**
 * Utility Functions
 */

// Debounce function for performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Check if element is in viewport
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// Add fade-in animation to elements when they come into view
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // Observe elements that should animate
    const animateElements = document.querySelectorAll('.overview-card, .next-lesson-card, .completion-card');
    animateElements.forEach(el => observer.observe(el));
}

// Initialize scroll animations after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initScrollAnimations, 100);
});

// Handle focus trap for sidebar on mobile
function trapFocus(element) {
    const focusableElements = element.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];
    
    element.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            if (e.shiftKey) {
                if (document.activeElement === firstFocusable) {
                    e.preventDefault();
                    lastFocusable.focus();
                }
            } else {
                if (document.activeElement === lastFocusable) {
                    e.preventDefault();
                    firstFocusable.focus();
                }
            }
        }
    });
}
