<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\CurriculumController;
use App\Http\Controllers\CurriculumV2Controller;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserProfileController;
use App\Http\Controllers\TutorController;
use App\Http\Controllers\TutorRegistrationController;
use App\Http\Controllers\CourseEnrollmentController;
use App\Http\Controllers\CourseLearningController;
use App\Http\Controllers\TutorPublicProfileController;
use App\Http\Controllers\TutorListingController;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\Tutor\CurriculumController as TutorCurriculumController;
use App\Http\Controllers\Tutor\ExamController as TutorExamController;
use App\Http\Controllers\Tutor\BlogController as TutorBlogController;
use App\Http\Controllers\Admin\TutorApplicationController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\StaticPageController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::get('/', [HomeController::class, 'index'])->name('home');

// Static pages
Route::get('/syarat-ketentuan', [StaticPageController::class, 'termsConditions'])->name('terms-conditions');
Route::get('/kebijakan-privasi', [StaticPageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/about-us', [StaticPageController::class, 'aboutUs'])->name('about-us');

// Courses routes (public) - V2 with enhanced design
Route::get('/courses', [CurriculumV2Controller::class, 'index'])->name('courses.index');
Route::get('/courses/{course}', [CurriculumController::class, 'show'])->name('course.show');

// Legacy redirect for old curriculum route
Route::get('/kurikulum', function() {
    return redirect()->route('courses.index', [], 301);
});

// Course Learning routes (protected by auth)
Route::middleware(['auth'])->group(function () {
    Route::post('/courses/{course}/enroll', [CourseEnrollmentController::class, 'enroll'])->name('course.enroll');
    Route::get('/courses/{course}/learn', [CourseLearningController::class, 'index'])->name('course.learn');
    Route::get('/courses/{course}/learn/{lesson}', [CourseLearningController::class, 'lesson'])->name('course.lesson');
    Route::post('/courses/{course}/lessons/{lesson}/progress', [CourseLearningController::class, 'updateProgress'])->name('course.lesson.progress');

    // Certificate routes
    Route::get('/courses/{course}/certificate/download', [CertificateController::class, 'downloadCourseCertificate'])->name('course.certificate.download');
    Route::get('/courses/{course}/certificate/preview', [CertificateController::class, 'previewCourseCertificate'])->name('course.certificate.preview');

    // Exam Certificate routes
    Route::get('/exams/{exam}/certificate/download', [CertificateController::class, 'downloadExamCertificate'])->name('exam.certificate.download');
    Route::get('/exams/{exam}/certificate/preview', [CertificateController::class, 'previewExamCertificate'])->name('exam.certificate.preview');
});

// Public Certificate Verification Route (no authentication required)
Route::get('/verify/{certificate_id}', [CertificateController::class, 'verify'])->name('certificate.verify');



// User Dashboard routes (protected by auth middleware)
Route::middleware('auth')->prefix('user')->name('user.')->group(function () {
    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');
    Route::get('/profile', [UserProfileController::class, 'show'])->name('profile');
    Route::put('/profile', [UserProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/picture', [UserProfileController::class, 'updateProfilePicture'])->name('profile.picture.update');
    Route::delete('/profile/picture', [UserProfileController::class, 'deleteProfilePicture'])->name('profile.delete-picture');
    Route::get('/change-password', [UserProfileController::class, 'showChangePassword'])->name('change-password');
    Route::put('/change-password', [UserProfileController::class, 'updatePassword'])->name('change-password.update');
    Route::get('/courses', [UserController::class, 'courses'])->name('courses');
    Route::get('/exams', [UserController::class, 'exams'])->name('exams');
    Route::get('/blog', [UserController::class, 'blog'])->name('blog');
    // Route::get('/progress', [UserController::class, 'progress'])->name('progress'); // Commented out due to functionality overlap with user dashboard and courses page
    Route::get('/certificates', [UserController::class, 'certificates'])->name('certificates');
    Route::get('/settings', [UserController::class, 'settings'])->name('user.settings');
    Route::put('/settings', [UserController::class, 'updateSettings'])->name('user.settings.update');
    Route::get('/membership', [PaymentController::class, 'userMembership'])->name('membership');
});

// Payment routes
Route::get('/pricing', [PaymentController::class, 'pricing'])->name('payment.pricing');


Route::middleware('auth')->prefix('payment')->name('payment.')->group(function () {
    // NALA Membership
    Route::get('/membership/{membershipPlan}/checkout', [PaymentController::class, 'membershipCheckout'])->name('membership.checkout');
    Route::post('/membership/{membershipPlan}/process', [PaymentController::class, 'processMembershipPayment'])->name('membership.process');

    // Course Purchase
    Route::get('/course/{course}/checkout', [PaymentController::class, 'courseCheckout'])->name('course.checkout');
    Route::post('/course/{course}/process', [PaymentController::class, 'processCoursePayment'])->name('course.process');
});

// Tutor Registration redirect route
Route::middleware('auth')->get('/tutor/register', function () {
    return redirect()->route('tutor.register.terms');
});

// Tutor Registration routes (protected by auth middleware)
Route::middleware('auth')->prefix('tutor/register')->name('tutor.register.')->group(function () {
    Route::get('/terms', [TutorRegistrationController::class, 'showTerms'])->name('terms');
    Route::post('/terms', [TutorRegistrationController::class, 'processTerms'])->name('terms.process');
    Route::get('/profile', [TutorRegistrationController::class, 'showProfile'])->name('profile');
    Route::post('/profile', [TutorRegistrationController::class, 'processProfile'])->name('profile.process');
    Route::get('/review', [TutorRegistrationController::class, 'showReview'])->name('review');
    Route::post('/submit', [TutorRegistrationController::class, 'submitApplication'])->name('submit');
    Route::get('/status', [TutorRegistrationController::class, 'showStatus'])->name('status');
});

// Tutor Dashboard routes (protected by auth and is.tutor middleware)
Route::middleware(['auth', 'is.tutor'])->prefix('tutor')->name('tutor.')->group(function () {
    Route::get('/dashboard', [TutorController::class, 'dashboard'])->name('dashboard');
    Route::get('/courses', [TutorController::class, 'courses'])->name('courses');
    Route::get('/create-course', [TutorController::class, 'createCourse'])->name('create-course');
    Route::post('/create-course', [TutorController::class, 'storeCourse'])->name('store-course');
    Route::get('/membership', [TutorController::class, 'membership'])->name('membership');

    // AI Course Builder routes
    Route::post('/ai-course-builder/generate', [App\Http\Controllers\Tutor\AICourseBuilderController::class, 'generateCourseSuggestion'])->name('ai-course-builder.generate');

    // AI Material Builder routes
    Route::post('/courses/{course}/chapters/{chapter}/ai-material-builder/generate', [App\Http\Controllers\Tutor\AICourseBuilderController::class, 'generateMaterialSuggestion'])->name('ai-material-builder.generate');
    Route::get('/courses/{course}/edit', [TutorController::class, 'editCourse'])->name('edit-course');
    Route::put('/courses/{course}', [TutorController::class, 'updateCourse'])->name('update-course');

    // Curriculum management routes
    Route::get('/courses/{course}/curriculum', [TutorCurriculumController::class, 'index'])->name('curriculum.index');
    Route::post('/courses/{course}/chapters', [TutorCurriculumController::class, 'storeChapter'])->name('curriculum.store-chapter');
    Route::get('/courses/{course}/chapters/{chapter}/materials/create', [TutorCurriculumController::class, 'createMaterial'])->name('curriculum.create-material');
    Route::post('/courses/{course}/chapters/{chapter}/lessons', [TutorCurriculumController::class, 'storeLesson'])->name('curriculum.store-lesson');
    Route::get('/courses/{course}/chapters/{chapter}/materials/{lesson}/edit', [TutorCurriculumController::class, 'editMaterial'])->name('curriculum.edit-material');
    Route::post('/courses/{course}/upload-video', [TutorCurriculumController::class, 'uploadVideo'])->name('curriculum.upload-video');
    Route::put('/courses/{course}/chapters/{chapter}', [TutorCurriculumController::class, 'updateChapter'])->name('curriculum.update-chapter');
    Route::put('/courses/{course}/chapters/{chapter}/lessons/{lesson}', [TutorCurriculumController::class, 'updateLesson'])->name('curriculum.update-lesson');
    Route::post('/courses/{course}/chapters/{chapter}/toggle-publish', [TutorCurriculumController::class, 'togglePublishChapter'])->name('curriculum.toggle-publish-chapter');
    Route::delete('/courses/{course}/chapters/{chapter}', [TutorCurriculumController::class, 'deleteChapter'])->name('curriculum.delete-chapter');
    Route::delete('/courses/{course}/chapters/{chapter}/lessons/{lesson}', [TutorCurriculumController::class, 'deleteLesson'])->name('curriculum.delete-lesson');
    Route::get('/quiz-template/download', [TutorCurriculumController::class, 'downloadQuizTemplate'])->name('curriculum.download-quiz-template');

    // Exam management routes
    Route::get('/exams', [TutorExamController::class, 'index'])->name('exams');
    Route::get('/exams/create', [TutorExamController::class, 'create'])->name('exams.create');
    Route::post('/exams', [TutorExamController::class, 'store'])->name('exams.store');
    Route::get('/exams/{exam}', [TutorExamController::class, 'show'])->name('exams.show');
    Route::get('/exams/{exam}/edit', [TutorExamController::class, 'edit'])->name('exams.edit');
    Route::put('/exams/{exam}', [TutorExamController::class, 'update'])->name('exams.update');
    Route::delete('/exams/{exam}', [TutorExamController::class, 'destroy'])->name('exams.destroy');
    Route::post('/exams/{exam}/toggle-publish', [TutorExamController::class, 'togglePublish'])->name('exams.toggle-publish');
    Route::get('/exam-template/download', [TutorExamController::class, 'downloadQuestionTemplate'])->name('exams.download-template');

    // Blog management routes
    Route::get('/blogs', [TutorBlogController::class, 'index'])->name('blogs');
    Route::get('/blogs/create', [TutorBlogController::class, 'create'])->name('blogs.create');
    Route::post('/blogs', [TutorBlogController::class, 'store'])->name('blogs.store');
    Route::get('/blogs/{blogPost}', [TutorBlogController::class, 'show'])->name('blogs.show');
    Route::get('/blogs/{blogPost}/edit', [TutorBlogController::class, 'edit'])->name('blogs.edit');
    Route::put('/blogs/{blogPost}', [TutorBlogController::class, 'update'])->name('blogs.update');
    Route::delete('/blogs/{blogPost}', [TutorBlogController::class, 'destroy'])->name('blogs.destroy');
    Route::post('/blogs/{blogPost}/toggle-publish', [TutorBlogController::class, 'togglePublish'])->name('blogs.toggle-publish');

    // Course publishing routes
    Route::get('/courses/{course}/publish', [TutorController::class, 'showPublishPage'])->name('courses.publish-page');
    Route::post('/courses/{course}/publish', [TutorController::class, 'publishCourse'])->name('courses.publish');
    Route::post('/courses/{course}/save-draft', [TutorController::class, 'saveDraft'])->name('courses.save-draft');
    Route::get('/courses/{course}/preview', [TutorController::class, 'previewCourse'])->name('courses.preview');

    Route::get('/students', [TutorController::class, 'students'])->name('students');
    Route::get('/analytics', [TutorController::class, 'analytics'])->name('analytics');
    Route::get('/earnings', [TutorController::class, 'earnings'])->name('earnings');

    // Payout routes
    Route::post('/request-payout', [TutorController::class, 'requestPayout'])->name('request-payout');
    Route::get('/payout-data', [TutorController::class, 'getPayoutData'])->name('payout-data');
    Route::get('/payout-history', [TutorController::class, 'getPayoutHistory'])->name('payout-history');

    // Payment settings routes
    Route::post('/payment-settings', [TutorController::class, 'updatePaymentSettings'])->name('payment-settings.update');
    Route::get('/payment-settings', [TutorController::class, 'getPaymentSettings'])->name('payment-settings.get');

    Route::get('/profile', [TutorController::class, 'profile'])->name('profile');
    Route::put('/profile', [TutorController::class, 'updateProfile'])->name('profile.update');
    Route::get('/settings', [TutorController::class, 'settings'])->name('settings');
});

// Secure File Access Routes
Route::middleware(['auth'])->group(function () {
    // Course lesson files
    Route::get('/secure/courses/{course}/lessons/{lesson}/video', [App\Http\Controllers\SecureFileController::class, 'serveVideo'])->name('secure.lesson.video');
    Route::get('/secure/courses/{course}/lessons/{lesson}/material/{filename}', [App\Http\Controllers\SecureFileController::class, 'serveMaterial'])->name('secure.lesson.material');

    // Tutor private files (KTP, Portfolio, NPWP)
    Route::get('/secure/tutor/{tutorId}/{type}/{filename}', [App\Http\Controllers\SecureFileController::class, 'serveTutorFile'])
        ->name('secure.tutor.file')
        ->where(['type' => 'ktp|portfolio|npwp']);
    Route::get('/secure/tutor/{tutorId}/{type}/{filename}/download', [App\Http\Controllers\SecureFileController::class, 'downloadTutorFile'])
        ->name('secure.tutor.file.download')
        ->where(['type' => 'ktp|portfolio|npwp']);
});

// Admin routes (protected by auth and is.admin middleware)
Route::middleware(['auth', 'is.admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/tutor-applications', [TutorApplicationController::class, 'index'])->name('tutor-applications.index');
    Route::get('/tutor-applications/{application}', [TutorApplicationController::class, 'show'])->name('tutor-applications.show');
    Route::post('/tutor-applications/{application}/review', [TutorApplicationController::class, 'review'])->name('tutor-applications.review');
    Route::post('/tutor-applications/{application}/approve', [TutorApplicationController::class, 'approve'])->name('tutor-applications.approve');
    Route::post('/tutor-applications/{application}/reject', [TutorApplicationController::class, 'reject'])->name('tutor-applications.reject');
    Route::get('/tutor-applications/{application}/download/{type}', [TutorApplicationController::class, 'downloadFile'])->name('tutor-applications.download');

    // Payout management routes
    Route::get('/payouts', [App\Http\Controllers\Admin\PayoutController::class, 'index'])->name('payouts.index');
    Route::get('/payouts/{payoutRequest}', [App\Http\Controllers\Admin\PayoutController::class, 'show'])->name('payouts.show');
    Route::get('/payouts/{payoutRequest}/data', [App\Http\Controllers\Admin\PayoutController::class, 'getData'])->name('payouts.data');
    Route::post('/payouts/{payoutRequest}/status', [App\Http\Controllers\Admin\PayoutController::class, 'updateStatus'])->name('payouts.update-status');
    Route::get('/payouts/export', [App\Http\Controllers\Admin\PayoutController::class, 'export'])->name('payouts.export');
});

// Redirect old dashboard route to new structure
Route::get('/dashboard', function () {
    return redirect()->route('user.dashboard');
})->middleware('auth')->name('dashboard');

// Public Exam Routes (V2 - Professional)
Route::get('/exams', [App\Http\Controllers\ExamV2Controller::class, 'index'])->name('exams.index');
Route::get('/exams/{exam}', [App\Http\Controllers\ExamV2Controller::class, 'show'])->name('exams.show');

// Test route to check available exams (for debugging)
Route::get('/test-exams', function () {
    $exams = \App\Models\Exam::select('id', 'title', 'is_published')->get();
    return response()->json($exams);
});

// Public Blog Routes
Route::get('/blog', [App\Http\Controllers\BlogController::class, 'index'])->name('blog.index');
Route::get('/blog/{blogPost}', [App\Http\Controllers\BlogController::class, 'show'])->name('blog.show');
Route::get('/blog/category/{category}', [App\Http\Controllers\BlogController::class, 'category'])->name('blog.category');

// Authenticated Exam Routes (Original) - COMMENTED OUT FOR TESTING V2
// Route::middleware('auth')->group(function () {
//     Route::post('/exams/{exam}/enroll', [App\Http\Controllers\ExamController::class, 'enroll'])->name('exams.enroll');
//     Route::get('/exams/{exam}/take', [App\Http\Controllers\ExamController::class, 'take'])->name('exams.take');
//     Route::post('/exams/{exam}/save-answer', [App\Http\Controllers\ExamController::class, 'saveAnswer'])->name('exams.save-answer');
//     Route::post('/exams/{exam}/submit', [App\Http\Controllers\ExamController::class, 'submit'])->name('exams.submit');
//     Route::get('/exams/{exam}/result/{attempt}', [App\Http\Controllers\ExamController::class, 'result'])->name('exams.result');
// });

// Professional Exam Routes (V2) - NOW USING MAIN EXAM ROUTES
Route::middleware('auth')->group(function () {
    Route::post('/exams/{exam}/enroll', [App\Http\Controllers\ExamV2Controller::class, 'enroll'])->name('exams.enroll');
    Route::get('/exams/{exam}/take', [App\Http\Controllers\ExamV2Controller::class, 'take'])->name('exams.take');
    Route::post('/exams/{exam}/auto-save', [App\Http\Controllers\ExamV2Controller::class, 'autoSave'])->name('exams.auto-save');
    Route::post('/exams/{exam}/submit', [App\Http\Controllers\ExamV2Controller::class, 'submit'])->name('exams.submit');
    Route::get('/exams/{exam}/result/{attempt}', [App\Http\Controllers\ExamV2Controller::class, 'result'])->name('exams.result');

    // Saved Articles Routes
    Route::post('/blog/{blogPost}/save', [App\Http\Controllers\SavedArticleController::class, 'store'])->name('articles.save');
    Route::delete('/blog/{blogPost}/save', [App\Http\Controllers\SavedArticleController::class, 'destroy'])->name('articles.unsave');
    Route::get('/blog/{blogPost}/check-saved', [App\Http\Controllers\SavedArticleController::class, 'check'])->name('articles.check');
});

// Public tutor listing route
Route::get('/tutors', [TutorListingController::class, 'index'])->name('tutors.index');

// Public tutor profile route (must be last to avoid conflicts with specific routes)
Route::get('/tutor/{slug}', [TutorPublicProfileController::class, 'show'])->name('tutor.public-profile');

