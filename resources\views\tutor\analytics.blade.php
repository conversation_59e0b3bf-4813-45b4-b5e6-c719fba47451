@extends('layouts.tutor')

@section('title', 'Analitik - Tutor Dashboard')

@section('content')
<div class="tutor-dashboard-container tutor-analytics-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Page Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mb-2">
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-0">Analitik Kursus</h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 w-fit">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Analytics Hub
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">Pantau performa dan engagement kursus Anda</p>
            </div>
            <div class="tutor-header-actions flex flex-col md:flex-row md:items-center md:space-x-3 gap-3">
                <a href="{{ route('tutor.dashboard') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-emerald-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Kursus</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">{{ $analyticsData['summary_stats']['total_courses'] }}</p>
                    <div class="flex items-center mt-1 text-xs text-emerald-600">
                        <span>{{ $analyticsData['summary_stats']['published_courses'] }} dipublikasi</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-teal-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Siswa</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">{{ $analyticsData['student_engagement']['total_students'] }}</p>
                    <div class="flex items-center mt-1 text-xs text-teal-600">
                        <span>{{ $analyticsData['student_engagement']['active_students'] }} aktif</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-amber-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Completion Rate</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">{{ $analyticsData['student_engagement']['completion_rate'] }}%</p>
                    <div class="flex items-center mt-1 text-xs text-amber-600">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Tingkat penyelesaian kursus</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-green-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Avg Rating</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">{{ $analyticsData['summary_stats']['average_rating'] > 0 ? number_format($analyticsData['summary_stats']['average_rating'], 1) : '-' }}</p>
                    <div class="flex items-center mt-1 text-xs text-gray-500">
                        <span>Dari {{ $analyticsData['summary_stats']['total_enrollments'] }} pendaftaran</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <h2 class="text-base md:text-lg font-semibold text-gray-900">Filter Periode</h2>
            <div class="flex flex-wrap gap-2">
                <button onclick="filterAnalytics(3)" class="filter-btn {{ ($months ?? 6) == 3 ? 'active border-emerald-500 bg-emerald-50 text-emerald-700' : 'border-gray-300 bg-white text-gray-700' }} px-3 py-2 text-sm font-medium rounded-lg border hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    3 Bulan
                </button>
                <button onclick="filterAnalytics(6)" class="filter-btn {{ ($months ?? 6) == 6 ? 'active border-emerald-500 bg-emerald-50 text-emerald-700' : 'border-gray-300 bg-white text-gray-700' }} px-3 py-2 text-sm font-medium rounded-lg border hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    6 Bulan
                </button>
                <button onclick="filterAnalytics(12)" class="filter-btn {{ ($months ?? 6) == 12 ? 'active border-emerald-500 bg-emerald-50 text-emerald-700' : 'border-gray-300 bg-white text-gray-700' }} px-3 py-2 text-sm font-medium rounded-lg border hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    1 Tahun
                </button>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="tutor-tablet-two-col grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6 md:mb-8">
        <!-- Enrollment Trends -->
        <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-base md:text-lg font-semibold text-gray-900">Tren Pendaftaran</h2>
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Total: {{ array_sum(array_column($analyticsData['enrollment_trends'], 'enrollments')) }} siswa
                </div>
            </div>
            @if(array_sum(array_column($analyticsData['enrollment_trends'], 'enrollments')) > 0)
                <div class="chart-container" style="height: 300px;">
                    <canvas id="enrollmentChart"></canvas>
                </div>
            @else
                <div class="chart-container text-center py-6 md:py-8">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-3 md:mb-4">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm md:text-base font-medium text-gray-900 mb-2">Belum Ada Data</h3>
                    <p class="text-gray-600 text-xs md:text-sm">Data akan muncul setelah ada siswa yang mendaftar</p>
                </div>
            @endif
        </div>

        <!-- Revenue Analytics -->
        <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-base md:text-lg font-semibold text-gray-900">Analitik Pendapatan</h2>
                <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Total: Rp {{ number_format(array_sum(array_map(function($item) { return is_numeric($item['revenue']) ? $item['revenue'] : 0; }, $analyticsData['revenue_analytics'])), 0, ',', '.') }}
                </div>
            </div>
            @if(array_sum(array_map(function($item) { return is_numeric($item['revenue']) ? $item['revenue'] : 0; }, $analyticsData['revenue_analytics'])) > 0)
                <div class="chart-container" style="height: 300px;">
                    <canvas id="revenueChart"></canvas>
                </div>
            @else
                <div class="chart-container text-center py-6 md:py-8">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3 md:mb-4">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm md:text-base font-medium text-gray-900 mb-2">Belum Ada Pendapatan</h3>
                    <p class="text-gray-600 text-xs md:text-sm">Mulai dengan membuat kursus pertama Anda</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Course Performance -->
    <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 mb-6 md:mb-8">
        <h2 class="text-base md:text-lg font-semibold text-gray-900 mb-4">Performa Kursus</h2>
        @if($analyticsData['course_performance']->count() > 0)
            <!-- Mobile Card View -->
            <div class="block md:hidden space-y-4">
                @foreach($analyticsData['course_performance'] as $course)
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-3">
                        <h3 class="font-medium text-gray-900 text-sm">{{ $course['title'] }}</h3>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {{ $course['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ $course['status'] === 'published' ? 'Dipublikasi' : 'Draft' }}
                        </span>
                    </div>
                    <div class="grid grid-cols-2 gap-3 text-xs">
                        <div>
                            <span class="text-gray-500">Siswa:</span>
                            <span class="font-medium text-gray-900">{{ $course['total_students'] }}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Rating:</span>
                            <div class="flex items-center">
                                <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                <span class="font-medium text-gray-900">{{ $course['average_rating'] > 0 ? number_format($course['average_rating'], 1) : '-' }}</span>
                            </div>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-500">Pendapatan:</span>
                            <span class="font-medium text-gray-900">Rp {{ number_format($course['revenue'], 0, ',', '.') }}</span>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Desktop Table View -->
            <div class="hidden md:block overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kursus</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Siswa</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rating</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pendapatan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($analyticsData['course_performance'] as $course)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $course['title'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $course['total_students'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-900">{{ $course['average_rating'] > 0 ? number_format($course['average_rating'], 1) : '-' }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Rp {{ number_format($course['revenue'], 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {{ $course['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                    {{ $course['status'] === 'published' ? 'Dipublikasi' : 'Draft' }}
                                </span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Belum Ada Kursus</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto text-sm">
                    Buat kursus pertama Anda untuk melihat analitik performa yang detail
                </p>
                <a href="{{ route('tutor.create-course') }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Buat Kursus Pertama
                </a>
            </div>
        @endif
    </div>

        <!-- Student Engagement -->
        <div class="bg-white rounded-xl shadow-sm p-4 md:p-8">
            <h2 class="text-lg md:text-xl font-bold text-gray-900 mb-4 md:mb-6">Engagement Siswa</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                <div class="text-center p-4 md:p-6 border border-gray-200 rounded-lg">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm md:text-base font-semibold text-gray-900">Waktu Belajar Rata-rata</h3>
                    <p class="text-xl md:text-2xl font-bold text-blue-600 mt-2">{{ $analyticsData['student_engagement']['avg_study_time_minutes'] }} menit</p>
                    <p class="text-xs md:text-sm text-gray-500 mt-1">Per sesi</p>
                </div>

                <div class="text-center p-4 md:p-6 border border-gray-200 rounded-lg">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm md:text-base font-semibold text-gray-900">Quiz Success Rate</h3>
                    <p class="text-xl md:text-2xl font-bold text-green-600 mt-2">{{ $analyticsData['student_engagement']['quiz_success_rate'] }}%</p>
                    <p class="text-xs md:text-sm text-gray-500 mt-1">Rata-rata skor</p>
                </div>

                <div class="text-center p-4 md:p-6 border border-gray-200 rounded-lg">
                    <div class="w-10 h-10 md:w-12 md:h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <svg class="w-5 h-5 md:w-6 md:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-sm md:text-base font-semibold text-gray-900">Diskusi Aktif</h3>
                    <p class="text-xl md:text-2xl font-bold text-purple-600 mt-2">{{ $analyticsData['student_engagement']['active_discussions'] }}</p>
                    <p class="text-xs md:text-sm text-gray-500 mt-1">Pesan bulan ini</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Analytics data from PHP
const analyticsData = @json($analyticsData);

// Chart instances
let enrollmentChart = null;
let revenueChart = null;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Initialize enrollment chart if data exists
    const totalEnrollments = analyticsData.enrollment_trends.reduce((sum, item) => sum + item.enrollments, 0);
    if (totalEnrollments > 0) {
        initEnrollmentChart();
    }

    // Initialize revenue chart if data exists
    const totalRevenue = analyticsData.revenue_analytics.reduce((sum, item) => {
        const revenue = typeof item.revenue === 'string' ? parseFloat(item.revenue) : item.revenue;
        return sum + (isNaN(revenue) ? 0 : revenue);
    }, 0);
    if (totalRevenue > 0) {
        initRevenueChart();
    }
}

function initEnrollmentChart() {
    const ctx = document.getElementById('enrollmentChart');
    if (!ctx) return;

    const labels = analyticsData.enrollment_trends.map(item => item.month);
    const data = analyticsData.enrollment_trends.map(item => item.enrollments);

    if (enrollmentChart) {
        enrollmentChart.destroy();
    }

    enrollmentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Pendaftaran',
                data: data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#10b981',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#10b981',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y + ' siswa';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        color: '#6b7280'
                    },
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#6b7280'
                    },
                    grid: {
                        display: false
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

function initRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;

    const labels = analyticsData.revenue_analytics.map(item => item.month);
    const data = analyticsData.revenue_analytics.map(item => {
        const revenue = typeof item.revenue === 'string' ? parseFloat(item.revenue) : item.revenue;
        return isNaN(revenue) ? 0 : revenue;
    });

    if (revenueChart) {
        revenueChart.destroy();
    }

    revenueChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Pendapatan',
                data: data,
                backgroundColor: 'rgba(20, 184, 166, 0.8)',
                borderColor: '#14b8a6',
                borderWidth: 1,
                borderRadius: 6,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#14b8a6',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID').format(context.parsed.y);
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#6b7280',
                        callback: function(value) {
                            return 'Rp ' + new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value);
                        }
                    },
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#6b7280'
                    },
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Filter functionality with AJAX
function filterAnalytics(months) {
    // Update active button
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active', 'border-emerald-500', 'bg-emerald-50', 'text-emerald-700');
        btn.classList.add('border-gray-300', 'bg-white', 'text-gray-700');
    });

    event.target.classList.add('active', 'border-emerald-500', 'bg-emerald-50', 'text-emerald-700');
    event.target.classList.remove('border-gray-300', 'bg-white', 'text-gray-700');

    // Show loading state
    showLoadingState();

    // Make AJAX request to fetch filtered data
    fetch(`{{ route('tutor.analytics') }}?months=${months}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update analytics data
        analyticsData.enrollment_trends = data.enrollment_trends;
        analyticsData.revenue_analytics = data.revenue_analytics;

        // Update totals in headers
        updateChartHeaders();

        // Reinitialize charts with new data
        initializeCharts();

        // Hide loading state
        hideLoadingState();
    })
    .catch(error => {
        console.error('Error fetching analytics data:', error);
        hideLoadingState();

        // Show error message
        showErrorMessage('Gagal memuat data analitik. Silakan coba lagi.');
    });
}

function showLoadingState() {
    // Add loading overlay to charts
    const chartContainers = document.querySelectorAll('.chart-container canvas');
    chartContainers.forEach(container => {
        container.style.opacity = '0.5';
    });
}

function hideLoadingState() {
    // Remove loading overlay from charts
    const chartContainers = document.querySelectorAll('.chart-container canvas');
    chartContainers.forEach(container => {
        container.style.opacity = '1';
    });
}

function updateChartHeaders() {
    // Update enrollment total
    const enrollmentTotal = analyticsData.enrollment_trends.reduce((sum, item) => sum + item.enrollments, 0);
    const enrollmentHeader = document.querySelector('.chart-container').parentElement.querySelector('.text-sm.text-gray-500');
    if (enrollmentHeader) {
        enrollmentHeader.innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Total: ${enrollmentTotal} siswa
        `;
    }

    // Update revenue total
    const revenueTotal = analyticsData.revenue_analytics.reduce((sum, item) => {
        const revenue = typeof item.revenue === 'string' ? parseFloat(item.revenue) : item.revenue;
        return sum + (isNaN(revenue) ? 0 : revenue);
    }, 0);

    const revenueHeaders = document.querySelectorAll('.text-sm.text-gray-500');
    if (revenueHeaders.length > 1) {
        revenueHeaders[1].innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            Total: Rp ${new Intl.NumberFormat('id-ID').format(revenueTotal)}
        `;
    }
}

function showErrorMessage(message) {
    // Create and show error toast
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>
@endpush
