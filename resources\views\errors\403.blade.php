@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full space-y-8">
        <!-- Error Icon -->
        <div class="text-center">
            <div class="mx-auto h-24 w-24 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg class="h-12 w-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-4a2 2 0 00-2-2H6a2 2 0 00-2 2v4a2 2 0 002 2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9V7a4 4 0 00-8 0v2"></path>
                </svg>
            </div>
            
            <!-- Error Code -->
            <h1 class="text-6xl font-bold text-gray-900 mb-2">403</h1>
            
            <!-- Error Title -->
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Akses Ditolak</h2>
            
            <!-- Error Message -->
            <p class="text-gray-600 mb-8 leading-relaxed">
                Maaf, Anda tidak memiliki izin untuk mengakses halaman ini. 
                Halaman yang Anda coba akses khusus untuk tutor yang telah terdaftar.
            </p>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            @auth
                <!-- For authenticated users -->
                <div class="text-center mb-6">
                    <p class="text-sm text-gray-500 mb-4">
                        Halo, {{ Auth::user()->name }}! Untuk mengakses area tutor, Anda perlu mendaftar sebagai tutor terlebih dahulu.
                    </p>
                </div>
                
                <!-- Primary CTA - Become a Tutor -->
                <a href="{{ route('tutor.register.terms') }}" 
                   class="w-full flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Daftar Menjadi Tutor
                </a>
                
                <!-- Secondary CTA - Go to Dashboard -->
                <a href="{{ route('user.dashboard') }}" 
                   class="w-full flex justify-center items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                    </svg>
                    Kembali ke Dashboard Saya
                </a>
            @else
                <!-- For guest users -->
                <div class="text-center mb-6">
                    <p class="text-sm text-gray-500 mb-4">
                        Anda perlu login terlebih dahulu untuk mengakses area tutor.
                    </p>
                </div>
                
                <!-- Primary CTA - Login -->
                <a href="{{ route('login') }}" 
                   class="w-full flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Login untuk Melanjutkan
                </a>
                
                <!-- Secondary CTA - Register -->
                <a href="{{ route('register') }}" 
                   class="w-full flex justify-center items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Daftar Akun Baru
                </a>
            @endauth
            
            <!-- Tertiary CTA - Back to Home -->
            <a href="{{ route('home') }}" 
               class="w-full flex justify-center items-center px-6 py-3 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali ke Beranda
            </a>
        </div>

        <!-- Additional Information -->
        <div class="mt-8 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-orange-800">
                        Ingin Menjadi Tutor?
                    </h3>
                    <div class="mt-2 text-sm text-orange-700">
                        <p>
                            Bergabunglah dengan ribuan tutor di Ngambiskuy dan mulai berbagi ilmu Anda. 
                            Proses pendaftaran mudah dan cepat!
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .bg-primary {
        background-color: #FF6B35;
    }
    .bg-primary-dark {
        background-color: #E55A2B;
    }
    .hover\:bg-primary-dark:hover {
        background-color: #E55A2B;
    }
    .focus\:ring-primary:focus {
        --tw-ring-color: #FF6B35;
    }
    .border-primary {
        border-color: #FF6B35;
    }
</style>
@endpush
@endsection
