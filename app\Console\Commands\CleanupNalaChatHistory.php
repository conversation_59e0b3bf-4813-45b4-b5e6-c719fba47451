<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use Illuminate\Support\Facades\Log;

class CleanupNalaChatHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nala:cleanup-chat-history 
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--days=30 : Number of days to keep deleted conversations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old Nala chat conversations and messages';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $daysToKeep = (int) $this->option('days');
        
        $this->info("Starting Nala chat history cleanup...");
        $this->info("Days to keep deleted conversations: {$daysToKeep}");
        
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No data will be deleted");
        }

        // Clean up old deleted conversations
        $this->cleanupDeletedConversations($daysToKeep, $dryRun);
        
        // Clean up orphaned messages
        $this->cleanupOrphanedMessages($dryRun);
        
        // Optimize active conversations (maintain message limits)
        $this->optimizeActiveConversations($dryRun);

        $this->info("Cleanup completed!");
    }

    /**
     * Clean up conversations that have been deleted for more than specified days
     */
    private function cleanupDeletedConversations(int $daysToKeep, bool $dryRun)
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $query = NalaChatConversation::where('status', 'deleted')
            ->where('updated_at', '<', $cutoffDate);
            
        $count = $query->count();
        
        if ($count > 0) {
            $this->info("Found {$count} old deleted conversations to permanently remove");
            
            if (!$dryRun) {
                // Delete associated messages first
                $messageCount = NalaChatMessage::whereIn('conversation_id', 
                    $query->pluck('id')
                )->delete();
                
                // Delete conversations
                $deletedCount = $query->delete();
                
                $this->info("Permanently deleted {$deletedCount} conversations and {$messageCount} messages");
                
                Log::info('Nala chat cleanup: Permanently deleted conversations', [
                    'conversations_deleted' => $deletedCount,
                    'messages_deleted' => $messageCount,
                    'cutoff_date' => $cutoffDate
                ]);
            }
        } else {
            $this->info("No old deleted conversations found");
        }
    }

    /**
     * Clean up messages that belong to deleted conversations
     */
    private function cleanupOrphanedMessages(bool $dryRun)
    {
        $query = NalaChatMessage::whereNotIn('conversation_id', 
            NalaChatConversation::where('status', '!=', 'deleted')->pluck('id')
        );
        
        $count = $query->count();
        
        if ($count > 0) {
            $this->info("Found {$count} orphaned messages to remove");
            
            if (!$dryRun) {
                $deletedCount = $query->delete();
                $this->info("Deleted {$deletedCount} orphaned messages");
                
                Log::info('Nala chat cleanup: Deleted orphaned messages', [
                    'messages_deleted' => $deletedCount
                ]);
            }
        } else {
            $this->info("No orphaned messages found");
        }
    }

    /**
     * Optimize active conversations by maintaining message limits
     */
    private function optimizeActiveConversations(bool $dryRun)
    {
        $conversations = NalaChatConversation::where('status', 'active')
            ->whereHas('messages', function ($query) {
                $query->where('status', '!=', 'deleted');
            }, '>', 30) // Conversations with more than 30 messages
            ->get();

        $totalOptimized = 0;
        
        foreach ($conversations as $conversation) {
            $messageCount = $conversation->messages()
                ->where('status', '!=', 'deleted')
                ->count();
                
            if ($messageCount > 30) {
                $this->info("Optimizing conversation {$conversation->id} ({$messageCount} messages)");
                
                if (!$dryRun) {
                    $conversation->maintainMessageLimit(30);
                    $totalOptimized++;
                }
            }
        }
        
        if ($totalOptimized > 0) {
            $this->info("Optimized {$totalOptimized} conversations");
            
            Log::info('Nala chat cleanup: Optimized conversations', [
                'conversations_optimized' => $totalOptimized
            ]);
        } else {
            $this->info("No conversations need optimization");
        }
    }
}
