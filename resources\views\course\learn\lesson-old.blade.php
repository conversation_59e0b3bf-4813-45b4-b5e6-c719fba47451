@extends('layouts.app')

@section('title', $lesson->title . ' - ' . $course->title . ' - Ngambis<PERSON>y')

@push('styles')
<style>
/* Udemy-style Course Learning Layout */
.lesson-learning-page {
    min-height: 100vh;
    background: #f8fafc;
    display: flex;
    flex-direction: column;
}

/* Header */
.lesson-header {
    background: #1c1d1f;
    color: white;
    padding: 0.75rem 0;
    border-bottom: 1px solid #3c3d41;
    position: sticky;
    top: 0;
    z-index: 100;
}

.lesson-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
    padding: 0 1rem;
}

.lesson-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.lesson-back-btn {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
    min-height: 44px;
    min-width: 44px;
    justify-content: center;
}

.lesson-back-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.lesson-title-info {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.lesson-breadcrumb {
    font-size: 0.875rem;
    color: #ccc;
    margin-top: 0.25rem;
}

.lesson-header-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lesson-nav-btn {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    min-height: 44px;
    border: none;
    cursor: pointer;
}

.lesson-nav-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.lesson-nav-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

.lesson-nav-btn-primary {
    background: #a435f0;
    color: #fff;
}

.lesson-nav-btn-primary:hover {
    background: #8710d8;
}

/* Main Layout */
.lesson-main-layout {
    display: flex;
    flex: 1;
    min-height: 0;
}

/* Professional Curriculum Sidebar - Consistent with Main Page */
.lesson-curriculum-sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 1023px) {
    .lesson-curriculum-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 55;
        transform: translateX(-100%);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border-right: none;
    }

    .lesson-curriculum-sidebar.show {
        transform: translateX(0);
    }
}

.curriculum-header {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.curriculum-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: white;
    margin: 0 0 0.5rem 0;
    letter-spacing: -0.025em;
}

.curriculum-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

.curriculum-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fafbfc;
}

/* Custom Scrollbar */
.curriculum-content::-webkit-scrollbar {
    width: 6px;
}

.curriculum-content::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.curriculum-content::-webkit-scrollbar-thumb {
    background: #d1d7dc;
    border-radius: 3px;
}

.curriculum-content::-webkit-scrollbar-thumb:hover {
    background: #a7b0b8;
}

.curriculum-close-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    min-height: 40px;
    min-width: 40px;
    display: none;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

@media (max-width: 1023px) {
    .curriculum-close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.curriculum-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.05);
}

/* Professional Mobile Toggle Button */
.curriculum-toggle-btn {
    position: fixed;
    top: 50%;
    left: 1rem;
    z-index: 60;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 50%;
    padding: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    min-height: 48px;
    min-width: 48px;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-50%);
}

@media (max-width: 1023px) {
    .curriculum-toggle-btn {
        display: flex;
    }
}

.curriculum-toggle-btn:hover {
    background: #f8fafc;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-50%) scale(1.05);
}

.curriculum-toggle-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* Main Content Area */
.lesson-content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background: #fff;
}
/* Responsive Design */
@media (max-width: 767px) {
    .lesson-header-content {
        padding: 0 0.5rem;
    }

    .lesson-title {
        font-size: 0.875rem;
    }

    .lesson-nav-btn {
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .lesson-content-area {
        padding: 0.5rem;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .lesson-curriculum-sidebar {
        width: 280px;
    }
}
</style>
@endpush

@section('content')
<div class="lesson-learning-page">
    <!-- Lesson Header -->
    <div class="lesson-header">
        <div class="lesson-header-content">
            <div class="lesson-header-left">
                <a href="{{ route('course.learn', $course) }}" class="lesson-back-btn">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <div class="lesson-title-info">
                    <h1 class="lesson-title">{{ $lesson->title }}</h1>
                    <div class="lesson-breadcrumb">{{ $course->title }} • {{ $lesson->chapter->title }}</div>
                </div>
            </div>
            <div class="lesson-header-right">
                @if($navigation['previous'])
                    <a href="{{ route('course.lesson', [$course, $navigation['previous']]) }}" class="lesson-nav-btn lesson-nav-btn-secondary">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                        Sebelumnya
                    </a>
                @endif
                @if($navigation['next'])
                    <a href="{{ route('course.lesson', [$course, $navigation['next']]) }}" class="lesson-nav-btn lesson-nav-btn-primary">
                        Selanjutnya
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Mobile Curriculum Toggle -->
    <button class="curriculum-toggle-btn" onclick="toggleCurriculum()">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
        </svg>
    </button>

    <!-- Main Layout -->
    <div class="lesson-main-layout">
        <!-- Curriculum Sidebar -->
        <div class="lesson-curriculum-sidebar" id="curriculumSidebar">
            <button class="curriculum-close-btn" onclick="toggleCurriculum()">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>

            <div class="curriculum-header">
                <h2 class="curriculum-title">Konten Kursus</h2>
                <p class="curriculum-subtitle">{{ $course->chapters->count() }} bab • {{ $course->lessons->count() }} materi</p>
            </div>

            <div class="curriculum-content">
                @foreach($course->chapters as $chapter)
                    <div class="chapter-section">
                        <div class="chapter-header">
                            <h3 class="chapter-title">{{ $chapter->title }}</h3>
                            <p class="chapter-meta">{{ $chapter->lessons->count() }} materi</p>
                        </div>
                        <div class="chapter-lessons">
                            @foreach($chapter->lessons as $chapterLesson)
                                <a href="{{ route('course.lesson', [$course, $chapterLesson]) }}"
                                   class="lesson-item {{ $chapterLesson->id === $lesson->id ? 'active' : '' }}">
                                    <div class="lesson-item-content">
                                        <div class="lesson-type-icon lesson-type-{{ $chapterLesson->type }}">
                                            @if($chapterLesson->type === 'video')
                                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a2 2 0 012-2h8a2 2 0 012 2v2M7 16h10"/>
                                                </svg>
                                            @elseif($chapterLesson->type === 'text')
                                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                            @elseif($chapterLesson->type === 'quiz')
                                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            @else
                                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                                </svg>
                                            @endif
                                        </div>
                                        <div class="lesson-info">
                                            <div class="lesson-title">{{ $chapterLesson->title }}</div>
                                            <div class="lesson-meta">
                                                @if($chapterLesson->duration_minutes)
                                                    <span>{{ $chapterLesson->duration_minutes }} menit</span>
                                                @endif
                                                <span class="lesson-type-label">{{ $chapterLesson->type_indonesian }}</span>
                                            </div>
                                        </div>
                                        <div class="lesson-status">
                                            @php
                                                $lessonProgress = $chapterLesson->progress->where('user_id', auth()->id())->first();
                                            @endphp
                                            @if($lessonProgress && $lessonProgress->status === 'completed')
                                                <div class="lesson-status-completed">
                                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                            @elseif($lessonProgress && $lessonProgress->status === 'in_progress')
                                                <div class="lesson-status-in-progress">
                                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                            @else
                                                <div class="lesson-status-not-started"></div>
                                            @endif
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="lesson-content-area">
            <div class="lesson-content">
                @if($lesson->type === 'video')
                    <!-- Video Content -->
                    <div class="aspect-video bg-black">
                        @if($lesson->video_url)
                            <!-- External Video (YouTube, Vimeo, etc.) -->
                            <iframe
                                src="{{ $lesson->video_url }}"
                                class="w-full h-full"
                                frameborder="0"
                                allowfullscreen>
                            </iframe>
                        @elseif($lesson->video_file)
                            <!-- Uploaded Video -->
                            <video
                                class="w-full h-full"
                                controls
                                preload="metadata"
                                id="lesson-video">
                                <source src="{{ $lesson->secure_video_url }}" type="video/mp4">
                                Browser Anda tidak mendukung pemutar video.
                            </video>
                        @else
                            <div class="flex items-center justify-center h-full text-white">
                                <div class="text-center">
                                    <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="opacity-75">Video tidak tersedia</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @elseif($lesson->type === 'text')
                    <!-- Text Content -->
                    <div class="p-6">
                        <div class="prose max-w-none">
                            {!! $lesson->content ?: '<p>Konten teks tidak tersedia.</p>' !!}
                        </div>
                    </div>
                @elseif($lesson->type === 'quiz')
                    <!-- Quiz Content -->
                    <div class="p-6">
                        @if($lesson->quiz)
                            <div class="mb-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->quiz->title }}</h2>
                                @if($lesson->quiz->description)
                                    <p class="text-gray-600 mb-4">{{ $lesson->quiz->description }}</p>
                                @endif

                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <h3 class="font-semibold text-blue-900 mb-2">Informasi Kuis</h3>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="text-blue-700">Jumlah Soal:</span>
                                            <div class="font-semibold">{{ $lesson->quiz->total_questions }}</div>
                                        </div>
                                        <div>
                                            <span class="text-blue-700">Total Poin:</span>
                                            <div class="font-semibold">{{ $lesson->quiz->total_points }}</div>
                                        </div>
                                        @if($lesson->quiz->time_limit)
                                            <div>
                                                <span class="text-blue-700">Waktu:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->time_limit }} menit</div>
                                            </div>
                                        @endif
                                        @if($lesson->quiz->passing_score)
                                            <div>
                                                <span class="text-blue-700">Nilai Lulus:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->passing_score }}%</div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button class="btn btn-primary btn-lg" onclick="startQuiz()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Mulai Kuis
                                </button>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <p class="text-gray-500">Kuis tidak tersedia.</p>
                            </div>
                        @endif
                    </div>
                @elseif($lesson->type === 'assignment')
                    <!-- Assignment Content -->
                    <div class="p-6">
                        @if($lesson->assignment)
                            <div class="mb-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->assignment->title }}</h2>
                                @if($lesson->assignment->description)
                                    <div class="prose max-w-none mb-6">
                                        {!! $lesson->assignment->description !!}
                                    </div>
                                @endif

                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                                    <h3 class="font-semibold text-orange-900 mb-2">Informasi Tugas</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                        <div>
                                            <span class="text-orange-700">Maksimal Poin:</span>
                                            <div class="font-semibold">{{ $lesson->assignment->max_points }}</div>
                                        </div>
                                        @if($lesson->assignment->due_date)
                                            <div>
                                                <span class="text-orange-700">Deadline:</span>
                                                <div class="font-semibold">{{ $lesson->assignment->formatted_due_date }}</div>
                                            </div>
                                        @endif
                                        <div>
                                            <span class="text-orange-700">File Maksimal:</span>
                                            <div class="font-semibold">{{ $lesson->assignment->max_files }} file</div>
                                        </div>
                                    </div>
                                </div>

                                @if($lesson->assignment->requirements)
                                    <div class="mb-6">
                                        <h3 class="font-semibold text-gray-900 mb-2">Persyaratan Tugas</h3>
                                        <div class="prose max-w-none">
                                            {!! $lesson->assignment->requirements !!}
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="text-center">
                                <button class="btn btn-primary btn-lg" onclick="startAssignment()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Mulai Mengerjakan
                                </button>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <p class="text-gray-500">Tugas tidak tersedia.</p>
                            </div>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Lesson Description -->
            @if($lesson->description)
                <div class="p-6 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Deskripsi Materi</h3>
                    <div class="prose max-w-none text-gray-600">
                        <p>{{ $lesson->description }}</p>
                    </div>
                </div>
            @endif

            <!-- Mark as Complete -->
            <div class="p-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Progress Materi</h3>
                        <p class="text-sm text-gray-600">Tandai sebagai selesai setelah Anda menyelesaikan materi ini</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        @if($progress->status === 'completed')
                            <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Selesai
                            </span>
                        @else
                            <button onclick="markAsComplete()" class="btn btn-primary" id="complete-btn">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Tandai Selesai
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Additional Curriculum Sidebar Styles */
.chapter-section {
    margin-bottom: 1.5rem;
}

.chapter-header {
    margin-bottom: 0.75rem;
}

.chapter-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1c1d1f;
    margin: 0 0 0.25rem 0;
}

.chapter-meta {
    font-size: 0.75rem;
    color: #6a6f73;
    margin: 0;
}

.chapter-lessons {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.lesson-item {
    display: block;
    padding: 0.75rem;
    border-radius: 0.25rem;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.lesson-item:hover {
    background: #f7f9fa;
    border-color: #d1d7dc;
}

.lesson-item.active {
    background: #e8f5e8;
    border-color: #4caf50;
}

.lesson-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lesson-type-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.lesson-type-video {
    background: #e3f2fd;
}

.lesson-type-text {
    background: #e8f5e9;
}

.lesson-type-quiz {
    background: #f3e5f5;
}

.lesson-type-assignment {
    background: #fff3e0;
}

.lesson-info {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1c1d1f;
    margin: 0 0 0.25rem 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.lesson-item.active .lesson-title {
    color: #2e7d32;
    font-weight: 600;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6a6f73;
}

.lesson-type-label {
    background: #f7f9fa;
    color: #6a6f73;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-weight: 500;
    text-transform: capitalize;
}

.lesson-status {
    flex-shrink: 0;
}

.lesson-status-completed,
.lesson-status-in-progress {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lesson-status-completed {
    background: #4caf50;
}

.lesson-status-in-progress {
    background: #ff9800;
}

.lesson-status-not-started {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d7dc;
    border-radius: 50%;
    background: #fff;
}
</style>

@push('scripts')
<script>
// Professional Curriculum Sidebar - Enhanced Functionality
(function() {
    'use strict';

    class LessonCurriculumSidebar {
        constructor() {
            this.sidebar = document.getElementById('curriculumSidebar');
            this.toggleBtn = document.querySelector('.curriculum-toggle-btn');
            this.closeBtn = document.querySelector('.curriculum-close-btn');

            this.init();
        }

        init() {
            this.bindEvents();
            this.handleInitialState();
        }

        bindEvents() {
            // Toggle functionality
            if (this.toggleBtn) {
                this.toggleBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.openSidebar();
                });
            }

            if (this.closeBtn) {
                this.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.closeSidebar();
                });
            }

            // Close on outside click (mobile)
            document.addEventListener('click', (event) => {
                if (window.innerWidth <= 1023 &&
                    this.sidebar?.classList.contains('show') &&
                    !this.sidebar.contains(event.target) &&
                    !this.toggleBtn?.contains(event.target)) {
                    this.closeSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 1023) {
                    this.closeSidebar();
                }
            });

            // Escape key to close
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.sidebar?.classList.contains('show')) {
                    this.closeSidebar();
                }
            });
        }

        openSidebar() {
            if (this.sidebar) {
                this.sidebar.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent background scroll
            }
        }

        closeSidebar() {
            if (this.sidebar) {
                this.sidebar.classList.remove('show');
                document.body.style.overflow = ''; // Restore scroll
            }
        }

        handleInitialState() {
            // Ensure sidebar is hidden on mobile by default
            if (window.innerWidth <= 1023 && this.sidebar) {
                this.sidebar.classList.remove('show');
            }
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new LessonCurriculumSidebar();
        });
    } else {
        new LessonCurriculumSidebar();
    }

    // Legacy function for backward compatibility
    window.toggleCurriculum = function() {
        const sidebar = document.getElementById('curriculumSidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    };
})();

function markAsComplete() {
    const btn = document.getElementById('complete-btn');
    if (!btn) return;

    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Memproses...';

    fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            status: 'completed',
            progress_percentage: 100
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Gagal memperbarui progress. Silakan coba lagi.');
            btn.disabled = false;
            btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan. Silakan coba lagi.');
        btn.disabled = false;
        btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
    });
}

function startQuiz() {
    alert('Fitur kuis akan segera tersedia!');
}

function startAssignment() {
    alert('Fitur tugas akan segera tersedia!');
}

// Auto-mark video lessons as in progress when video starts playing
@if($lesson->type === 'video')
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('lesson-video');
    if (video) {
        video.addEventListener('play', function() {
            fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: 'in_progress',
                    progress_percentage: 50
                })
            });
        });
    }
});
@endif

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Ensure curriculum sidebar is hidden on mobile by default
    const sidebar = document.getElementById('curriculumSidebar');
    if (window.innerWidth <= 1023) {
        sidebar.classList.remove('show');
    }
});
</script>
@endpush
@endsection
