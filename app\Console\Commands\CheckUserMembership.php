<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class CheckUserMembership extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:user-membership {email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check user membership status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found");
            $this->info("Available users:");
            User::select('email')->get()->each(function($u) {
                $this->line("- {$u->email}");
            });
            return;
        }

        $this->info("User: {$user->email}");

        // Show all memberships first
        $this->info("All memberships for this user:");
        $allMemberships = $user->memberships()->with('membershipPlan')->get();
        if ($allMemberships->count() === 0) {
            $this->warn("No memberships found at all!");
        } else {
            $allMemberships->each(function($m) {
                $planName = $m->membershipPlan ? $m->membershipPlan->name : 'Plan not found';
                $this->line("- Plan: {$planName}, Status: {$m->status}, Starts: {$m->starts_at}, Expires: {$m->expires_at}");
            });
        }

        $membership = $user->activeMembership;
        if (!$membership) {
            $this->warn("No active membership found");
            return;
        }

        $membership->load('membershipPlan');
        $this->info("\nActive Membership:");
        $this->line("- Plan: {$membership->membershipPlan->name}");
        $this->line("- Slug: {$membership->membershipPlan->slug}");
        $this->line("- Status: {$membership->status}");
        $this->line("- Starts: {$membership->starts_at}");
        $this->line("- Expires: {$membership->expires_at}");
        $this->line("- Is Free: " . ($membership->membershipPlan->is_free ? 'Yes' : 'No'));
    }
}
