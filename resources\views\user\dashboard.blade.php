@extends('layouts.user')

@section('title', 'Dashboard - Ngambiskuy')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/dashboard-responsive.css') }}">
@endpush

@section('content')
<div class="dashboard-container p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
    <!-- Welcome Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <!-- Mobile-first responsive layout -->
            <div class="space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
                <!-- Main content area -->
                <div class="flex-1 space-y-3 lg:space-y-2">
                    <!-- Title and role badge - responsive stacking -->
                    <div class="space-y-2 sm:space-y-0 sm:flex sm:items-start sm:space-x-3">
                        <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">
                            Selamat datang, {{ $user->name }}!
                        </h1>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-{{ $user->getRoleBadgeColor() }}-100 text-{{ $user->getRoleBadgeColor() }}-800 border border-{{ $user->getRoleBadgeColor() }}-200 flex-shrink-0">
                            {{ $user->getRole() }}
                        </span>
                    </div>

                    <!-- Welcome message -->
                    <p class="text-gray-600 text-sm sm:text-base lg:text-lg">
                        Mari lanjutkan perjalanan belajar Anda menuju kesuksesan!
                    </p>

                    <!-- Quick Stats - Mobile responsive grid -->
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 lg:flex lg:items-center lg:space-x-6 lg:gap-0 pt-2">
                        <div class="flex items-center justify-center sm:justify-start text-sm bg-blue-50 sm:bg-transparent p-3 sm:p-0 rounded-lg sm:rounded-none">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <span class="font-medium text-gray-700">Streak:</span>
                            <span class="ml-1 font-bold text-blue-600">{{ $stats['learning_streak'] }} hari</span>
                        </div>
                        <div class="flex items-center justify-center sm:justify-start text-sm bg-purple-50 sm:bg-transparent p-3 sm:p-0 rounded-lg sm:rounded-none">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-purple-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                            <span class="font-medium text-gray-700">XP:</span>
                            <span class="ml-1 font-bold text-purple-600">{{ number_format($stats['xp_points']) }}</span>
                        </div>
                        <div class="flex items-center justify-center sm:justify-start text-sm bg-green-50 sm:bg-transparent p-3 sm:p-0 rounded-lg sm:rounded-none">
                            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-medium text-gray-700">Total Belajar:</span>
                            <span class="ml-1 font-bold text-green-600">{{ $stats['total_hours'] }} jam</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions - Mobile responsive -->
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 lg:flex-shrink-0">
                    <a href="{{ route('courses.index') }}" class="btn btn-primary min-h-[44px] justify-center text-sm sm:text-base">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span>Jelajahi Kursus</span>
                    </a>
                    <a href="{{ route('exams.index') }}" class="btn btn-outline min-h-[44px] justify-center text-sm sm:text-base">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Ikuti Ujian</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6 mb-6 lg:mb-8">
        <!-- Enrolled Courses -->
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-blue-300">
            <div class="flex items-center justify-between mb-3 sm:mb-4">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <a href="{{ route('user.courses') }}" class="text-blue-600 hover:text-blue-800 text-xs sm:text-sm font-medium min-h-[44px] flex items-center px-2 py-1 rounded hover:bg-blue-50 transition-colors">
                    Lihat →
                </a>
            </div>
            <div>
                <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Kursus Diikuti</p>
                <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{{ $stats['enrolled_courses'] }}</p>
                <div class="flex items-center text-xs sm:text-sm text-gray-500">
                    <span>{{ $stats['completed_courses'] }} selesai</span>
                </div>
            </div>
        </div>

        <!-- Certificates -->
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-green-300">
            <div class="flex items-center justify-between mb-3 sm:mb-4">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <a href="{{ route('user.certificates') }}" class="text-green-600 hover:text-green-800 text-xs sm:text-sm font-medium min-h-[44px] flex items-center px-2 py-1 rounded hover:bg-green-50 transition-colors">
                    Lihat →
                </a>
            </div>
            <div>
                <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Sertifikat</p>
                <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{{ $stats['certificates'] }}</p>
                <div class="flex items-center text-xs sm:text-sm text-gray-500">
                    <span>{{ $stats['exam_enrollments'] }} ujian terdaftar</span>
                </div>
            </div>
        </div>

        <!-- XP Points -->
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-purple-300">
            <div class="flex items-center justify-between mb-3 sm:mb-4">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <span class="text-purple-600 text-xs sm:text-sm font-medium px-2 py-1 bg-purple-50 rounded">
                    Level {{ floor($stats['xp_points'] / 1000) + 1 }}
                </span>
            </div>
            <div>
                <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Poin XP</p>
                <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{{ number_format($stats['xp_points']) }}</p>
                <div class="w-full bg-gray-200 rounded-full h-2 mb-1">
                    <div class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: {{ ($stats['xp_points'] % 1000) / 10 }}%"></div>
                </div>
                <div class="flex items-center justify-between text-xs text-gray-500">
                    <span>{{ $stats['xp_points'] % 1000 }}/1000</span>
                    <span>{{ 1000 - ($stats['xp_points'] % 1000) }} lagi</span>
                </div>
            </div>
        </div>

        <!-- Learning Hours -->
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-orange-300">
            <div class="flex items-center justify-between mb-3 sm:mb-4">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                {{-- Commented out due to functionality overlap with user dashboard and courses page
                <a href="{{ route('user.progress') }}" class="text-orange-600 hover:text-orange-800 text-xs sm:text-sm font-medium min-h-[44px] flex items-center px-2 py-1 rounded hover:bg-orange-50 transition-colors">
                    Lihat →
                </a>
                --}}
            </div>
            <div>
                <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Jam Belajar</p>
                <p class="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">{{ $stats['total_hours'] }}</p>
                <div class="flex items-center text-xs sm:text-sm text-gray-500">
                    <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span>Terus bertambah!</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="space-y-6 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-6 mb-6 lg:mb-8">
        <!-- Continue Learning -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="flex items-center justify-between mb-4 sm:mb-6">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-900">Lanjutkan Belajar</h2>
                @if($stats['enrolled_courses'] > 0)
                    <a href="{{ route('user.courses') }}" class="text-blue-600 hover:text-blue-800 text-xs sm:text-sm font-medium min-h-[44px] flex items-center px-2 py-1 rounded hover:bg-blue-50 transition-colors">
                        Lihat Semua →
                    </a>
                @endif
            </div>

            @if($allCoursesWithProgress->count() > 0)
                <div class="space-y-3 sm:space-y-4">
                    @foreach($allCoursesWithProgress as $courseData)
                        <div class="border border-gray-200 rounded-lg p-3 sm:p-4 hover:border-blue-300 transition-colors">
                            <div class="space-y-3 sm:space-y-0 sm:flex sm:items-center sm:space-x-4">
                                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                                    @if($courseData['course']->thumbnail)
                                        <img src="{{ asset('storage/' . $courseData['course']->thumbnail) }}" alt="{{ $courseData['course']->title }}" class="w-full h-full object-cover rounded-lg">
                                    @else
                                        <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    @endif
                                </div>
                                <div class="flex-1 text-center sm:text-left">
                                    <div class="flex items-center gap-2 mb-1">
                                        <h3 class="font-medium text-gray-900 text-sm sm:text-base">{{ $courseData['course']->title }}</h3>
                                        @if($courseData['is_free_course'])
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Gratis
                                            </span>
                                        @endif
                                    </div>
                                    <p class="text-xs sm:text-sm text-gray-600 mb-2">{{ $courseData['course']->tutor->name }} • {{ $courseData['course']->category->name ?? 'Umum' }}</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: {{ $courseData['progress'] }}%"></div>
                                    </div>
                                    <div class="flex items-center justify-between text-xs text-gray-500 mt-1">
                                        <span>{{ $courseData['progress'] }}% selesai</span>
                                        <span>{{ $courseData['completed_lessons'] }}/{{ $courseData['total_lessons'] }} pelajaran</span>
                                    </div>
                                </div>
                                <a href="{{ $courseData['url'] }}" class="btn btn-primary btn-sm min-h-[44px] w-full sm:w-auto mt-3 sm:mt-0">
                                    Lanjutkan
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 sm:py-12">
                    <div class="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-2">Mulai Perjalanan Belajar Anda</h3>
                    <p class="text-gray-600 mb-6 max-w-sm mx-auto text-sm sm:text-base px-4">Belum ada kursus yang diikuti. Jelajahi ribuan kursus berkualitas dan mulai belajar hari ini!</p>
                    <div class="space-y-3 px-4">
                        <a href="{{ route('courses.index') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto inline-flex">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <span>Jelajahi Kursus</span>
                        </a>
                        <div class="text-sm text-gray-500">atau</div>
                        <a href="{{ route('exams.index') }}" class="btn btn-outline min-h-[44px] w-full sm:w-auto inline-flex">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span>Coba Ujian Gratis</span>
                        </a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Recent Activities -->
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4 sm:mb-6">Aktivitas Terbaru</h2>

            @if($recentActivities->count() > 0)
                <div class="space-y-3 sm:space-y-4">
                    @foreach($recentActivities as $activity)
                        <div class="flex items-start space-x-3 p-2 sm:p-3 rounded-lg hover:bg-gray-50 transition-colors min-h-[44px]">
                            <div class="w-8 h-8 bg-{{ $activity['color'] }}-100 rounded-full flex items-center justify-center flex-shrink-0">
                                @if($activity['icon'] === 'book')
                                    <svg class="w-4 h-4 text-{{ $activity['color'] }}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4 text-{{ $activity['color'] }}-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $activity['title'] }}</p>
                                <div class="flex items-center justify-between mt-1">
                                    <p class="text-xs text-gray-500">{{ $activity['date']->diffForHumans() }}</p>
                                    @if(isset($activity['score']))
                                        <span class="text-xs px-2 py-1 rounded-full {{ $activity['color'] === 'green' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $activity['score'] }}%
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6 sm:py-8">
                    <div class="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-sm text-gray-500">Belum ada aktivitas</p>
                    <p class="text-xs text-gray-400 mt-1">Mulai belajar untuk melihat aktivitas Anda</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Recommended Courses -->
    <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 mb-6 lg:mb-8">
        <div class="space-y-3 sm:space-y-0 sm:flex sm:items-center sm:justify-between mb-4 sm:mb-6">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                <h2 class="text-lg sm:text-xl font-semibold text-gray-900">Kursus Rekomendasi</h2>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white w-fit">
                    <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    <span>Terpilih untuk Anda</span>
                </span>
            </div>
            <a href="{{ route('courses.index') }}" class="text-blue-600 hover:text-blue-800 text-xs sm:text-sm font-medium min-h-[44px] flex items-center px-2 py-1 rounded hover:bg-blue-50 transition-colors w-fit">
                Lihat Semua →
            </a>
        </div>

        @if($recommendedCourses->count() > 0)
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
                @foreach($recommendedCourses as $course)
                    <a href="{{ $course['url'] }}" class="block border border-gray-200 rounded-lg p-3 sm:p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 min-h-[44px]">
                        <div class="space-y-3 sm:space-y-0 sm:flex sm:items-start sm:space-x-4">
                            @if($course['thumbnail'])
                                <img src="{{ $course['thumbnail'] }}" alt="{{ $course['title'] }}" class="w-12 h-12 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0 mx-auto sm:mx-0">
                            @else
                                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                            @endif
                            <div class="flex-1 min-w-0 text-center sm:text-left">
                                <div class="flex items-center gap-2 mb-1">
                                    <h3 class="font-medium text-gray-900 truncate text-sm sm:text-base">{{ $course['title'] }}</h3>
                                    @if($course['is_free'])
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Gratis
                                        </span>
                                    @endif
                                </div>
                                <p class="text-xs sm:text-sm text-gray-600 mb-2 line-clamp-2">{{ $course['description'] }}</p>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-center sm:justify-between flex-wrap gap-2">
                                        <div class="flex items-center space-x-2">
                                            <span class="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">{{ $course['level'] }}</span>
                                            @if($course['rating'] > 0)
                                                <div class="flex items-center">
                                                    <svg class="w-3 h-3 text-yellow-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                    <span class="text-xs text-gray-600 ml-1">{{ number_format($course['rating'], 1) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                        <span class="font-semibold {{ $course['is_free'] ? 'text-green-600' : 'text-blue-600' }} text-sm">{{ $course['price'] }}</span>
                                    </div>
                                    <div class="flex items-center justify-center sm:justify-between text-xs text-gray-500 flex-wrap gap-1">
                                        <span class="truncate">{{ $course['tutor'] }}</span>
                                        <span class="flex-shrink-0">{{ $course['students'] }} siswa</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                @endforeach
            </div>
        @else
            <div class="text-center py-6 sm:py-8">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-2">Jelajahi Kursus Terbaik</h3>
                <p class="text-gray-600 mb-4 text-sm sm:text-base px-4">Temukan kursus yang sesuai dengan minat dan kebutuhan Anda</p>
                <a href="{{ route('courses.index') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto">
                    Lihat Semua Kursus
                </a>
            </div>
        @endif
    </div>

    <!-- Quick Links & Account Info -->
    <div class="space-y-6 lg:space-y-0 lg:grid lg:grid-cols-2 lg:gap-6">
        <!-- Quick Links -->
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-4">Akses Cepat</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <a href="{{ route('user.profile') }}" class="flex items-center p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors min-h-[44px]">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Profil</span>
                </a>
                <a href="{{ route('user.certificates') }}" class="flex items-center p-3 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-colors min-h-[44px]">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Sertifikat</span>
                </a>
                <a href="{{ route('user.membership') }}" class="flex items-center p-3 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors min-h-[44px] {{ !auth()->user()->hasActiveMembership() ? 'bg-purple-50 border-purple-200' : '' }}">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    <div class="flex-1 min-w-0">
                        <span class="text-sm font-medium text-gray-700 block">Membership</span>
                        @if(!auth()->user()->hasActiveMembership())
                            <div class="text-xs text-purple-600 font-medium">Upgrade untuk AI!</div>
                        @endif
                    </div>
                </a>
                <a href="{{ route('blog.index') }}" class="flex items-center p-3 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-colors min-h-[44px]">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 text-orange-600 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Blog</span>
                </a>
                @if(!auth()->user()->isTutor() && !auth()->user()->hasTutorProfile())
                    <a href="{{ route('tutor.register.terms') }}" class="sm:col-span-2 flex items-center p-3 rounded-lg border border-emerald-200 bg-emerald-50 hover:border-emerald-300 hover:bg-emerald-100 transition-colors min-h-[44px]">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 mr-2 sm:mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <div class="flex-1 min-w-0">
                            <span class="text-sm font-medium text-emerald-700 block">Jadi Pengajar</span>
                            <div class="text-xs text-emerald-600">Bagikan ilmu & dapatkan penghasilan</div>
                        </div>
                    </a>
                @endif
            </div>
        </div>

        <!-- Account Info -->
        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg p-4 sm:p-5 lg:p-6 border border-blue-200">
            <div class="flex items-start space-x-3 sm:space-x-4">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="flex-1 min-w-0">
                    <h3 class="text-base sm:text-lg font-semibold text-blue-900 mb-2">Informasi Akun</h3>
                    <div class="space-y-2 text-xs sm:text-sm text-blue-800">
                        <div class="flex items-center justify-between">
                            <span class="font-medium">Email:</span>
                            <span class="truncate ml-2">{{ $user->email }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="font-medium">Bergabung:</span>
                            <span class="flex-shrink-0">{{ $user->created_at->format('d M Y') }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="font-medium">Status:</span>
                            <span class="px-2 py-1 bg-blue-200 text-blue-800 rounded-full text-xs flex-shrink-0">{{ $user->getRole() }}</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="{{ route('user.profile') }}" class="text-blue-600 hover:text-blue-800 text-xs sm:text-sm font-medium inline-flex items-center min-h-[44px] px-2 py-1 rounded hover:bg-blue-200 transition-colors">
                            Kelola Profil →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
