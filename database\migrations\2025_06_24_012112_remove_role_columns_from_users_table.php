<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\User;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, migrate existing role data from boolean columns to the new roles system
        $this->migrateExistingRoleData();

        Schema::table('users', function (Blueprint $table) {
            // Remove the boolean role columns
            $table->dropColumn(['is_tutor', 'is_admin', 'is_superadmin']);
        });
    }

    /**
     * Migrate existing role data from boolean columns to the new roles system
     */
    private function migrateExistingRoleData(): void
    {
        // Check if the boolean columns still exist
        if (!Schema::hasColumn('users', 'is_tutor')) {
            return; // Columns already removed, skip migration
        }

        // Get all users with role data
        $users = User::whereNotNull('id')->get();

        foreach ($users as $user) {
            $rolesToAssign = [Role::USER]; // Everyone gets user role by default

            // Check old boolean columns and assign corresponding roles
            if ($user->getAttributes()['is_superadmin'] ?? false) {
                $rolesToAssign[] = Role::SUPERADMIN;
                $rolesToAssign[] = Role::ADMIN;
                $rolesToAssign[] = Role::TUTOR;
            } elseif ($user->getAttributes()['is_admin'] ?? false) {
                $rolesToAssign[] = Role::ADMIN;
                $rolesToAssign[] = Role::TUTOR;
            } elseif ($user->getAttributes()['is_tutor'] ?? false) {
                $rolesToAssign[] = Role::TUTOR;
            }

            // Assign roles using the new system
            $user->syncRoles(array_unique($rolesToAssign));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add back the boolean role columns for rollback only if they don't exist
            if (!Schema::hasColumn('users', 'is_tutor')) {
                $table->boolean('is_tutor')->default(false);
            }
            if (!Schema::hasColumn('users', 'is_admin')) {
                $table->boolean('is_admin')->default(false);
            }
            if (!Schema::hasColumn('users', 'is_superadmin')) {
                $table->boolean('is_superadmin')->default(false);
            }
        });

        // Restore role data from the new system back to boolean columns
        $this->restoreRoleDataToBooleanColumns();
    }

    /**
     * Restore role data from the new system back to boolean columns
     */
    private function restoreRoleDataToBooleanColumns(): void
    {
        // Only restore if the boolean columns exist
        if (!Schema::hasColumn('users', 'is_tutor')) {
            return;
        }

        $users = User::with('roles')->get();

        foreach ($users as $user) {
            $updateData = [];
            
            if (Schema::hasColumn('users', 'is_tutor')) {
                $updateData['is_tutor'] = $user->hasRole(Role::TUTOR);
            }
            if (Schema::hasColumn('users', 'is_admin')) {
                $updateData['is_admin'] = $user->hasRole(Role::ADMIN);
            }
            if (Schema::hasColumn('users', 'is_superadmin')) {
                $updateData['is_superadmin'] = $user->hasRole(Role::SUPERADMIN);
            }
            
            if (!empty($updateData)) {
                $user->update($updateData);
            }
        }
    }
};
