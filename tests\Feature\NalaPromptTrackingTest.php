<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserMembership;
use App\Models\MembershipPlan;
use App\Services\NalaPromptTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class NalaPromptTrackingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $promptTrackingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->promptTrackingService = new NalaPromptTrackingService();
    }

    /** @test */
    public function it_correctly_identifies_free_user_membership_level()
    {
        $user = User::factory()->create([
            'current_membership_id' => null
        ]);

        $membershipLevel = $this->promptTrackingService->getUserMembershipLevel($user);
        
        $this->assertEquals('free', $membershipLevel);
    }

    /** @test */
    public function it_correctly_identifies_membership_user_level()
    {
        // Create a membership plan
        $membershipPlan = MembershipPlan::create([
            'id' => \Illuminate\Support\Str::uuid(),
            'name' => 'Basic',
            'slug' => 'basic',
            'description' => 'Basic membership',
            'type' => 'individual',
            'price' => 99000,
            'duration_months' => 1,
            'is_free' => false,
            'nala_prompts' => 100,
            'is_active' => true,
            'sort_order' => 1
        ]);

        $user = User::factory()->create([
            'current_membership_id' => $membershipPlan->id
        ]);

        $membershipLevel = $this->promptTrackingService->getUserMembershipLevel($user);
        
        $this->assertEquals('basic', $membershipLevel);
    }

    /** @test */
    public function it_correctly_calculates_prompt_limits_for_different_membership_levels()
    {
        $statistics = $this->promptTrackingService->getUsageStatistics(
            User::factory()->create(['current_membership_id' => null])
        );
        
        $this->assertEquals(10, $statistics['daily_limit']);
        $this->assertEquals('free', $statistics['membership_level']);
    }

    /** @test */
    public function it_provides_correct_limit_responses_for_different_membership_levels()
    {
        $freeResponse = $this->promptTrackingService->getMembershipLimitResponse('free');
        $basicResponse = $this->promptTrackingService->getMembershipLimitResponse('basic');
        $standardResponse = $this->promptTrackingService->getMembershipLimitResponse('standard');
        $proResponse = $this->promptTrackingService->getMembershipLimitResponse('pro');

        $this->assertStringContainsString('10 prompt/hari', $freeResponse);
        $this->assertStringContainsString('100 prompt/hari', $basicResponse);
        $this->assertStringContainsString('300 prompt/hari', $standardResponse);
        $this->assertStringContainsString('400 prompt/hari', $proResponse);

        $this->assertStringContainsString('Basic', $freeResponse);
        $this->assertStringContainsString('Standard', $basicResponse);
        $this->assertStringContainsString('Pro', $standardResponse);
    }

    /** @test */
    public function it_can_track_free_user_prompt_usage()
    {
        $user = User::factory()->create([
            'current_membership_id' => null
        ]);

        // Free users should be able to use prompts up to their limit
        $canUse = $this->promptTrackingService->canUsePrompts($user, 1);
        $this->assertTrue($canUse);

        // Test usage
        $used = $this->promptTrackingService->usePrompts($user, 1);
        $this->assertTrue($used);
    }

    /** @test */
    public function it_respects_daily_prompt_limits()
    {
        $user = User::factory()->create([
            'current_membership_id' => null
        ]);

        // Should not be able to use more than daily limit
        $canUseOverLimit = $this->promptTrackingService->canUsePrompts($user, 15); // Over free limit of 10
        $this->assertFalse($canUseOverLimit);
    }
}
