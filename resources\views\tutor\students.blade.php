@extends('layouts.tutor')

@section('title', 'Sis<PERSON> Saya - Tutor Dashboard')

@section('content')
<div class="tutor-dashboard-container tutor-students-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mb-2">
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-0">Si<PERSON><PERSON> Saya</h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 w-fit">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        Student Hub
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">Pantau progress dan interaksi dengan siswa Anda</p>
            </div>
            <div class="tutor-header-actions flex flex-col md:flex-row md:items-center md:space-x-3 gap-3">
                <a href="{{ route('tutor.dashboard') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-emerald-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Siswa</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">{{ count($enrolledStudents) }}</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-teal-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Siswa Aktif</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">0</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-amber-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Completion Rate</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">-</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-green-500">
            <div class="flex items-center">
                <div class="w-10 h-10 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-5 h-5 md:w-6 md:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Pesan Baru</p>
                    <p class="stat-number text-xl md:text-2xl font-bold text-gray-900">0</p>
                </div>
            </div>
        </div>
    </div>

        @if(count($enrolledStudents) > 0)
            <!-- Students List -->
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="px-4 md:px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <h2 class="text-base md:text-lg font-semibold text-gray-900">Daftar Siswa</h2>
                        <div class="flex flex-col md:flex-row md:items-center md:space-x-4 gap-3 md:gap-0">
                            <div class="relative">
                                <input type="text" id="searchInput" placeholder="Cari siswa..."
                                       class="tutor-form-mobile w-full md:w-auto pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <select id="courseFilter" class="tutor-form-mobile w-full md:w-auto border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                <option value="">Semua Kursus</option>
                                @foreach($tutorCourses as $course)
                                    <option value="{{ $course->id }}">{{ $course->title }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Mobile Card View -->
                <div class="block md:hidden p-4 space-y-4">
                    @foreach($enrolledStudents as $student)
                        <div class="student-card border border-gray-200 rounded-lg p-4"
                             data-student-name="{{ strtolower($student['name']) }}"
                             data-student-email="{{ strtolower($student['email']) }}"
                             data-course-id="{{ $student['course_id'] }}"
                             data-course-title="{{ strtolower($student['course_title']) }}">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">{{ substr($student['name'], 0, 2) }}</span>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">{{ $student['name'] }}</div>
                                        <div class="text-xs text-gray-500">{{ $student['email'] }}</div>
                                    </div>
                                </div>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                           {{ $student['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ $student['status'] === 'active' ? 'Aktif' : 'Tidak Aktif' }}
                                </span>
                            </div>

                            <div class="space-y-2 mb-3">
                                <div>
                                    <span class="text-xs text-gray-500">Kursus:</span>
                                    <div class="text-sm text-gray-900">{{ $student['course_title'] }}</div>
                                    <div class="text-xs text-gray-500">Enrolled: {{ $student['enrollment_date'] }}</div>
                                </div>

                                <div>
                                    <span class="text-xs text-gray-500">Progress:</span>
                                    <div class="flex items-center mt-1">
                                        <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-emerald-500 h-2 rounded-full" style="width: {{ $student['progress'] }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-900">{{ $student['progress'] }}%</span>
                                    </div>
                                </div>

                                <div>
                                    <span class="text-xs text-gray-500">Terakhir Aktif:</span>
                                    <div class="text-sm text-gray-900">{{ $student['last_active'] }}</div>
                                </div>
                            </div>

                            <div class="student-actions flex flex-col space-y-2">
                                <button class="tutor-touch-friendly btn btn-outline text-emerald-600 hover:text-emerald-700 text-sm">Lihat Detail</button>
                                <button class="tutor-touch-friendly btn btn-outline text-teal-600 hover:text-teal-700 text-sm">Kirim Pesan</button>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Desktop Table View -->
                <div class="hidden md:block overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Siswa</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kursus</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terakhir Aktif</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($enrolledStudents as $student)
                                <tr class="hover:bg-gray-50 student-row"
                                    data-student-name="{{ strtolower($student['name']) }}"
                                    data-student-email="{{ strtolower($student['email']) }}"
                                    data-course-id="{{ $student['course_id'] }}"
                                    data-course-title="{{ strtolower($student['course_title']) }}">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">{{ substr($student['name'], 0, 2) }}</span>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ $student['name'] }}</div>
                                                <div class="text-sm text-gray-500">{{ $student['email'] }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ $student['course_title'] }}</div>
                                        <div class="text-sm text-gray-500">Enrolled: {{ $student['enrollment_date'] }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                <div class="bg-emerald-500 h-2 rounded-full" style="width: {{ $student['progress'] }}%"></div>
                                            </div>
                                            <span class="text-sm text-gray-900">{{ $student['progress'] }}%</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $student['last_active'] }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                   {{ $student['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                            {{ $student['status'] === 'active' ? 'Aktif' : 'Tidak Aktif' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-emerald-600 hover:text-emerald-700 mr-3">Lihat Detail</button>
                                        <button class="text-teal-600 hover:text-teal-700">Kirim Pesan</button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm p-8 md:p-12 text-center border border-gray-200">
            <div class="w-12 h-12 md:w-16 md:h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4 md:mb-6">
                <svg class="w-6 h-6 md:w-8 md:h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 715 0z"></path>
                </svg>
            </div>
            <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-2">Belum Ada Siswa</h3>
            <p class="text-gray-600 mb-4 md:mb-6 max-w-md mx-auto text-sm">
                Siswa yang mendaftar ke kursus Anda akan muncul di sini. Mulai dengan membuat kursus pertama Anda.
            </p>
            <!-- Mobile: Vertical Button Layout -->
            <div class="flex flex-col md:flex-row gap-3 justify-center">
                <a href="{{ route('tutor.create-course') }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly order-1">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Buat Kursus Pertama
                </a>
                <a href="{{ route('tutor.courses') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly order-2">
                    Lihat Kursus Saya
                </a>
            </div>
        </div>
    @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const courseFilter = document.getElementById('courseFilter');
    const studentRows = document.querySelectorAll('.student-row');
    const studentCards = document.querySelectorAll('.student-card');

    function filterStudents() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const selectedCourseId = courseFilter.value;

        // Filter desktop table rows
        studentRows.forEach(row => {
            const studentName = row.dataset.studentName;
            const studentEmail = row.dataset.studentEmail;
            const courseId = row.dataset.courseId;
            const courseTitle = row.dataset.courseTitle;

            // Check search term match
            const matchesSearch = searchTerm === '' ||
                                studentName.includes(searchTerm) ||
                                studentEmail.includes(searchTerm) ||
                                courseTitle.includes(searchTerm);

            // Check course filter match
            const matchesCourse = selectedCourseId === '' || courseId === selectedCourseId;

            // Show/hide row based on both filters
            if (matchesSearch && matchesCourse) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        // Filter mobile cards
        studentCards.forEach(card => {
            const studentName = card.dataset.studentName;
            const studentEmail = card.dataset.studentEmail;
            const courseId = card.dataset.courseId;
            const courseTitle = card.dataset.courseTitle;

            // Check search term match
            const matchesSearch = searchTerm === '' ||
                                studentName.includes(searchTerm) ||
                                studentEmail.includes(searchTerm) ||
                                courseTitle.includes(searchTerm);

            // Check course filter match
            const matchesCourse = selectedCourseId === '' || courseId === selectedCourseId;

            // Show/hide card based on both filters
            if (matchesSearch && matchesCourse) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        // Show/hide "no results" message
        const visibleRows = Array.from(studentRows).filter(row => row.style.display !== 'none');
        const visibleCards = Array.from(studentCards).filter(card => card.style.display !== 'none');
        updateNoResultsMessage(visibleRows.length === 0 && visibleCards.length === 0);
    }

    function updateNoResultsMessage(show) {
        let noResultsRow = document.getElementById('no-results-row');

        if (show && !noResultsRow) {
            // Create no results row
            const tbody = document.querySelector('.student-row').parentNode;
            noResultsRow = document.createElement('tr');
            noResultsRow.id = 'no-results-row';
            noResultsRow.innerHTML = `
                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <svg class="w-12 h-12 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <p class="text-lg font-medium text-gray-900 mb-1">Tidak ada siswa ditemukan</p>
                        <p class="text-sm text-gray-500">Coba ubah kata kunci pencarian atau filter kursus</p>
                    </div>
                </td>
            `;
            tbody.appendChild(noResultsRow);
        } else if (!show && noResultsRow) {
            noResultsRow.remove();
        }
    }

    // Add event listeners
    if (searchInput) {
        searchInput.addEventListener('input', filterStudents);
    }

    if (courseFilter) {
        courseFilter.addEventListener('change', filterStudents);
    }
});
</script>
@endsection
