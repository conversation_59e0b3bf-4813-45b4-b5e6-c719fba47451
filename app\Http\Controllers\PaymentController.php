<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Payment;
use App\Models\MembershipPlan;
use App\Models\UserMembership;
use App\Models\CoursePurchase;
use App\Models\CertificationPurchase;
use App\Models\Course;
use App\Models\User;

class PaymentController extends Controller
{
    /**
     * Show pricing page with NALA membership plans.
     */
    public function pricing()
    {
        $membershipPlans = MembershipPlan::active()
            ->orderBy('type')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('type');

        // Sample courses to show pricing examples
        $sampleCourses = Course::published()
            ->with('tutor')
            ->take(6)
            ->get();

        // Get current user's active membership if authenticated
        $currentMembership = null;
        $currentPlanId = null;
        $userHasFreeMembership = false;

        if (Auth::check()) {
            $user = Auth::user();
            $currentMembership = $user->activeMembership;
            if ($currentMembership) {
                $currentMembership->load('membershipPlan');
                $currentPlanId = $currentMembership->membership_plan_id;
                $userHasFreeMembership = $currentMembership->membershipPlan->is_free;
            }
        }

        return view('payment.pricing', compact('membershipPlans', 'sampleCourses', 'currentMembership', 'currentPlanId', 'userHasFreeMembership'));
    }

    /**
     * Show membership checkout page.
     */
    public function membershipCheckout(Request $request, MembershipPlan $membershipPlan)
    {
        $user = Auth::user();

        // Get current active membership
        $activeMembership = $user->activeMembership;
        $currentPlan = $activeMembership ? $activeMembership->membershipPlan : null;

        // Check if user is trying to select the same plan they already have
        if ($activeMembership && $currentPlan && $currentPlan->id === $membershipPlan->id) {
            return redirect()->route('payment.pricing')
                ->with('info', 'Anda sudah memiliki paket ' . $membershipPlan->name . ' yang aktif.');
        }

        $teamSize = $request->input('team_size', $membershipPlan->minimum_users);

        if ($membershipPlan->is_team_plan && $teamSize < $membershipPlan->minimum_users) {
            $teamSize = $membershipPlan->minimum_users;
        }

        $totalPrice = $membershipPlan->is_team_plan
            ? $membershipPlan->price_per_user * $teamSize
            : $membershipPlan->price;

        // Determine if this is an upgrade or downgrade
        $isUpgrade = false;
        $isDowngrade = false;
        if ($currentPlan) {
            $isUpgrade = $currentPlan->sort_order < $membershipPlan->sort_order;
            $isDowngrade = $currentPlan->sort_order > $membershipPlan->sort_order;
        }

        return view('payment.membership-checkout', compact(
            'membershipPlan',
            'teamSize',
            'totalPrice',
            'activeMembership',
            'currentPlan',
            'isUpgrade',
            'isDowngrade'
        ));
    }

    /**
     * Process membership payment.
     */
    public function processMembershipPayment(Request $request, MembershipPlan $membershipPlan)
    {
        $request->validate([
            'payment_method' => 'required|string',
            'team_size' => 'nullable|integer|min:' . $membershipPlan->minimum_users,
            'terms_agreed' => 'required|accepted',
        ]);

        $user = Auth::user();
        $teamSize = $request->input('team_size', 1);

        if ($membershipPlan->is_team_plan && $teamSize < $membershipPlan->minimum_users) {
            return redirect()->back()->with('error', 'Ukuran tim minimal ' . $membershipPlan->minimum_users . ' orang.');
        }

        $totalPrice = $membershipPlan->is_team_plan
            ? $membershipPlan->price_per_user * $teamSize
            : $membershipPlan->price;

        try {
            DB::beginTransaction();

            // Deactivate any existing active memberships for this user
            UserMembership::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'expired']);

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payment_type' => 'membership',
                'payable_id' => $membershipPlan->id,
                'payable_type' => MembershipPlan::class,
                'transaction_id' => 'TXN-' . time() . '-' . $user->id,
                'amount' => $totalPrice,
                'platform_fee' => Payment::calculatePlatformFee($totalPrice),
                'tutor_earnings' => 0, // No tutor earnings for memberships
                'currency' => 'IDR',
                'status' => 'pending',
                'payment_method' => $request->payment_method,
            ]);

            // Create user membership record
            $startsAt = now();
            $expiresAt = $startsAt->copy()->addMonths($membershipPlan->duration_months);

            $userMembership = UserMembership::create([
                'user_id' => $user->id,
                'membership_plan_id' => $membershipPlan->id,
                'payment_id' => $payment->id,
                'status' => 'pending',
                'starts_at' => $startsAt,
                'expires_at' => $expiresAt,
                'team_size' => $teamSize,
                'nala_prompts_allocated' => $membershipPlan->nala_prompts ?? 0,
                'nala_prompts_remaining' => $membershipPlan->nala_prompts ?? 0,
                'has_unlimited_nala' => $membershipPlan->has_unlimited_nala,
                'has_ice_full' => $membershipPlan->has_ice_full,
                'has_ai_teaching_assistants_courses' => $membershipPlan->has_ai_teaching_assistants_courses,
                'has_ai_teaching_assistants_tryout' => $membershipPlan->has_ai_teaching_assistants_tryout,
                'has_free_certifications' => $membershipPlan->has_free_certifications,
                'has_blog_access' => $membershipPlan->has_blog_access,
                'career_path_predictor' => $membershipPlan->career_path_predictor,
                'has_priority_support' => $membershipPlan->has_priority_support,
            ]);

            DB::commit();

            // TODO: Integrate with payment gateway (Midtrans, Xendit, etc.)
            // For now, we'll simulate successful payment
            $this->simulatePaymentSuccess($payment, $userMembership);

            return redirect()->route('user.membership')
                ->with('success', 'Pembayaran membership berhasil! Membership Anda telah aktif.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Terjadi kesalahan saat memproses pembayaran: ' . $e->getMessage());
        }
    }

    /**
     * Show course checkout page.
     */
    public function courseCheckout(Course $course)
    {
        $user = Auth::user();

        // Check if user already purchased this course
        $existingPurchase = CoursePurchase::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->active()
            ->first();

        if ($existingPurchase) {
            return redirect()->route('course.show', $course)
                ->with('error', 'Anda sudah memiliki akses ke kursus ini.');
        }

        $platformFee = $course->calculatePlatformFee();
        $tutorEarnings = $course->calculateTutorEarnings(false); // TODO: Check referral

        return view('payment.course-checkout', compact(
            'course',
            'platformFee',
            'tutorEarnings'
        ));
    }

    /**
     * Process course payment.
     */
    public function processCoursePayment(Request $request, Course $course)
    {
        $request->validate([
            'payment_method' => 'required|string',
            'terms_agreed' => 'required|accepted',
        ]);

        $user = Auth::user();

        // Check if user already purchased this course
        $existingPurchase = CoursePurchase::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->active()
            ->first();

        if ($existingPurchase) {
            return redirect()->route('course.show', $course)
                ->with('error', 'Anda sudah memiliki akses ke kursus ini.');
        }

        // Check if course meets minimum price
        if (!$course->meetsMinimumPrice()) {
            return redirect()->back()->with('error', 'Harga kursus tidak valid.');
        }

        try {
            DB::beginTransaction();

            // Check for referral
            $referrer = null;
            $hasReferral = false;
            if ($request->referral_code) {
                $referrer = User::where('referral_code', $request->referral_code)->first();
                if ($referrer && $referrer->id !== $user->id) {
                    $hasReferral = true;
                }
            }

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payment_type' => 'course',
                'payable_id' => $course->id,
                'payable_type' => Course::class,
                'transaction_id' => 'TXN-' . time() . '-' . $user->id,
                'amount' => $course->price,
                'platform_fee' => $course->calculatePlatformFee(),
                'tutor_earnings' => $course->calculateTutorEarnings($hasReferral),
                'currency' => 'IDR',
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'referrer_id' => $referrer?->id,
                'has_referral_bonus' => $hasReferral,
            ]);

            // Create course purchase record
            $coursePurchase = CoursePurchase::create([
                'user_id' => $user->id,
                'course_id' => $course->id,
                'payment_id' => $payment->id,
                'status' => 'active',
                'amount_paid' => $course->price,
                'purchased_at' => now(),
                'referrer_id' => $referrer?->id,
                'has_referral_bonus' => $hasReferral,
            ]);

            DB::commit();

            // TODO: Integrate with payment gateway (Midtrans, Xendit, etc.)
            // For now, we'll simulate successful payment
            $this->simulatePaymentSuccess($payment);

            return redirect()->route('course.learn', $course)
                ->with('success', 'Pembayaran berhasil! Anda sekarang dapat mengakses kursus ini.');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Terjadi kesalahan saat memproses pembayaran: ' . $e->getMessage());
        }
    }

    /**
     * Show user membership page.
     */
    public function userMembership()
    {
        $user = Auth::user();
        $activeMembership = $user->activeMembership;
        $membershipHistory = UserMembership::where('user_id', $user->id)
            ->with('membershipPlan', 'payment')
            ->latest()
            ->get();

        return view('user.membership.index', compact('activeMembership', 'membershipHistory'));
    }

    /**
     * Simulate payment success (for development).
     */
    private function simulatePaymentSuccess($payment, $userMembership = null)
    {
        $payment->markAsCompleted();

        if ($userMembership) {
            $userMembership->update(['status' => 'active']);

            // Update user's current membership info
            $userMembership->user->update([
                'current_membership_id' => $userMembership->id,
                'membership_expires_at' => $userMembership->expires_at,
                'nala_prompts_remaining' => $userMembership->nala_prompts_remaining,
                'has_unlimited_nala' => $userMembership->has_unlimited_nala,
            ]);
        }
    }
}
