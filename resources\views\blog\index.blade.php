@extends('layouts.app')

@section('title', 'Blog Ngambiskuy - <PERSON><PERSON>s Karir & Tren Tech Indonesia')

@push('styles')
<style>
/* Blog Index Mobile-First Responsive Styles */
.blog-index-page {
    min-height: 100vh;
    background: #f8fafc;
}

/* Hero Section Responsive */
.blog-hero-section {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .blog-hero-section {
        padding: 4rem 0;
    }
}

.blog-hero-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .blog-hero-title {
        font-size: 2.5rem;
    }
}

@media (min-width: 1024px) {
    .blog-hero-title {
        font-size: 3rem;
    }
}

.blog-hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
    color: rgba(219, 234, 254, 0.9);
    max-width: 48rem;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .blog-hero-subtitle {
        font-size: 1.125rem;
    }
}

@media (min-width: 1024px) {
    .blog-hero-subtitle {
        font-size: 1.25rem;
    }
}

/* Search & Filter Section */
.blog-search-section {
    padding: 1.5rem 0;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

@media (min-width: 768px) {
    .blog-search-section {
        padding: 2rem 0;
    }
}

.blog-search-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
}

@media (min-width: 768px) {
    .blog-search-container {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 1.5rem;
    }
}

.blog-search-form {
    flex: 1;
    max-width: 100%;
}

@media (min-width: 768px) {
    .blog-search-form {
        max-width: 28rem;
    }
}

.blog-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    min-height: 44px;
    transition: all 0.2s ease;
}

.blog-search-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

.blog-categories-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

@media (min-width: 768px) {
    .blog-categories-container {
        justify-content: flex-end;
    }
}

.blog-category-btn {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    text-decoration: none;
    white-space: nowrap;
}

.blog-category-btn.active {
    background-color: #2563eb;
    color: white;
}

.blog-category-btn:not(.active) {
    background-color: #f3f4f6;
    color: #374151;
}

.blog-category-btn:not(.active):hover {
    background-color: #e5e7eb;
}

/* Featured Post Section */
.blog-featured-section {
    padding: 2rem 0;
    background: white;
}

@media (min-width: 768px) {
    .blog-featured-section {
        padding: 3rem 0;
    }
}

.blog-featured-card {
    background: linear-gradient(135deg, #eff6ff 0%, #f3e8ff 100%);
    border-radius: 1rem;
    overflow: hidden;
}

.blog-featured-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
    align-items: center;
}

@media (min-width: 1024px) {
    .blog-featured-grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

.blog-featured-content {
    padding: 2rem;
}

@media (min-width: 768px) {
    .blog-featured-content {
        padding: 2.5rem;
    }
}

@media (min-width: 1024px) {
    .blog-featured-content {
        padding: 3rem;
    }
}

.blog-featured-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.blog-featured-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-weight: 500;
}

.blog-featured-badge-primary {
    background-color: #2563eb;
    color: white;
}

.blog-featured-badge-secondary {
    background-color: #f3f4f6;
    color: #374151;
}

.blog-featured-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1rem;
    line-height: 1.3;
}

@media (min-width: 768px) {
    .blog-featured-title {
        font-size: 1.75rem;
    }
}

@media (min-width: 1024px) {
    .blog-featured-title {
        font-size: 1.875rem;
    }
}

.blog-featured-link {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-featured-link:hover {
    color: #2563eb;
}

.blog-featured-excerpt {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-featured-author {
    margin-bottom: 1.5rem;
}

.blog-featured-author-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.blog-featured-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    object-fit: cover;
}

.blog-featured-author-name {
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.25rem;
}

.blog-featured-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.blog-featured-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: background-color 0.2s ease;
    min-height: 44px;
}

.blog-featured-btn:hover {
    background-color: #1d4ed8;
}

.blog-featured-image {
    position: relative;
    height: 16rem;
    order: -1;
}

@media (min-width: 1024px) {
    .blog-featured-image {
        height: 100%;
        order: 0;
    }
}

.blog-featured-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Blog Posts Grid */
.blog-posts-section {
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .blog-posts-section {
        padding: 3rem 0;
    }
}

.blog-posts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .blog-posts-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .blog-posts-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.blog-post-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.blog-post-card:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.blog-post-image-container {
    position: relative;
}

.blog-post-image {
    width: 100%;
    height: 12rem;
    object-fit: cover;
}

.blog-post-category {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background-color: #f3f4f6;
    color: #374151;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
}

.blog-post-content {
    padding: 1.5rem;
}

.blog-post-content-inner {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.blog-post-title {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-post-title-link {
    color: #111827;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-post-title-link:hover {
    color: #2563eb;
}

.blog-post-excerpt {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-post-author {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.blog-post-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.blog-post-author-info {
    flex: 1;
}

.blog-post-author-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.125rem;
}

.blog-post-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.blog-post-actions {
    padding-top: 0.75rem;
    border-top: 1px solid #f3f4f6;
}

.blog-post-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

@media (min-width: 768px) {
    .blog-post-buttons {
        flex-direction: row;
        gap: 0.5rem;
    }
}

.blog-post-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    border: none;
    cursor: pointer;
}

.blog-post-btn-full {
    width: 100%;
}

.blog-post-btn-save {
    background-color: #2563eb;
    color: white;
    flex: 1;
}

.blog-post-btn-save:hover {
    background-color: #1d4ed8;
}

.blog-post-btn-saved {
    background-color: #059669;
    color: white;
    flex: 1;
}

.blog-post-btn-saved:hover {
    background-color: #047857;
}

.blog-post-btn-read {
    border: 1px solid #d1d5db;
    color: #374151;
    background-color: white;
    flex: 1;
}

.blog-post-btn-read:hover {
    background-color: #f9fafb;
}

.blog-post-btn-primary {
    background-color: #2563eb;
    color: white;
}

.blog-post-btn-primary:hover {
    background-color: #1d4ed8;
}

.blog-pagination {
    margin-top: 3rem;
}

.blog-empty-state {
    text-align: center;
    padding: 3rem 0;
}

.blog-empty-icon {
    margin: 0 auto;
    height: 3rem;
    width: 3rem;
    color: #9ca3af;
}

.blog-empty-title {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
}

.blog-empty-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
}
</style>
@endpush

@section('content')
<div class="blog-index-page">
    <!-- Hero Section -->
    <section class="blog-hero-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="blog-hero-title">Blog Ngambiskuy Advance Learning Assistance</h1>
                <p class="blog-hero-subtitle">
                    Tetap update dengan tren tech terbaru, tips karir, dan kisah sukses dari komunitas tech Indonesia
                </p>
            </div>
        </div>
    </section>

    <!-- Search & Filter Section -->
    <section class="blog-search-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="blog-search-container">
                <!-- Search -->
                <form method="GET" class="blog-search-form">
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="Cari artikel..."
                               class="blog-search-input">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </form>

                <!-- Categories Filter -->
                <div class="blog-categories-container">
                    <a href="{{ route('blog.index') }}"
                       class="blog-category-btn {{ !request('category') ? 'active' : '' }}">
                        Semua
                    </a>
                    @foreach($categories as $category)
                    <a href="{{ route('blog.index', ['category' => $category->id]) }}"
                       class="blog-category-btn {{ request('category') == $category->id ? 'active' : '' }}">
                        {{ $category->name }}
                    </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Post -->
    @if($featuredPost && !request('search') && !request('category'))
    <section class="blog-featured-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="blog-featured-card">
                <div class="blog-featured-grid">
                    <div class="blog-featured-content">
                        <div class="blog-featured-badges">
                            <span class="blog-featured-badge blog-featured-badge-primary">Featured</span>
                            @if($featuredPost->category)
                            <span class="blog-featured-badge blog-featured-badge-secondary">{{ $featuredPost->category->name }}</span>
                            @endif
                        </div>

                        <h2 class="blog-featured-title">
                            <a href="{{ route('blog.show', $featuredPost->slug) }}" class="blog-featured-link">
                                {{ $featuredPost->title }}
                            </a>
                        </h2>

                        <p class="blog-featured-excerpt">{{ $featuredPost->excerpt }}</p>

                        <div class="blog-featured-author">
                            <div class="blog-featured-author-info">
                                <img src="{{ $featuredPost->author->profile_picture ? asset('storage/' . $featuredPost->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                     alt="{{ $featuredPost->author->name }}" class="blog-featured-avatar">
                                <div>
                                    <p class="blog-featured-author-name">{{ $featuredPost->author->name }}</p>
                                    <div class="blog-featured-meta">
                                        <span>{{ $featuredPost->formatted_published_date }}</span>
                                        <span>•</span>
                                        <span>{{ $featuredPost->read_time_text }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ route('blog.show', $featuredPost->slug) }}"
                           class="blog-featured-btn">
                            Baca Selengkapnya
                            <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <div class="blog-featured-image">
                        <img src="{{ $featuredPost->featured_image ? asset('storage/' . $featuredPost->featured_image) : asset('images/blog/placeholder.svg') }}"
                             alt="{{ $featuredPost->title }}"
                             class="blog-featured-img">
                    </div>
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Blog Posts Grid -->
    <section class="blog-posts-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($blogPosts->count() > 0)
                <div class="blog-posts-grid">
                    @foreach($blogPosts as $post)
                    <article class="blog-post-card">
                        <div class="blog-post-image-container">
                            <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg') }}"
                                 alt="{{ $post->title }}" class="blog-post-image">
                            @if($post->category)
                            <span class="blog-post-category">{{ $post->category->name }}</span>
                            @endif
                        </div>

                        <div class="blog-post-content">
                            <div class="blog-post-content-inner">
                                <h3 class="blog-post-title">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="blog-post-title-link">
                                        {{ $post->title }}
                                    </a>
                                </h3>

                                <p class="blog-post-excerpt">{{ $post->excerpt }}</p>

                                <div class="blog-post-author">
                                    <img src="{{ $post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                         alt="{{ $post->author->name }}" class="blog-post-avatar">
                                    <div class="blog-post-author-info">
                                        <p class="blog-post-author-name">{{ $post->author->name }}</p>
                                        <div class="blog-post-meta">
                                            <span>{{ $post->formatted_published_date }}</span>
                                            <span>•</span>
                                            <span>{{ $post->read_time_text }}</span>
                                        </div>
                                    </div>
                                </div>

                                @auth
                                <div class="blog-post-actions">
                                    @php
                                        $isSaved = auth()->user()->savedBlogPosts()->where('blog_posts.id', $post->id)->exists();
                                    @endphp
                                    <div class="blog-post-buttons">
                                        @if($isSaved)
                                            <button onclick="unsaveArticle('{{ $post->slug }}')" class="blog-post-btn blog-post-btn-saved">
                                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                Tersimpan
                                            </button>
                                        @else
                                            <button onclick="saveArticle('{{ $post->slug }}')" class="blog-post-btn blog-post-btn-save">
                                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                                </svg>
                                                Simpan
                                            </button>
                                        @endif
                                        <a href="{{ route('blog.show', $post->slug) }}" class="blog-post-btn blog-post-btn-read">
                                            Baca
                                        </a>
                                    </div>
                                </div>
                                @else
                                <div class="blog-post-actions">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="blog-post-btn blog-post-btn-primary blog-post-btn-full">
                                        Baca Artikel
                                    </a>
                                </div>
                                @endauth
                            </div>
                        </div>
                    </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="blog-pagination">
                    {{ $blogPosts->links() }}
                </div>
            @else
                <div class="blog-empty-state">
                    <svg class="blog-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="blog-empty-title">Tidak ada artikel</h3>
                    <p class="blog-empty-text">
                        {{ request('search') ? 'Tidak ada artikel yang sesuai dengan pencarian Anda.' : 'Belum ada artikel yang dipublikasikan.' }}
                    </p>
                </div>
            @endif
        </div>
    </section>
</div>

@auth
<script>
async function saveArticle(articleSlug) {
    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Artikel berhasil disimpan!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menyimpan artikel', 'error');
        }
    } catch (error) {
        console.error('Error saving article:', error);
        showNotification('Terjadi kesalahan saat menyimpan artikel', 'error');
    }
}

async function unsaveArticle(articleSlug) {
    if (!confirm('Apakah Anda yakin ingin menghapus artikel ini dari daftar simpan?')) {
        return;
    }

    try {
        const response = await fetch(`/blog/${articleSlug}/save`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification('Artikel berhasil dihapus dari daftar simpan!', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Gagal menghapus artikel', 'error');
        }
    } catch (error) {
        console.error('Error unsaving article:', error);
        showNotification('Terjadi kesalahan saat menghapus artikel', 'error');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' : 'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}
</script>
@endauth

@endsection
