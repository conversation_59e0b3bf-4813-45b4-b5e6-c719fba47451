<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use Illuminate\Support\Str;

class NalaChatHistoryTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $otherUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        
        $this->otherUser = User::factory()->create([
            'name' => 'Other User',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function authenticated_user_can_get_chat_history()
    {
        // Create a conversation with messages
        $conversation = $this->createConversationWithMessages($this->user->id, 5);

        $response = $this->actingAs($this->user)
            ->getJson('/api/nala-chat/history');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'conversations' => [
                    'data' => [
                        '*' => [
                            'id',
                            'title',
                            'message_preview',
                            'total_messages',
                            'last_message_at',
                            'started_context'
                        ]
                    ]
                ]
            ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_chat_history()
    {
        $response = $this->getJson('/api/nala-chat/history');

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Authentication required'
            ]);
    }

    /** @test */
    public function user_can_only_access_their_own_conversations()
    {
        // Create conversations for both users
        $userConversation = $this->createConversationWithMessages($this->user->id, 3);
        $otherConversation = $this->createConversationWithMessages($this->otherUser->id, 3);

        $response = $this->actingAs($this->user)
            ->getJson('/api/nala-chat/history');

        $response->assertStatus(200);
        
        $conversations = $response->json('conversations.data');
        
        // Should only see own conversation
        $this->assertCount(1, $conversations);
        $this->assertEquals($userConversation->id, $conversations[0]['id']);
    }

    /** @test */
    public function user_can_get_specific_conversation_with_messages()
    {
        $conversation = $this->createConversationWithMessages($this->user->id, 10);

        $response = $this->actingAs($this->user)
            ->getJson("/api/nala-chat/conversation/{$conversation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ])
            ->assertJsonStructure([
                'conversation' => [
                    'id',
                    'title',
                    'started_route',
                    'started_context',
                    'message_count',
                    'last_message_at',
                    'messages' => [
                        '*' => [
                            'id',
                            'sender',
                            'content',
                            'created_at'
                        ]
                    ]
                ]
            ]);

        $messages = $response->json('conversation.messages');
        $this->assertCount(10, $messages);
    }

    /** @test */
    public function user_cannot_access_other_users_conversations()
    {
        $otherConversation = $this->createConversationWithMessages($this->otherUser->id, 3);

        $response = $this->actingAs($this->user)
            ->getJson("/api/nala-chat/conversation/{$otherConversation->id}");

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Conversation not found or access denied'
            ]);
    }

    /** @test */
    public function user_can_delete_their_own_conversation()
    {
        $conversation = $this->createConversationWithMessages($this->user->id, 5);

        $response = $this->actingAs($this->user)
            ->deleteJson("/api/nala-chat/conversation/{$conversation->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Conversation deleted successfully'
            ]);

        // Verify conversation is soft deleted
        $this->assertDatabaseHas('nala_chat_conversations', [
            'id' => $conversation->id,
            'status' => 'deleted'
        ]);
    }

    /** @test */
    public function user_can_clear_all_chat_history()
    {
        // Create multiple conversations
        $this->createConversationWithMessages($this->user->id, 3);
        $this->createConversationWithMessages($this->user->id, 5);

        $response = $this->actingAs($this->user)
            ->deleteJson('/api/nala-chat/clear-history');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Chat history cleared successfully'
            ]);

        // Verify all conversations are soft deleted
        $activeConversations = NalaChatConversation::forUser($this->user->id)
            ->active()
            ->count();
            
        $this->assertEquals(0, $activeConversations);
    }

    /** @test */
    public function conversation_maintains_message_limit()
    {
        $conversation = NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => $this->user->id,
            'started_route' => 'test',
            'started_context' => 'test'
        ]);

        // Create 35 messages (exceeds limit of 30)
        for ($i = 1; $i <= 35; $i++) {
            NalaChatMessage::create([
                'id' => Str::uuid(),
                'conversation_id' => $conversation->id,
                'sender' => $i % 2 === 0 ? 'user' : 'ai',
                'content' => "Test message {$i}",
                'status' => 'sent'
            ]);
        }

        // Trigger message limit maintenance
        $conversation->maintainMessageLimit(30);

        // Should have exactly 30 active messages
        $activeMessages = $conversation->messages()
            ->where('status', '!=', 'deleted')
            ->count();
            
        $this->assertEquals(30, $activeMessages);
    }

    /**
     * Helper method to create a conversation with messages
     */
    private function createConversationWithMessages(string $userId, int $messageCount): NalaChatConversation
    {
        $conversation = NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => $userId,
            'title' => 'Test Conversation',
            'started_route' => 'test.route',
            'started_context' => 'test_context',
            'status' => 'active'
        ]);

        for ($i = 1; $i <= $messageCount; $i++) {
            NalaChatMessage::create([
                'id' => Str::uuid(),
                'conversation_id' => $conversation->id,
                'sender' => $i % 2 === 0 ? 'user' : 'ai',
                'content' => "Test message {$i}",
                'status' => 'sent'
            ]);
        }

        $conversation->updateStats();
        
        return $conversation;
    }
}
