<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Role;

class AssignTutorRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'roles:assign-tutors {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign tutor roles to users who have tutor profiles but missing tutor role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('DRY RUN MODE - No changes will be made');
            $this->newLine();
        }

        // Find users with tutor profiles but without tutor role
        $usersWithTutorProfiles = User::whereHas('tutorProfile')
            ->whereDoesntHave('roles', function($query) {
                $query->where('name', Role::TUTOR);
            })
            ->get();

        if ($usersWithTutorProfiles->isEmpty()) {
            $this->info('✅ All users with tutor profiles already have tutor role assigned.');
            return;
        }

        $this->info('Found ' . $usersWithTutorProfiles->count() . ' users with tutor profiles missing tutor role:');
        $this->newLine();

        foreach ($usersWithTutorProfiles as $user) {
            $this->line("👤 {$user->name} ({$user->email})");
            $currentRoles = $user->roles->pluck('name')->toArray();
            $this->line("   Current roles: " . (empty($currentRoles) ? 'None' : implode(', ', $currentRoles)));
            
            if (!$dryRun) {
                // Ensure user has basic user role
                if (!$user->hasRole(Role::USER)) {
                    $user->assignRole(Role::USER);
                }
                
                // Assign tutor role
                $user->assignRole(Role::TUTOR);
                
                $this->line("   ✅ Assigned tutor role");
            } else {
                $this->line("   🔄 Would assign tutor role");
            }
            
            $this->newLine();
        }

        if (!$dryRun) {
            $this->info('✅ Successfully assigned tutor roles to ' . $usersWithTutorProfiles->count() . ' users.');
        } else {
            $this->info('🔍 Dry run completed. Use without --dry-run to make actual changes.');
        }
    }
}
