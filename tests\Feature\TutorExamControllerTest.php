<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;
use App\Models\Exam;
use App\Models\Payment;
use App\Models\Category;
use App\Models\MembershipPlan;

class TutorExamControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'user']);
        Role::create(['name' => 'tutor']);
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'superadmin']);
    }

    /** @test */
    public function tutor_can_access_exam_index_page_without_database_error()
    {
        // Create a tutor user
        $tutor = User::factory()->create();
        $tutor->assignRole('tutor');

        // Create a category for the exam
        $category = Category::factory()->create();

        // Create an exam for the tutor
        $exam = Exam::create([
            'tutor_id' => $tutor->id,
            'category_id' => $category->id,
            'title' => 'Test Exam',
            'description' => 'Test exam description',
            'price' => 50000,
            'time_limit' => 60,
            'max_attempts' => 3,
            'passing_score' => 70,
            'difficulty_level' => 'beginner',
            'is_published' => true,
        ]);

        // Create a membership plan (this was causing the original error)
        $membershipPlan = MembershipPlan::create([
            'name' => 'Standard',
            'slug' => 'standard',
            'description' => 'Standard plan',
            'price' => 100000,
            'is_free' => false,
            'is_active' => true,
        ]);

        // Create a payment for the membership (this should NOT affect exam revenue)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'membership',
            'payable_type' => MembershipPlan::class,
            'payable_id' => $membershipPlan->id,
            'transaction_id' => 'TXN-MEMBERSHIP-' . time(),
            'amount' => 100000,
            'currency' => 'IDR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // Create a payment for the exam (this SHOULD be counted in exam revenue)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'exam',
            'payable_type' => Exam::class,
            'payable_id' => $exam->id,
            'transaction_id' => 'TXN-EXAM-' . time(),
            'amount' => 50000,
            'currency' => 'IDR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // Access the tutor exam index page
        $response = $this->actingAs($tutor)->get('/tutor/exams');

        // Should not get a database error and should return 200
        $response->assertStatus(200);

        // Should contain the exam statistics
        $response->assertViewHas('stats');

        // Verify the stats contain the expected data
        $stats = $response->viewData('stats');
        $this->assertEquals(1, $stats['total_exams']);
        $this->assertEquals(1, $stats['published_exams']);
        $this->assertEquals(50000, $stats['total_revenue']); // Only exam payment should be counted
    }

    /** @test */
    public function exam_revenue_calculation_only_includes_exam_payments()
    {
        // Create a tutor user
        $tutor = User::factory()->create();
        $tutor->assignRole('tutor');
        
        // Create another tutor to ensure we only get payments for the correct tutor
        $otherTutor = User::factory()->create();
        $otherTutor->assignRole('tutor');
        
        // Create categories
        $category = Category::factory()->create();
        
        // Create exams for both tutors
        $tutorExam = Exam::create([
            'tutor_id' => $tutor->id,
            'category_id' => $category->id,
            'title' => 'Tutor Exam',
            'description' => 'Exam by main tutor',
            'price' => 75000,
            'time_limit' => 60,
            'max_attempts' => 3,
            'passing_score' => 70,
            'difficulty_level' => 'beginner',
            'is_published' => true,
        ]);

        $otherTutorExam = Exam::create([
            'tutor_id' => $otherTutor->id,
            'category_id' => $category->id,
            'title' => 'Other Tutor Exam',
            'description' => 'Exam by other tutor',
            'price' => 60000,
            'time_limit' => 60,
            'max_attempts' => 3,
            'passing_score' => 70,
            'difficulty_level' => 'beginner',
            'is_published' => true,
        ]);

        // Create a membership plan
        $membershipPlan = MembershipPlan::create([
            'name' => 'Test Plan',
            'slug' => 'test-plan',
            'description' => 'Test membership plan',
            'price' => 100000,
            'is_free' => false,
            'is_active' => true,
        ]);
        
        // Create various payments
        // 1. Exam payment for our tutor (should be counted)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'exam',
            'payable_type' => Exam::class,
            'payable_id' => $tutorExam->id,
            'transaction_id' => 'TXN-EXAM-1-' . time(),
            'amount' => 75000,
            'currency' => 'IDR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // 2. Exam payment for other tutor (should NOT be counted)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'exam',
            'payable_type' => Exam::class,
            'payable_id' => $otherTutorExam->id,
            'transaction_id' => 'TXN-EXAM-2-' . time(),
            'amount' => 60000,
            'currency' => 'IDR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // 3. Membership payment (should NOT be counted)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'membership',
            'payable_type' => MembershipPlan::class,
            'payable_id' => $membershipPlan->id,
            'transaction_id' => 'TXN-MEMBERSHIP-' . time(),
            'amount' => 100000,
            'currency' => 'IDR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        // 4. Pending exam payment for our tutor (should NOT be counted)
        Payment::create([
            'user_id' => User::factory()->create()->id,
            'payment_type' => 'exam',
            'payable_type' => Exam::class,
            'payable_id' => $tutorExam->id,
            'transaction_id' => 'TXN-EXAM-PENDING-' . time(),
            'amount' => 75000,
            'currency' => 'IDR',
            'status' => 'pending',
        ]);
        
        // Access the tutor exam index page
        $response = $this->actingAs($tutor)->get('/tutor/exams');
        
        $response->assertStatus(200);
        
        // Verify only the correct exam payment is counted
        $stats = $response->viewData('stats');
        $this->assertEquals(75000, $stats['total_revenue']); // Only completed exam payment for this tutor
    }
}
