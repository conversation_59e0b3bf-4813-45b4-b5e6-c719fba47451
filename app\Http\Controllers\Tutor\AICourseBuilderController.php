<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\Category;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Services\NalaPromptTrackingService;

class AICourseBuilderController extends Controller
{
    private $geminiApiKey;
    private $geminiModel = 'gemini-1.5-flash';
    protected $promptTrackingService;

    public function __construct(NalaPromptTrackingService $promptTrackingService)
    {
        $this->promptTrackingService = $promptTrackingService;
        $this->geminiApiKey = config('services.gemini.api_key');
    }

    /**
     * Generate course suggestions using AI
     */
    public function generateCourseSuggestion(Request $request)
    {
        try {
            // Check if user has active membership and can use prompts
            $user = Auth::user();

            // Course Builder requires membership (no free access)
            if (!$user->hasActiveMembership()) {
                return response()->json([
                    'success' => false,
                    'error' => 'membership_required',
                    'message' => 'Fitur Nala AI Course Builder memerlukan membership aktif. Upgrade sekarang untuk mengakses AI assistant!',
                    'upgrade_url' => route('payment.pricing')
                ], 403);
            }

            // Check if user has remaining prompts for today
            if (!$this->promptTrackingService->canUsePrompts($user, 1)) {
                $membershipLevel = $this->promptTrackingService->getUserMembershipLevel($user);
                $limitMessage = $this->promptTrackingService->getMembershipLimitResponse($membershipLevel);

                return response()->json([
                    'success' => false,
                    'error' => 'daily_limit_reached',
                    'message' => $limitMessage,
                    'upgrade_url' => route('payment.pricing')
                ], 429);
            }

            // Get available categories
            $categories = Category::where('is_active', true)
                ->orderBy('sort_order')
                ->get(['id', 'name', 'slug']);

            // Generate course suggestion using AI
            $courseSuggestion = $this->generateAICourseSuggestion($categories, $user);

            return response()->json([
                'success' => true,
                'course_suggestion' => $courseSuggestion
            ]);

        } catch (\Exception $e) {
            Log::error('AI Course Builder Error: ' . $e->getMessage());
            Log::error('AI Course Builder Stack Trace: ' . $e->getTraceAsString());

            // Fallback to predefined suggestions if AI fails
            $fallbackSuggestion = $this->getFallbackCourseSuggestion();

            return response()->json([
                'success' => true,
                'course_suggestion' => $fallbackSuggestion,
                'is_fallback' => true,
                'error_message' => $e->getMessage() // For debugging
            ]);
        }
    }

    /**
     * Generate AI course suggestion
     */
    private function generateAICourseSuggestion($categories, $user)
    {
        $categoryList = $categories->pluck('name')->implode(', ');
        
        $systemPrompt = "Anda adalah AI assistant yang membantu tutor membuat kursus teknologi berkualitas tinggi.

KONTEKS PLATFORM:
Ngambiskuy adalah platform edukasi teknologi Indonesia yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

TUGAS:
Buatkan saran kursus yang menarik, praktis, dan sesuai dengan tren industri tech 2024.

KATEGORI TERSEDIA: {$categoryList}

FORMAT RESPONSE (JSON):
{
    \"title\": \"Judul kursus yang menarik dan spesifik\",
    \"category\": \"Nama kategori yang sesuai dari daftar di atas\",
    \"description\": \"Deskripsi 100-150 kata yang menjelaskan manfaat konkret\",
    \"level\": \"beginner/intermediate/advanced\",
    \"duration\": \"Durasi dalam jam (20-100)\",
    \"price\": \"Harga dalam rupiah (************, kelipatan 1000)\",
    \"is_free\": false,
    \"learning_outcomes\": [\"Outcome 1\", \"Outcome 2\", \"Outcome 3\"],
    \"target_audience\": [\"Target 1\", \"Target 2\", \"Target 3\"],
    \"requirements\": [\"Requirement 1\", \"Requirement 2\"]
}

PENTING:
- Judul harus spesifik dan menarik (contoh: 'Membangun REST API dengan Laravel untuk E-commerce')
- Deskripsi harus fokus pada manfaat praktis dan hasil yang bisa dicapai
- Learning outcomes harus konkret dan terukur
- Harga sesuai dengan kompleksitas dan durasi
- Semua konten harus dalam Bahasa Indonesia
- Fokus pada skill yang dibutuhkan industri tech Indonesia";

        $userPrompt = "Buatkan saran kursus teknologi yang menarik dan sesuai dengan tren industri 2024. Pastikan kursus ini praktis dan memberikan value tinggi untuk siswa.";

        $response = $this->callGeminiAPI($systemPrompt, $userPrompt);

        // Use prompt from user's daily allocation
        $this->promptTrackingService->usePrompts($user, 1);

        // Clean and parse JSON response (remove markdown code blocks if present)
        $cleanResponse = $this->cleanJsonResponse($response);
        $courseData = json_decode($cleanResponse, true);

        if (!$courseData) {
            throw new \Exception('Invalid JSON response from AI: ' . $cleanResponse);
        }

        // Validate and clean the response
        return $this->validateAndCleanCourseData($courseData, $categories);
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 1024,
            ]
        ];

        $response = Http::timeout(20)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Clean JSON response by removing markdown code blocks
     */
    private function cleanJsonResponse($response)
    {
        // Remove markdown code blocks (```json ... ```)
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);
        $response = preg_replace('/```/', '', $response);

        // Remove any leading/trailing whitespace
        $response = trim($response);

        return $response;
    }

    /**
     * Validate and clean course data
     */
    private function validateAndCleanCourseData($courseData, $categories)
    {
        // Find matching category
        $categoryName = $courseData['category'] ?? '';
        $matchingCategory = $categories->firstWhere('name', $categoryName);
        
        if (!$matchingCategory) {
            // Try to find by partial match
            $matchingCategory = $categories->first(function ($cat) use ($categoryName) {
                return stripos($cat->name, $categoryName) !== false || 
                       stripos($categoryName, $cat->name) !== false;
            });
        }

        // Use first category as fallback
        if (!$matchingCategory) {
            $matchingCategory = $categories->first();
        }

        // Clean and validate data
        return [
            'title' => substr($courseData['title'] ?? 'Kursus Teknologi Terbaru', 0, 255),
            'category_id' => $matchingCategory->id,
            'category_name' => $matchingCategory->name,
            'description' => substr($courseData['description'] ?? 'Deskripsi kursus akan diisi di sini.', 0, 1000),
            'level' => in_array($courseData['level'] ?? 'beginner', ['beginner', 'intermediate', 'advanced']) 
                      ? $courseData['level'] : 'beginner',
            'duration' => max(1, min(500, intval($courseData['duration'] ?? 20))),
            'price' => $this->validatePrice($courseData['price'] ?? 50000),
            'is_free' => $courseData['is_free'] ?? false,
            'learning_outcomes' => array_slice($courseData['learning_outcomes'] ?? [], 0, 5),
            'target_audience' => array_slice($courseData['target_audience'] ?? [], 0, 5),
            'requirements' => array_slice($courseData['requirements'] ?? [], 0, 5)
        ];
    }

    /**
     * Validate price (must be multiple of 1000, min 30000)
     */
    private function validatePrice($price)
    {
        $price = intval($price);
        
        if ($price < 30000) {
            $price = 30000;
        }
        
        // Round to nearest 1000
        $price = round($price / 1000) * 1000;
        
        return min(1000000, $price); // Max 1 million
    }

    /**
     * Get fallback course suggestion when AI fails
     */
    private function getFallbackCourseSuggestion()
    {
        $fallbackSuggestions = [
            [
                'title' => 'Membangun Website Modern dengan React dan Laravel',
                'category_name' => 'Web Development',
                'description' => 'Pelajari cara membangun aplikasi web full-stack modern menggunakan React untuk frontend dan Laravel untuk backend. Kursus ini akan mengajarkan Anda dari dasar hingga deployment aplikasi yang siap production.',
                'level' => 'intermediate',
                'duration' => 40,
                'price' => 150000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Membangun REST API dengan Laravel',
                    'Membuat UI responsif dengan React',
                    'Implementasi autentikasi dan authorization',
                    'Deploy aplikasi ke production'
                ],
                'target_audience' => [
                    'Developer yang ingin upgrade skill',
                    'Fresh graduate IT',
                    'Freelancer web developer'
                ],
                'requirements' => [
                    'Dasar HTML, CSS, JavaScript',
                    'Familiar dengan PHP',
                    'Pengalaman dengan database MySQL'
                ]
            ],
            [
                'title' => 'Data Science untuk Pemula dengan Python',
                'category_name' => 'Data Science',
                'description' => 'Mulai karir Anda di bidang Data Science dengan mempelajari Python, pandas, dan machine learning. Kursus praktis dengan project real-world yang akan membantu Anda memahami analisis data dari nol.',
                'level' => 'beginner',
                'duration' => 35,
                'price' => 120000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Menguasai Python untuk data analysis',
                    'Membuat visualisasi data yang menarik',
                    'Membangun model machine learning sederhana',
                    'Menganalisis dataset real-world'
                ],
                'target_audience' => [
                    'Pemula yang ingin masuk ke Data Science',
                    'Professional yang ingin pivot karir',
                    'Mahasiswa IT dan statistik'
                ],
                'requirements' => [
                    'Dasar matematika dan statistik',
                    'Tidak perlu pengalaman programming',
                    'Laptop dengan spesifikasi minimal'
                ]
            ],
            [
                'title' => 'Mobile App Development dengan Flutter',
                'category_name' => 'Mobile Development',
                'description' => 'Belajar membuat aplikasi mobile cross-platform dengan Flutter. Dari UI design hingga integrasi API, kursus ini akan membekali Anda skill untuk menjadi mobile developer profesional.',
                'level' => 'intermediate',
                'duration' => 45,
                'price' => 180000,
                'is_free' => false,
                'learning_outcomes' => [
                    'Membangun aplikasi Android dan iOS dengan satu codebase',
                    'Implementasi state management yang efisien',
                    'Integrasi dengan REST API dan database',
                    'Publish aplikasi ke Play Store dan App Store'
                ],
                'target_audience' => [
                    'Web developer yang ingin masuk mobile',
                    'Fresh graduate yang ingin spesialisasi mobile',
                    'Entrepreneur yang ingin membuat aplikasi sendiri'
                ],
                'requirements' => [
                    'Dasar programming (any language)',
                    'Familiar dengan konsep OOP',
                    'Pengalaman dengan Git'
                ]
            ]
        ];

        // Get random suggestion
        $randomIndex = array_rand($fallbackSuggestions);
        $suggestion = $fallbackSuggestions[$randomIndex];

        // Find matching category ID
        $category = Category::where('name', $suggestion['category_name'])->first();
        if ($category) {
            $suggestion['category_id'] = $category->id;
        }

        return $suggestion;
    }

    /**
     * Generate material suggestions using AI based on chapter context
     */
    public function generateMaterialSuggestion(Request $request, $courseSlug, $chapterSlug)
    {
        try {
            // Find course and chapter manually
            $course = Course::where('slug', $courseSlug)->firstOrFail();
            $chapter = $course->chapters()->where('slug', $chapterSlug)->firstOrFail();

            // Ensure the course belongs to the authenticated tutor
            if ($course->tutor_id !== Auth::id()) {
                abort(403, 'Unauthorized access to course curriculum.');
            }

            // Check if user has active membership and can use prompts
            $user = Auth::user();
            if (!$user->hasActiveMembership()) {
                return response()->json([
                    'success' => false,
                    'error' => 'membership_required',
                    'message' => 'Fitur Nala AI Course Builder memerlukan membership aktif. Upgrade sekarang untuk mengakses AI assistant!',
                    'upgrade_url' => route('payment.pricing')
                ], 403);
            }

            // Check if user has remaining prompts for today
            if (!$this->promptTrackingService->canUsePrompts($user, 1)) {
                $membershipLevel = $this->promptTrackingService->getUserMembershipLevel($user);
                $limitMessage = $this->promptTrackingService->getMembershipLimitResponse($membershipLevel);

                return response()->json([
                    'success' => false,
                    'error' => 'daily_limit_reached',
                    'message' => $limitMessage,
                    'upgrade_url' => route('payment.pricing')
                ], 429);
            }

            // Generate material suggestion using AI
            $materialSuggestion = $this->generateAIMaterialSuggestion($course, $chapter, $user);

            return response()->json([
                'success' => true,
                'material_suggestion' => $materialSuggestion
            ]);

        } catch (\Exception $e) {
            Log::error('AI Material Builder Error: ' . $e->getMessage());
            Log::error('AI Material Builder Stack Trace: ' . $e->getTraceAsString());

            // Fallback to predefined suggestions if AI fails
            $fallbackSuggestion = $this->getFallbackMaterialSuggestion($course, $chapter);

            return response()->json([
                'success' => true,
                'material_suggestion' => $fallbackSuggestion,
                'is_fallback' => true,
                'error_message' => $e->getMessage() // For debugging
            ]);
        }
    }

    /**
     * Generate AI material suggestion based on chapter context and existing materials
     */
    private function generateAIMaterialSuggestion($course, $chapter, $user)
    {
        // Fetch existing materials in this chapter
        $existingMaterials = $this->getExistingMaterialsInChapter($chapter);

        // Build existing materials context for AI
        $existingMaterialsContext = $this->buildExistingMaterialsContext($existingMaterials);

        // Get session-based previous suggestions to encourage variety
        $previousSuggestions = $this->getPreviousSessionSuggestions($course->id, $chapter->id);
        $variationContext = $this->buildVariationContext($previousSuggestions, $existingMaterials);

        $systemPrompt = "Anda adalah AI assistant yang membantu tutor membuat materi pembelajaran berkualitas tinggi.

KONTEKS KURSUS:
- Judul Kursus: {$course->title}
- Kategori: {$course->category->name}
- Level: {$course->level}
- Deskripsi: {$course->description}

KONTEKS BAB:
- Judul Bab: {$chapter->title}
- Deskripsi Bab: {$chapter->description}

{$existingMaterialsContext}

{$variationContext}

TUGAS ANDA:
Buatkan SATU saran materi pembelajaran yang sesuai dengan konteks bab dan kursus di atas. Materi harus:
1. Relevan dengan judul dan deskripsi bab
2. Sesuai dengan level kursus ({$course->level})
3. Mendukung tujuan pembelajaran kursus
4. Memiliki struktur yang jelas dan mudah dipahami
5. Durasi yang realistis (5-60 menit)
6. TIDAK DUPLIKAT dengan materi yang sudah ada
7. Melengkapi dan memperkaya pembelajaran dari materi yang sudah ada
8. Mengisi gap atau melanjutkan progression pembelajaran yang logis
9. BERBEDA dari saran sebelumnya - berikan variasi dan pendekatan yang fresh
10. Gunakan kreativitas untuk memberikan perspektif pembelajaran yang unik

FORMAT RESPONSE (JSON OBJECT TUNGGAL):
{
    \"title\": \"Judul materi yang spesifik dan menarik\",
    \"description\": \"Deskripsi singkat 50-100 kata tentang materi\",
    \"type\": \"video/text/quiz/assignment\",
    \"duration_minutes\": 25,
    \"content_outline\": \"Outline konten materi dalam format bullet points\"
}

PENTING:
- Response harus berupa SATU OBJECT JSON saja, bukan array
- Jangan gunakan markdown formatting (```json)
- Pastikan JSON valid dan dapat di-parse
- Fokus pada satu materi terbaik yang paling dibutuhkan";

        // Generate varied user prompt with randomization elements
        $randomSeed = time() . rand(1000, 9999);
        $creativityPrompts = [
            "Berikan pendekatan pembelajaran yang inovatif dan engaging",
            "Fokus pada aspek praktis yang langsung applicable",
            "Ciptakan materi yang mendorong critical thinking",
            "Berikan perspektif industry-relevant yang up-to-date",
            "Buat materi yang mendorong hands-on learning experience"
        ];
        $randomCreativityPrompt = $creativityPrompts[array_rand($creativityPrompts)];

        $userPrompt = "Buatkan saran materi untuk bab '{$chapter->title}' dalam kursus '{$course->title}'. {$randomCreativityPrompt}. Pastikan materi relevan, unik, dan memberikan value tambah yang berbeda dari sebelumnya. Random seed: {$randomSeed}";

        try {
            $response = Http::timeout(30)->post("https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}", [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $systemPrompt . "\n\n" . $userPrompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.9, // Increased for more creativity
                    'topK' => 50,         // Increased for more variety
                    'topP' => 0.98,       // Increased for more diverse outputs
                    'maxOutputTokens' => 1024,
                ]
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $content = $data['candidates'][0]['content']['parts'][0]['text'] ?? '';

                // Log the AI response for debugging
                Log::info('AI Material Builder Response', [
                    'course' => $course->title,
                    'chapter' => $chapter->title,
                    'raw_response' => $content
                ]);

                // Use prompt from user's daily allocation
                $this->promptTrackingService->usePrompts($user, 1);

                // Clean the response
                $content = trim($content);
                $content = preg_replace('/^```json\s*/', '', $content);
                $content = preg_replace('/\s*```$/', '', $content);

                $materialData = json_decode($content, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($materialData)) {
                    // Handle both single object and array of suggestions
                    $suggestion = $this->extractBestMaterialSuggestion($materialData, $existingMaterials);

                    if ($suggestion && $this->isValidAIMaterialData($suggestion)) {
                        $validatedSuggestion = $this->validateMaterialSuggestion($suggestion, $course, $chapter);

                        // Store this suggestion in session for future variation
                        $this->storeSuggestionInSession($course->id, $chapter->id, $validatedSuggestion);

                        return $validatedSuggestion;
                    } else {
                        Log::warning('AI Material Builder returned incomplete data', [
                            'material_data' => $materialData,
                            'extracted_suggestion' => $suggestion,
                            'course' => $course->title,
                            'chapter' => $chapter->title
                        ]);
                    }
                } else {
                    Log::warning('AI Material Builder returned invalid JSON', [
                        'json_error' => json_last_error_msg(),
                        'content' => $content,
                        'course' => $course->title,
                        'chapter' => $chapter->title
                    ]);
                }
            } else {
                Log::error('Gemini API Request Failed', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'course' => $course->title,
                    'chapter' => $chapter->title
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Gemini API Error in Material Builder: ' . $e->getMessage(), [
                'course' => $course->title,
                'chapter' => $chapter->title,
                'trace' => $e->getTraceAsString()
            ]);
        }

        // If AI fails, return fallback with session tracking
        $fallbackSuggestion = $this->getFallbackMaterialSuggestion($course, $chapter);

        // Store fallback suggestion in session for future variation
        $this->storeSuggestionInSession($course->id, $chapter->id, $fallbackSuggestion);

        return $fallbackSuggestion;
    }

    /**
     * Extract the best material suggestion from AI response (handles both single object and array)
     */
    private function extractBestMaterialSuggestion($materialData, $existingMaterials)
    {
        // If it's not an array, assume it's a single suggestion object
        if (!is_array($materialData) || empty($materialData)) {
            return null;
        }

        // If it's an associative array (single object), return it directly
        if (isset($materialData['title']) && isset($materialData['description'])) {
            return $materialData;
        }

        // If it's a numeric array (multiple suggestions), select the best one
        if (is_numeric(array_keys($materialData)[0])) {
            return $this->selectBestSuggestionFromArray($materialData, $existingMaterials);
        }

        return null;
    }

    /**
     * Select the best suggestion from an array of suggestions
     */
    private function selectBestSuggestionFromArray($suggestions, $existingMaterials)
    {
        if (empty($suggestions)) {
            return null;
        }

        $existingTitles = $existingMaterials->pluck('title')->map(function($title) {
            return strtolower($title);
        })->toArray();

        $existingTypes = $existingMaterials->pluck('type')->toArray();

        // Score each suggestion based on various criteria
        $scoredSuggestions = [];

        foreach ($suggestions as $index => $suggestion) {
            if (!$this->isValidAIMaterialData($suggestion)) {
                continue; // Skip invalid suggestions
            }

            $score = 0;
            $titleLower = strtolower($suggestion['title']);

            // Avoid duplicates (high penalty)
            $isDuplicate = false;
            foreach ($existingTitles as $existingTitle) {
                if (stripos($titleLower, $existingTitle) !== false || stripos($existingTitle, $titleLower) !== false) {
                    $isDuplicate = true;
                    break;
                }
            }

            if ($isDuplicate) {
                $score -= 100; // Heavy penalty for duplicates
            }

            // Prefer different types for variety
            if (!in_array($suggestion['type'], $existingTypes)) {
                $score += 20;
            }

            // Prefer practical/hands-on content
            if (stripos($suggestion['title'], 'praktik') !== false ||
                stripos($suggestion['title'], 'latihan') !== false ||
                stripos($suggestion['title'], 'assignment') !== false ||
                $suggestion['type'] === 'assignment') {
                $score += 15;
            }

            // Prefer reasonable duration (not too short or too long)
            $duration = intval($suggestion['duration_minutes']);
            if ($duration >= 15 && $duration <= 45) {
                $score += 10;
            }

            // Prefer detailed descriptions
            if (strlen($suggestion['description']) > 100) {
                $score += 5;
            }

            $scoredSuggestions[] = [
                'suggestion' => $suggestion,
                'score' => $score,
                'index' => $index
            ];
        }

        // Sort by score (highest first)
        usort($scoredSuggestions, function($a, $b) {
            return $b['score'] - $a['score'];
        });

        // Return the best suggestion, or first valid one if all have negative scores
        foreach ($scoredSuggestions as $scored) {
            if ($scored['score'] >= 0) {
                return $scored['suggestion'];
            }
        }

        // If all suggestions have negative scores, return the least bad one
        return !empty($scoredSuggestions) ? $scoredSuggestions[0]['suggestion'] : null;
    }

    /**
     * Check if AI material data contains valid, meaningful content
     */
    private function isValidAIMaterialData($materialData)
    {
        // Check if all required fields exist and are not empty/generic
        $requiredFields = ['title', 'description', 'type', 'duration_minutes'];

        foreach ($requiredFields as $field) {
            if (!isset($materialData[$field]) || empty(trim($materialData[$field]))) {
                return false;
            }
        }

        // Check for generic placeholder content that indicates AI failure
        $genericTitles = [
            'Materi Pembelajaran Baru',
            'Materi Baru',
            'New Material',
            'Learning Material'
        ];

        $genericDescriptions = [
            'Deskripsi materi akan diisi di sini.',
            'Deskripsi akan diisi di sini.',
            'Description will be filled here.',
            'Material description here.'
        ];

        $title = trim($materialData['title']);
        $description = trim($materialData['description']);

        // Reject if title or description is generic
        if (in_array($title, $genericTitles) || in_array($description, $genericDescriptions)) {
            return false;
        }

        // Reject if title or description is too short (likely incomplete)
        if (strlen($title) < 10 || strlen($description) < 20) {
            return false;
        }

        return true;
    }

    /**
     * Validate and clean material suggestion data
     */
    private function validateMaterialSuggestion($materialData, $course, $chapter)
    {
        // Validate material type
        $validTypes = ['video', 'text', 'quiz', 'assignment'];
        $type = in_array($materialData['type'] ?? '', $validTypes) ? $materialData['type'] : 'text';

        // Clean and validate data - no fallback to generic content here
        return [
            'title' => substr($materialData['title'], 0, 255),
            'description' => substr($materialData['description'], 0, 500),
            'type' => $type,
            'duration_minutes' => max(5, min(60, intval($materialData['duration_minutes']))),
            'content_outline' => $materialData['content_outline'] ?? '',
        ];
    }

    /**
     * Get fallback material suggestion when AI fails
     */
    private function getFallbackMaterialSuggestion($course, $chapter)
    {
        // Get existing materials to avoid duplication
        $existingMaterials = $this->getExistingMaterialsInChapter($chapter);
        $existingTitles = $existingMaterials->pluck('title')->map(function($title) {
            return strtolower($title);
        })->toArray();

        // Get previous session suggestions to avoid repetition
        $previousSuggestions = $this->getPreviousSessionSuggestions($course->id, $chapter->id);
        $previousTitles = array_map('strtolower', array_column($previousSuggestions, 'title'));
        $previousTypes = array_column($previousSuggestions, 'type');

        // Create contextual suggestions based on course and chapter
        $fallbackSuggestions = [];

        // For Flutter course specifically
        if (stripos($course->title, 'flutter') !== false) {
            $fallbackSuggestions = [
                [
                    'title' => 'Setup Environment Flutter',
                    'description' => 'Panduan lengkap instalasi dan konfigurasi Flutter SDK, Android Studio, dan tools pengembangan yang diperlukan untuk memulai development aplikasi mobile.',
                    'type' => 'text',
                    'duration_minutes' => 25,
                    'content_outline' => '• Download dan install Flutter SDK\n• Setup Android Studio\n• Konfigurasi emulator\n• Verifikasi instalasi dengan flutter doctor'
                ],
                [
                    'title' => 'Widget Dasar Flutter',
                    'description' => 'Memahami konsep widget dalam Flutter dan cara menggunakan widget-widget dasar untuk membangun user interface aplikasi mobile.',
                    'type' => 'video',
                    'duration_minutes' => 30,
                    'content_outline' => '• Konsep widget tree\n• StatelessWidget vs StatefulWidget\n• Container, Text, Image widgets\n• Layout widgets (Row, Column, Stack)'
                ],
                [
                    'title' => 'Praktik Membuat UI Sederhana',
                    'description' => 'Latihan hands-on membuat tampilan aplikasi sederhana menggunakan widget-widget Flutter yang telah dipelajari.',
                    'type' => 'assignment',
                    'duration_minutes' => 45,
                    'content_outline' => '• Membuat layout halaman login\n• Implementasi form input\n• Styling dengan theme\n• Testing di emulator'
                ],
                [
                    'title' => 'Kuis Pemahaman Flutter Basics',
                    'description' => 'Evaluasi pemahaman konsep dasar Flutter, widget, dan development environment melalui kuis interaktif.',
                    'type' => 'quiz',
                    'duration_minutes' => 15,
                    'content_outline' => '• Soal tentang widget lifecycle\n• Pertanyaan setup environment\n• Konsep state management\n• Best practices Flutter'
                ]
            ];
        } else {
            // Generic fallback suggestions
            $fallbackSuggestions = [
                [
                    'title' => 'Pengenalan ' . $chapter->title,
                    'description' => 'Materi pengenalan untuk memahami konsep dasar dalam bab ini dan mempersiapkan foundation pembelajaran selanjutnya.',
                    'type' => 'video',
                    'duration_minutes' => 15,
                    'content_outline' => '• Pengenalan konsep\n• Tujuan pembelajaran\n• Contoh praktis\n• Rangkuman'
                ],
                [
                    'title' => 'Praktik ' . $chapter->title,
                    'description' => 'Latihan praktis untuk menerapkan konsep yang telah dipelajari dengan studi kasus nyata dan implementasi langsung.',
                    'type' => 'text',
                    'duration_minutes' => 20,
                    'content_outline' => '• Langkah-langkah praktik\n• Contoh kasus\n• Tips dan trik\n• Troubleshooting'
                ],
                [
                    'title' => 'Kuis ' . $chapter->title,
                    'description' => 'Evaluasi pemahaman materi melalui kuis interaktif untuk mengukur tingkat penguasaan konsep yang telah dipelajari.',
                    'type' => 'quiz',
                    'duration_minutes' => 10,
                    'content_outline' => '• Soal pilihan ganda\n• Soal benar/salah\n• Penjelasan jawaban\n• Skor dan feedback'
                ]
            ];
        }

        // Filter out suggestions that might duplicate existing materials or previous suggestions
        $availableSuggestions = array_filter($fallbackSuggestions, function($suggestion) use ($existingTitles, $previousTitles) {
            $titleLower = strtolower($suggestion['title']);

            // Check if similar title already exists in materials
            foreach ($existingTitles as $existingTitle) {
                if (stripos($titleLower, $existingTitle) !== false || stripos($existingTitle, $titleLower) !== false) {
                    return false;
                }
            }

            // Check if similar title was suggested before in this session
            foreach ($previousTitles as $previousTitle) {
                if (stripos($titleLower, $previousTitle) !== false || stripos($previousTitle, $titleLower) !== false) {
                    return false;
                }
            }

            return true;
        });

        // If no available suggestions (all filtered out), use original suggestions
        if (empty($availableSuggestions)) {
            $availableSuggestions = $fallbackSuggestions;
        }

        // Prioritize suggestions by type variety - prefer types not used in previous suggestions
        $prioritizedSuggestions = [];
        $normalSuggestions = [];

        foreach ($availableSuggestions as $suggestion) {
            if (!in_array($suggestion['type'], $previousTypes)) {
                $prioritizedSuggestions[] = $suggestion; // Higher priority for unused types
            } else {
                $normalSuggestions[] = $suggestion;
            }
        }

        // Choose from prioritized suggestions first, then normal ones
        if (!empty($prioritizedSuggestions)) {
            $randomIndex = array_rand($prioritizedSuggestions);
            return $prioritizedSuggestions[$randomIndex];
        } else {
            $randomIndex = array_rand($normalSuggestions);
            return $normalSuggestions[$randomIndex];
        }
    }

    /**
     * Fetch existing materials/lessons in the current chapter
     */
    private function getExistingMaterialsInChapter($chapter)
    {
        return $chapter->lessons()
            ->select(['id', 'title', 'description', 'type', 'duration_minutes', 'sort_order'])
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Build context string about existing materials for AI prompt
     */
    private function buildExistingMaterialsContext($existingMaterials)
    {
        if ($existingMaterials->isEmpty()) {
            return "MATERI YANG SUDAH ADA:
- Belum ada materi dalam bab ini (Anda akan membuat materi pertama)

SARAN UNTUK MATERI PERTAMA:
- Buat materi pengenalan yang memberikan overview bab
- Fokus pada konsep fundamental yang akan menjadi dasar pembelajaran
- Pilih format yang engaging (video untuk pengenalan biasanya efektif)";
        }

        $context = "MATERI YANG SUDAH ADA DALAM BAB INI:\n";

        foreach ($existingMaterials as $index => $material) {
            $materialNumber = $index + 1;
            $context .= "- Materi {$materialNumber}: {$material->title}\n";
            $context .= "  * Tipe: {$material->type}\n";
            $context .= "  * Durasi: {$material->duration_minutes} menit\n";

            if ($material->description) {
                $context .= "  * Deskripsi: " . substr($material->description, 0, 150) . (strlen($material->description) > 150 ? '...' : '') . "\n";
            }

            $context .= "\n";
        }

        // Analyze existing materials to suggest what's missing
        $existingTypes = $existingMaterials->pluck('type')->toArray();
        $missingTypes = array_diff(['video', 'text', 'quiz', 'assignment'], $existingTypes);

        $context .= "ANALISIS GAP PEMBELAJARAN:\n";
        if (!empty($missingTypes)) {
            $context .= "- Tipe materi yang belum ada: " . implode(', ', $missingTypes) . "\n";
        }

        // Suggest logical progression
        $totalMaterials = $existingMaterials->count();
        if ($totalMaterials == 1) {
            $context .= "- Saran: Buat materi lanjutan yang memperdalam atau mempraktikkan konsep dari materi pertama\n";
        } elseif ($totalMaterials >= 2) {
            $context .= "- Saran: Buat materi evaluasi (quiz/assignment) atau materi advanced untuk melengkapi pembelajaran\n";
        }

        $context .= "\nINSTRUKSI KHUSUS:
- WAJIB: Jangan buat materi yang duplikat atau terlalu mirip dengan yang sudah ada
- Buat materi yang melengkapi dan memperkaya pembelajaran yang sudah ada
- Pertimbangkan progression logis dari basic ke advanced
- Pastikan materi baru mengisi gap atau melanjutkan alur pembelajaran
- Pertimbangkan urutan logis pembelajaran (materi selanjutnya harus membangun dari yang sudah ada)
- Variasikan tipe materi untuk pengalaman belajar yang lebih baik";

        return $context;
    }

    /**
     * Get previous AI suggestions from session for this course/chapter
     */
    private function getPreviousSessionSuggestions($courseId, $chapterId)
    {
        $sessionKey = "nala_suggestions_{$courseId}_{$chapterId}";
        return Session::get($sessionKey, []);
    }

    /**
     * Store AI suggestion in session for future variation
     */
    private function storeSuggestionInSession($courseId, $chapterId, $suggestion)
    {
        $sessionKey = "nala_suggestions_{$courseId}_{$chapterId}";
        $previousSuggestions = Session::get($sessionKey, []);

        // Add current suggestion with timestamp
        $previousSuggestions[] = [
            'title' => $suggestion['title'],
            'type' => $suggestion['type'],
            'description' => $suggestion['description'],
            'timestamp' => now()->toISOString()
        ];

        // Keep only last 5 suggestions to avoid session bloat
        if (count($previousSuggestions) > 5) {
            $previousSuggestions = array_slice($previousSuggestions, -5);
        }

        Session::put($sessionKey, $previousSuggestions);
    }

    /**
     * Build variation context to encourage different suggestions
     */
    private function buildVariationContext($previousSuggestions, $existingMaterials)
    {
        if (empty($previousSuggestions)) {
            return "VARIASI SARAN:
- Ini adalah permintaan pertama untuk bab ini dalam sesi ini
- Berikan saran terbaik yang paling fundamental dan penting";
        }

        $context = "SARAN AI SEBELUMNYA DALAM SESI INI:\n";
        foreach ($previousSuggestions as $index => $prev) {
            $suggestionNumber = $index + 1;
            $context .= "- Saran {$suggestionNumber}: {$prev['title']} (Tipe: {$prev['type']})\n";
            if (isset($prev['description'])) {
                $context .= "  Deskripsi: " . substr($prev['description'], 0, 100) . "...\n";
            }
        }

        // Analyze what types have been suggested
        $suggestedTypes = array_column($previousSuggestions, 'type');
        $allTypes = ['video', 'text', 'quiz', 'assignment'];
        $unusedTypes = array_diff($allTypes, $suggestedTypes);

        $context .= "\nANALISIS VARIASI:\n";
        if (!empty($unusedTypes)) {
            $context .= "- Tipe yang belum disarankan: " . implode(', ', $unusedTypes) . "\n";
            $context .= "- PRIORITAS: Pertimbangkan menggunakan tipe yang belum disarankan\n";
        }

        // Suggest different approaches
        $approaches = [
            'Fokus pada aspek teoritis/konseptual',
            'Berikan pendekatan praktis/hands-on',
            'Buat evaluasi/assessment',
            'Ciptakan studi kasus real-world',
            'Berikan latihan problem-solving',
            'Fokus pada best practices industry',
            'Buat materi troubleshooting/debugging',
            'Berikan perspective advanced/expert level'
        ];

        $randomApproach = $approaches[array_rand($approaches)];
        $context .= "- SARAN PENDEKATAN: {$randomApproach}\n";

        $context .= "\nINSTRUKSI VARIASI:
- WAJIB: Berikan saran yang BERBEDA dari semua saran sebelumnya
- Gunakan pendekatan pembelajaran yang fresh dan unik
- Jika semua tipe dasar sudah disarankan, berikan variasi dalam format atau fokus
- Pertimbangkan level complexity yang berbeda (basic → intermediate → advanced)
- Berikan perspektif yang belum diexplore sebelumnya
- Pastikan tetap relevan dengan konteks bab dan kursus";

        return $context;
    }
}
