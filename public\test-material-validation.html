<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material Form Validation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6">Material Form Validation Test</h1>
        
        <form id="materialForm" class="space-y-4">
            <!-- Title Field -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                    Judu<PERSON> <span class="text-red-500">*</span>
                </label>
                <input type="text" id="title" name="title" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                       placeholder="Contoh: 1.1 Pengenalan Konsep Dasar"
                       data-validation="required|min:5|max:255">
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- Duration Field -->
            <div>
                <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                    Estimasi Durasi (menit) <span class="text-red-500">*</span>
                </label>
                <input type="number" id="duration_minutes" name="duration_minutes" required min="1" max="300"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                       placeholder="Contoh: 15"
                       data-validation="required|numeric|min:1|max:300">
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- Type Field -->
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                    Tipe Materi <span class="text-red-500">*</span>
                </label>
                <select id="type" name="type" required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                        data-validation="required">
                    <option value="">Pilih tipe materi</option>
                    <option value="video">Video</option>
                    <option value="text">Teks/Artikel</option>
                    <option value="quiz">Kuis</option>
                    <option value="assignment">Tugas</option>
                </select>
                <div class="validation-message mt-1 text-sm hidden"></div>
            </div>

            <!-- Hidden Video Content (for testing) -->
            <div id="video_content" class="hidden">
                <div>
                    <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">URL Video</label>
                    <input type="url" id="video_url" name="video_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                           placeholder="https://www.youtube.com/watch?v=..."
                           data-validation="url">
                    <div class="validation-message mt-1 text-sm hidden"></div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
                <button type="submit" id="submit_btn"
                        class="px-6 py-3 text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg font-medium">
                    <span id="submit_text">Test Validation</span>
                </button>
            </div>
        </form>

        <!-- Debug Output -->
        <div id="debug-output" class="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-medium mb-2">Debug Output:</h3>
            <div id="debug-content" class="text-sm text-gray-600"></div>
        </div>
    </div>

    <script>
        // Copy the MaterialFormValidator class from the main file
        class MaterialFormValidator {
            constructor(formId) {
                this.form = document.getElementById(formId);
                this.validationRules = {
                    required: (value) => value.trim() !== '',
                    min: (value, param) => {
                        if (!isNaN(value) && value !== '') {
                            return parseFloat(value) >= parseFloat(param);
                        }
                        return value.length >= parseInt(param);
                    },
                    max: (value, param) => {
                        if (!isNaN(value) && value !== '') {
                            return parseFloat(value) <= parseFloat(param);
                        }
                        return value.length <= parseInt(param);
                    },
                    numeric: (value) => !isNaN(value) && value !== '',
                    url: (value) => value === '' || /^https?:\/\/.+/.test(value),
                };
                this.init();
            }

            init() {
                const fields = this.form.querySelectorAll('[data-validation]');
                fields.forEach(field => {
                    field.addEventListener('input', () => this.validateField(field));
                    field.addEventListener('blur', () => this.validateField(field));
                });
            }

            validateField(field) {
                const rules = field.dataset.validation.split('|');
                const value = field.value;
                const fieldName = field.name || field.id || 'unknown';

                let isValid = true;
                let errorMessage = '';

                console.log(`Validating field: ${fieldName}, value: "${value}", rules: ${rules.join(', ')}`);

                for (const rule of rules) {
                    const [ruleName, param] = rule.split(':');

                    if (!this.validationRules[ruleName]) {
                        console.log(`Unknown validation rule: ${ruleName}`);
                        continue;
                    }

                    const ruleValid = this.validationRules[ruleName](value, param);

                    if (!ruleValid) {
                        isValid = false;
                        errorMessage = this.getErrorMessage(ruleName, param, field);
                        console.log(`Field ${fieldName} failed validation rule: ${ruleName}, error: ${errorMessage}`);
                        break;
                    }
                }

                this.updateFieldUI(field, isValid, errorMessage);
                return isValid;
            }

            getErrorMessage(ruleName, param, field) {
                const fieldName = field.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || 'Field';

                const messages = {
                    required: `${fieldName} harus diisi`,
                    min: `${fieldName} minimal ${param} karakter`,
                    max: `${fieldName} maksimal ${param} karakter`,
                    numeric: `${fieldName} harus berupa angka`,
                    url: `Format URL tidak valid`
                };

                if (ruleName === 'min' && field.type === 'number') {
                    return `${fieldName} minimal ${param}`;
                }
                if (ruleName === 'max' && field.type === 'number') {
                    return `${fieldName} maksimal ${param}`;
                }

                return messages[ruleName] || `${fieldName} tidak valid`;
            }

            updateFieldUI(field, isValid, errorMessage) {
                const container = field.closest('div');
                const messageEl = container.querySelector('.validation-message');

                field.classList.remove('border-red-500', 'border-green-500');

                if (field.value.trim() !== '') {
                    field.classList.add(isValid ? 'border-green-500' : 'border-red-500');
                }

                if (messageEl) {
                    if (errorMessage) {
                        messageEl.textContent = errorMessage;
                        messageEl.classList.remove('hidden');
                        messageEl.className = 'validation-message mt-1 text-sm text-red-600';
                    } else {
                        messageEl.classList.add('hidden');
                    }
                }
            }

            validateForm() {
                const fields = this.form.querySelectorAll('[data-validation]');
                let isFormValid = true;
                let validatedFields = [];
                let skippedFields = [];

                console.log(`Total fields with data-validation: ${fields.length}`);

                fields.forEach(field => {
                    const fieldName = field.name || field.id || 'unknown';
                    
                    const hiddenParent = field.closest('#video_content.hidden, #text_content.hidden, #quiz_content.hidden, #assignment_content.hidden');
                    
                    if (hiddenParent) {
                        skippedFields.push(fieldName);
                        console.log(`Skipping validation for hidden field: ${fieldName}`);
                        return;
                    }
                    
                    validatedFields.push(fieldName);
                    console.log(`Validating field: ${fieldName}`);
                    
                    if (!this.validateField(field)) {
                        console.log(`Field validation failed: ${fieldName}`);
                        isFormValid = false;
                    } else {
                        console.log(`Field validation passed: ${fieldName}`);
                    }
                });

                console.log(`Validated fields: ${validatedFields.join(', ')}`);
                console.log(`Skipped fields: ${skippedFields.join(', ')}`);
                console.log(`Overall form validation result: ${isFormValid}`);

                return isFormValid;
            }
        }

        // Initialize validator
        let isSubmitting = false;
        const validator = new MaterialFormValidator('materialForm');

        // Form submission handler
        document.getElementById('materialForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (isSubmitting) {
                console.log('Form submission already in progress, ignoring...');
                return false;
            }

            isSubmitting = true;
            console.log('Form submission started...');

            const debugContent = document.getElementById('debug-content');
            debugContent.innerHTML = '';

            function addDebugMessage(message) {
                debugContent.innerHTML += message + '<br>';
            }

            addDebugMessage('Form submission started...');

            if (!validator.validateForm()) {
                addDebugMessage('Form validation failed');
                isSubmitting = false;
                return false;
            }

            addDebugMessage('Form validation passed!');
            addDebugMessage('Form would be submitted successfully.');
            isSubmitting = false;
        });
    </script>
</body>
</html>
