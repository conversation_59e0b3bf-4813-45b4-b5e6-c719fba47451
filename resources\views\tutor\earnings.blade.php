@extends('layouts.tutor')

@section('title', 'Pen<PERSON><PERSON>lan - Tutor Dashboard')

@section('content')
<div class="tutor-dashboard-container tutor-earnings-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Page Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mb-2">
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-0">Pen<PERSON><PERSON>lan</h1>
                    <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 md:py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 w-fit">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        Earnings Center
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">Pantau pendapatan dan riwayat pembayaran Anda</p>
            </div>
            <div class="tutor-header-actions flex items-center">
                <a href="{{ route('tutor.dashboard') }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly w-full md:w-auto">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Earnings Overview -->
    <div class="tutor-stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-emerald-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Penghasilan</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">Rp {{ number_format($earningsData['total_earnings']) }}</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-teal-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Bulan Ini</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">Rp {{ number_format($earningsData['monthly_earnings']) }}</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-amber-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Pending Payout</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">Rp {{ number_format($earningsData['pending_payouts']) }}</p>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-green-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Rata-rata/Bulan</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">Rp {{ number_format($earningsData['avg_monthly_earnings']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payout Information -->
    <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg p-4 md:p-6 mb-6 md:mb-8 border border-emerald-200">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <h2 class="text-base md:text-lg font-semibold text-emerald-900 mb-2">💰 Informasi Pembayaran</h2>
                <p class="text-emerald-800 text-xs md:text-sm">
                    Pembayaran dilakukan setiap tanggal 15 dan akhir bulan. Minimum payout Rp 100.000.
                </p>
                <p class="text-emerald-700 text-xs md:text-sm mt-1">
                    Komisi tutor: 70% dari harga kursus setelah dipotong biaya platform.
                </p>
            </div>
            <button id="requestPayoutBtn" class="bg-emerald-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg font-semibold hover:bg-emerald-700 transition-colors shadow-lg hover:shadow-xl tutor-touch-friendly w-full md:w-auto text-sm">
                Request Payout
            </button>
        </div>
    </div>

        @if(count($earningsData['transaction_history']) > 0)
            <!-- Earnings by Course -->
            <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6 md:mb-8">
                <div class="px-4 md:px-6 py-3 md:py-4 border-b border-gray-200">
                    <h2 class="text-base md:text-lg font-semibold text-gray-900">Pendapatan per Kursus</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kursus</th>
                                <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga</th>
                                <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Siswa</th>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Pendapatan</th>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($earningsData['earnings_by_course'] as $course)
                            <tr class="hover:bg-gray-50">
                                <td class="px-3 md:px-6 py-4">
                                    <div class="text-xs md:text-sm font-medium text-gray-900 truncate">{{ $course['title'] }}</div>
                                    <!-- Mobile: Show additional info -->
                                    <div class="md:hidden mt-1 space-y-1">
                                        <div class="text-xs text-gray-600">Rp {{ number_format($course['price'], 0, ',', '.') }}</div>
                                        <div class="text-xs text-gray-500">{{ $course['total_enrollments'] }} siswa</div>
                                    </div>
                                </td>
                                <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Rp {{ number_format($course['price'], 0, ',', '.') }}
                                </td>
                                <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $course['total_enrollments'] }}
                                </td>
                                <td class="px-3 md:px-6 py-4 whitespace-nowrap text-xs md:text-sm font-medium text-green-600">
                                    Rp {{ number_format($course['total_revenue'], 0, ',', '.') }}
                                </td>
                                <td class="px-3 md:px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        {{ $course['status'] === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $course['status'] === 'published' ? 'Dipublikasi' : 'Draft' }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                <div class="px-4 md:px-6 py-3 md:py-4 border-b border-gray-200">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-3 md:gap-4">
                        <h2 class="text-base md:text-lg font-semibold text-gray-900">Riwayat Transaksi</h2>
                        <div class="flex flex-col md:flex-row gap-2 md:gap-4">
                            <select class="tutor-form-mobile border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                                <option>Semua Transaksi</option>
                                <option>Penjualan Kursus</option>
                                <option>Payout</option>
                                <option>Refund</option>
                            </select>
                            <select class="tutor-form-mobile border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm">
                                <option>30 Hari Terakhir</option>
                                <option>3 Bulan Terakhir</option>
                                <option>6 Bulan Terakhir</option>
                                <option>1 Tahun Terakhir</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deskripsi</th>
                                <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Kursus</th>
                                <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipe</th>
                                <th class="px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah</th>
                                <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($earningsData['transaction_history'] as $transaction)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 md:px-6 py-4 whitespace-nowrap text-xs md:text-sm text-gray-900">
                                        {{ $transaction['date'] }}
                                    </td>
                                    <td class="px-3 md:px-6 py-4">
                                        <div class="text-xs md:text-sm text-gray-900">Pendaftaran Kursus</div>
                                        <div class="text-xs text-gray-500">{{ $transaction['student_name'] }}</div>
                                        <!-- Mobile: Show additional info -->
                                        <div class="md:hidden mt-1 space-y-1">
                                            <div class="text-xs text-gray-600 truncate">{{ $transaction['course_title'] }}</div>
                                            <div class="flex items-center gap-2">
                                                <span class="inline-flex px-2 py-0.5 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                    Penjualan
                                                </span>
                                                <span class="inline-flex px-2 py-0.5 text-xs font-semibold rounded-full
                                                       {{ $transaction['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                                    {{ $transaction['status'] === 'active' ? 'Aktif' : ucfirst($transaction['status']) }}
                                                </span>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $transaction['course_title'] }}
                                    </td>
                                    <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Penjualan
                                        </span>
                                    </td>
                                    <td class="px-3 md:px-6 py-4 whitespace-nowrap text-xs md:text-sm font-medium">
                                        <span class="text-green-600">
                                            +Rp {{ number_format($transaction['amount'], 0, ',', '.') }}
                                        </span>
                                    </td>
                                    <td class="hidden md:table-cell px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                   {{ $transaction['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ $transaction['status'] === 'active' ? 'Aktif' : ucfirst($transaction['status']) }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="bg-white rounded-xl shadow-sm p-8 md:p-12 text-center">
                <div class="w-16 h-16 md:w-24 md:h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                    <svg class="w-8 h-8 md:w-12 md:h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-2">Belum Ada Penghasilan</h3>
                <p class="text-gray-600 mb-6 md:mb-8 max-w-md mx-auto text-sm md:text-base">
                    Mulai menghasilkan dengan membuat kursus berkualitas yang menarik bagi siswa.
                </p>
                <div class="flex flex-col md:flex-row gap-3 md:gap-4 justify-center">
                    <a href="{{ route('tutor.create-course') }}" class="btn btn-primary tutor-touch-friendly">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Buat Kursus Pertama
                    </a>
                    <a href="{{ route('tutor.analytics') }}" class="btn btn-outline tutor-touch-friendly">
                        Lihat Analitik
                    </a>
                </div>
            </div>
        @endif

        <!-- Payment Settings -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mt-8">
            <!-- Header Section -->
            <div class="bg-gradient-to-r from-emerald-50 to-teal-50 px-8 py-6 border-b border-gray-100">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-xl font-bold text-gray-900 mb-2">Pengaturan Pembayaran</h2>
                        <p class="text-sm text-gray-600">Kelola metode pembayaran dan informasi pajak untuk payout</p>
                    </div>
                    <div class="hidden md:flex items-center space-x-2">
                        <div class="w-3 h-3 bg-emerald-400 rounded-full"></div>
                        <div class="w-3 h-3 bg-emerald-300 rounded-full"></div>
                        <div class="w-3 h-3 bg-emerald-200 rounded-full"></div>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            <div class="p-8">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Payment Methods Section -->
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Metode Pembayaran</h3>
                        </div>

                        <div class="space-y-4">
                            <!-- Bank Transfer Card -->
                            <div class="group relative bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-5 hover:shadow-md transition-all duration-200 cursor-pointer" id="bankTransferCard">
                                <div class="flex items-center">
                                    <div class="w-14 h-14 bg-blue-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-blue-200 transition-colors">
                                        <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900 mb-1">Transfer Bank</h4>
                                        <p class="text-sm text-gray-600 mb-2">BCA, Mandiri, BNI, BRI, dan bank lainnya</p>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" id="bankStatus">
                                                Belum diatur
                                            </span>
                                        </div>
                                    </div>
                                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm setup-bank-btn">
                                        <span class="hidden sm:inline">Atur Sekarang</span>
                                        <span class="sm:hidden">Atur</span>
                                    </button>
                                </div>
                            </div>

                            <!-- E-Wallet Card -->
                            <div class="group relative bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-5 hover:shadow-md transition-all duration-200 cursor-pointer" id="ewalletCard">
                                <div class="flex items-center">
                                    <div class="w-14 h-14 bg-green-100 rounded-xl flex items-center justify-center mr-4 group-hover:bg-green-200 transition-colors">
                                        <svg class="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900 mb-1">E-Wallet Digital</h4>
                                        <p class="text-sm text-gray-600 mb-2">GoPay, OVO, DANA, ShopeePay</p>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" id="ewalletStatus">
                                                Belum diatur
                                            </span>
                                        </div>
                                    </div>
                                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm setup-ewallet-btn">
                                        <span class="hidden sm:inline">Atur Sekarang</span>
                                        <span class="sm:hidden">Atur</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tax Information Section -->
                    <div class="space-y-6">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                                <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900">Informasi Pajak</h3>
                        </div>

                        <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6" id="taxInfoSection">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">Informasi Pajak Diperlukan</h4>
                                <p class="text-sm text-gray-600 mb-6 max-w-sm mx-auto">
                                    Lengkapi informasi NPWP dan status pajak untuk memenuhi persyaratan payout sesuai regulasi.
                                </p>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                                        <svg class="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>Aman dan terenkripsi</span>
                                    </div>
                                    <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                                        <svg class="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                        <span>Sesuai regulasi pajak</span>
                                    </div>
                                </div>
                                <button class="mt-6 px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors font-medium save-tax-info-btn">
                                    <span class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        <span>Atur Informasi Pajak</span>
                                    </span>
                                </button>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="bg-gray-50 rounded-xl p-4">
                            <h4 class="font-medium text-gray-900 mb-3">Status Pengaturan</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Metode Pembayaran</span>
                                    <span class="font-medium text-gray-900" id="paymentMethodStatus">0/2 Lengkap</span>
                                </div>
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">Informasi Pajak</span>
                                    <span class="font-medium text-gray-900" id="taxInfoStatus">Belum Lengkap</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-3">
                                    <div class="bg-emerald-600 h-2 rounded-full transition-all duration-300" style="width: 0%" id="completionProgress"></div>
                                </div>
                                <p class="text-xs text-gray-500 text-center mt-2">
                                    <span id="completionPercentage">0%</span> pengaturan selesai
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payout History Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Riwayat Payout</h2>
                <button id="refreshPayoutHistory" class="text-primary hover:text-primary-dark text-sm font-medium">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>

            <div id="payoutHistoryContent">
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500">Memuat riwayat payout...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payout Request Modal -->
<div id="payoutModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Request Payout</h3>
                    <button id="closePayoutModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="payoutModalContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Settings Modal -->
<div id="paymentSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Pengaturan Pembayaran</h3>
                    <button id="closePaymentSettingsModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div id="paymentSettingsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal elements
    const payoutModal = document.getElementById('payoutModal');
    const paymentSettingsModal = document.getElementById('paymentSettingsModal');
    const requestPayoutBtn = document.getElementById('requestPayoutBtn');
    const closePayoutModal = document.getElementById('closePayoutModal');
    const closePaymentSettingsModal = document.getElementById('closePaymentSettingsModal');
    const setupBankBtns = document.querySelectorAll('.setup-bank-btn');
    const setupEwalletBtns = document.querySelectorAll('.setup-ewallet-btn');
    const saveTaxInfoBtn = document.querySelector('.save-tax-info-btn');

    // Load payment settings and payout history on page load
    loadPaymentSettingsStatus();
    loadPayoutHistory();

    // Show payout modal
    requestPayoutBtn.addEventListener('click', function() {
        showPayoutModal();
    });

    // Show payment settings modal
    setupBankBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            showPaymentSettingsModal('bank_transfer');
        });
    });

    setupEwalletBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            showPaymentSettingsModal('ewallet');
        });
    });

    saveTaxInfoBtn.addEventListener('click', function() {
        showPaymentSettingsModal('tax');
    });

    // Refresh payout history
    document.getElementById('refreshPayoutHistory').addEventListener('click', function() {
        loadPayoutHistory();
    });

    // Close modals
    closePayoutModal.addEventListener('click', function() {
        payoutModal.classList.add('hidden');
    });

    closePaymentSettingsModal.addEventListener('click', function() {
        paymentSettingsModal.classList.add('hidden');
    });

    // Close modals when clicking outside
    payoutModal.addEventListener('click', function(e) {
        if (e.target === payoutModal) {
            payoutModal.classList.add('hidden');
        }
    });

    paymentSettingsModal.addEventListener('click', function(e) {
        if (e.target === paymentSettingsModal) {
            paymentSettingsModal.classList.add('hidden');
        }
    });

    // Show payout modal function
    function showPayoutModal() {
        fetch('{{ route("tutor.payout-data") }}')
            .then(response => response.json())
            .then(data => {
                // Check if the response was successful
                if (!data.success) {
                    showToast(data.message || 'Terjadi kesalahan saat memuat data payout', 'error');
                    return;
                }

                const content = document.getElementById('payoutModalContent');

                // Check if payment settings don't exist
                if (!data.has_payment_settings) {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Pengaturan Pembayaran Belum Ada</h4>
                            <p class="text-gray-600 mb-6">${data.message}</p>
                            <button onclick="showPaymentSettingsModal('bank_transfer')" class="btn btn-primary">
                                Setup Pengaturan Pembayaran
                            </button>
                        </div>
                    `;
                } else if (!data.payment_settings_complete) {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Pengaturan Pembayaran Belum Lengkap</h4>
                            <p class="text-gray-600 mb-6">${data.message}</p>
                            <button onclick="showPaymentSettingsModal('bank_transfer')" class="btn btn-primary">
                                Lengkapi Pengaturan
                            </button>
                        </div>
                    `;
                } else if (!data.can_request_payout) {
                    content.innerHTML = `
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Saldo Tidak Mencukupi</h4>
                            <p class="text-gray-600 mb-2">Saldo tersedia: ${data.formatted_available_amount}</p>
                            <p class="text-gray-600 mb-6">Minimum payout: ${data.formatted_minimum_payout}</p>
                            <p class="text-sm text-gray-500">${data.message}</p>
                        </div>
                    `;
                } else {
                    content.innerHTML = generatePayoutForm(data);
                }

                payoutModal.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Terjadi kesalahan saat memuat data payout', 'error');
            });
    }

    // Generate payout form
    function generatePayoutForm(data) {
        return `
            <form id="payoutForm">
                <div class="space-y-4">
                    <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-emerald-800">Saldo Tersedia</span>
                            <span class="text-lg font-bold text-emerald-900">${data.formatted_available_amount}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-emerald-700">Metode Pembayaran</span>
                            <span class="text-sm font-medium text-emerald-800">${data.preferred_method}</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Jumlah Payout</label>
                        <div class="relative">
                            <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">IDR</span>
                            <input type="number" id="payoutAmount" name="amount"
                                   class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="0" min="${data.minimum_payout}" max="${data.available_amount}" required>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Minimum: ${data.formatted_minimum_payout}</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Catatan (Opsional)</label>
                        <textarea name="notes" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                  placeholder="Tambahkan catatan untuk permintaan payout ini..."></textarea>
                    </div>

                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h5 class="font-medium text-gray-900 mb-2">Riwayat Payout Terbaru</h5>
                        ${data.recent_payouts.length > 0 ?
                            data.recent_payouts.map(payout => `
                                <div class="flex justify-between items-center py-1">
                                    <span class="text-sm text-gray-600">${payout.request_id}</span>
                                    <div class="text-right">
                                        <div class="text-sm font-medium">${payout.amount}</div>
                                        <div class="text-xs text-gray-500">${payout.status} • ${payout.requested_at}</div>
                                    </div>
                                </div>
                            `).join('') :
                            '<p class="text-sm text-gray-500">Belum ada riwayat payout</p>'
                        }
                    </div>

                    <div class="flex gap-3 pt-4">
                        <button type="button" onclick="document.getElementById('payoutModal').classList.add('hidden')"
                                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Batal
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">
                            Request Payout
                        </button>
                    </div>
                </div>
            </form>
        `;
    }

    // Handle payout form submission
    document.addEventListener('submit', function(e) {
        if (e.target.id === 'payoutForm') {
            e.preventDefault();
            submitPayoutRequest(e.target);
        }
    });

    // Submit payout request
    function submitPayoutRequest(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = 'Memproses...';

        fetch('{{ route("tutor.request-payout") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                payoutModal.classList.add('hidden');
                showToast(data.message, 'success');
                // Refresh the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat memproses permintaan', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }

    // Show payment settings modal
    function showPaymentSettingsModal(tab = 'bank_transfer') {
        fetch('{{ route("tutor.payment-settings.get") }}')
            .then(response => response.json())
            .then(data => {
                const content = document.getElementById('paymentSettingsContent');
                content.innerHTML = generatePaymentSettingsForm(data.payment_settings, tab);
                paymentSettingsModal.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Terjadi kesalahan saat memuat pengaturan pembayaran', 'error');
            });
    }

    // Make showPaymentSettingsModal globally available
    window.showPaymentSettingsModal = showPaymentSettingsModal;

    // Generate payment settings form
    function generatePaymentSettingsForm(settings, activeTab) {
        const defaultSettings = settings || {
            preferred_method: 'bank_transfer',
            minimum_payout: 100000,
            payout_frequency: 'manual',
            auto_payout_enabled: false,
            tax_status: 'non_pkp'
        };

        return `
            <form id="paymentSettingsForm">
                <div class="space-y-6">
                    <!-- Payment Method Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Metode Pembayaran Utama</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method" value="bank_transfer"
                                       ${defaultSettings.preferred_method === 'bank_transfer' ? 'checked' : ''}
                                       class="mr-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                    </div>
                                    <span class="font-medium">Transfer Bank</span>
                                </div>
                            </label>
                            <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method" value="gopay"
                                       ${defaultSettings.preferred_method === 'gopay' ? 'checked' : ''}
                                       class="mr-3">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <span class="font-medium">GoPay</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Bank Transfer Details -->
                    <div id="bankDetails" class="space-y-4" style="display: ${defaultSettings.preferred_method === 'bank_transfer' ? 'block' : 'none'}">
                        <h4 class="font-medium text-gray-900">Informasi Bank</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nama Bank</label>
                                <select name="bank_name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
                                    <option value="">Pilih Bank</option>
                                    <option value="BCA" ${defaultSettings.bank_name === 'BCA' ? 'selected' : ''}>BCA</option>
                                    <option value="Mandiri" ${defaultSettings.bank_name === 'Mandiri' ? 'selected' : ''}>Mandiri</option>
                                    <option value="BNI" ${defaultSettings.bank_name === 'BNI' ? 'selected' : ''}>BNI</option>
                                    <option value="BRI" ${defaultSettings.bank_name === 'BRI' ? 'selected' : ''}>BRI</option>
                                    <option value="CIMB Niaga" ${defaultSettings.bank_name === 'CIMB Niaga' ? 'selected' : ''}>CIMB Niaga</option>
                                    <option value="Danamon" ${defaultSettings.bank_name === 'Danamon' ? 'selected' : ''}>Danamon</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nomor Rekening</label>
                                <input type="text" name="bank_account_number" value="${defaultSettings.bank_account_number || ''}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                       placeholder="**********">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nama Pemegang Rekening</label>
                                <input type="text" name="bank_account_holder" value="${defaultSettings.bank_account_holder || ''}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                       placeholder="Nama sesuai rekening">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Cabang (Opsional)</label>
                                <input type="text" name="bank_branch" value="${defaultSettings.bank_branch || ''}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                       placeholder="Cabang bank">
                            </div>
                        </div>
                    </div>

                    <!-- E-wallet Details -->
                    <div id="ewalletDetails" class="space-y-4" style="display: ${defaultSettings.preferred_method !== 'bank_transfer' ? 'block' : 'none'}">
                        <h4 class="font-medium text-gray-900">Informasi E-Wallet</h4>
                        <div id="gopayDetails" style="display: ${defaultSettings.preferred_method === 'gopay' ? 'block' : 'none'}">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nomor GoPay</label>
                            <input type="text" name="gopay_number" value="${defaultSettings.gopay_number || ''}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                   placeholder="08xxxxxxxxxx">
                        </div>
                    </div>

                    <!-- Tax Information -->
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Informasi Pajak</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nomor NPWP</label>
                                <input type="text" name="npwp_number" value="${defaultSettings.npwp_number || ''}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                       placeholder="XX.XXX.XXX.X-XXX.XXX" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Nama sesuai NPWP</label>
                                <input type="text" name="npwp_name" value="${defaultSettings.npwp_name || ''}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                       placeholder="Nama lengkap sesuai NPWP" required>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Alamat NPWP</label>
                                <textarea name="npwp_address" rows="2"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                          placeholder="Alamat sesuai NPWP">${defaultSettings.npwp_address || ''}</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status Pajak</label>
                                <select name="tax_status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
                                    <option value="non_pkp" ${defaultSettings.tax_status === 'non_pkp' ? 'selected' : ''}>Non PKP</option>
                                    <option value="pkp" ${defaultSettings.tax_status === 'pkp' ? 'selected' : ''}>PKP (Pengusaha Kena Pajak)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Payout Settings -->
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Pengaturan Payout</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Payout</label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">IDR</span>
                                    <input type="number" name="minimum_payout" value="${defaultSettings.minimum_payout}"
                                           class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500"
                                           min="100000" step="1000" required>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Frekuensi Payout</label>
                                <select name="payout_frequency" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500">
                                    <option value="manual" ${defaultSettings.payout_frequency === 'manual' ? 'selected' : ''}>Manual</option>
                                    <option value="weekly" ${defaultSettings.payout_frequency === 'weekly' ? 'selected' : ''}>Mingguan</option>
                                    <option value="monthly" ${defaultSettings.payout_frequency === 'monthly' ? 'selected' : ''}>Bulanan</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="flex gap-3 pt-4 border-t">
                        <button type="button" onclick="document.getElementById('paymentSettingsModal').classList.add('hidden')"
                                class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Batal
                        </button>
                        <button type="submit" class="flex-1 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">
                            Simpan Pengaturan
                        </button>
                    </div>
                </div>
            </form>
        `;
    }

    // Handle payment method change
    document.addEventListener('change', function(e) {
        if (e.target.name === 'payment_method') {
            const bankDetails = document.getElementById('bankDetails');
            const ewalletDetails = document.getElementById('ewalletDetails');
            const gopayDetails = document.getElementById('gopayDetails');

            if (e.target.value === 'bank_transfer') {
                bankDetails.style.display = 'block';
                ewalletDetails.style.display = 'none';
            } else {
                bankDetails.style.display = 'none';
                ewalletDetails.style.display = 'block';

                // Show specific e-wallet details
                gopayDetails.style.display = e.target.value === 'gopay' ? 'block' : 'none';
            }
        }
    });

    // Handle payment settings form submission
    document.addEventListener('submit', function(e) {
        if (e.target.id === 'paymentSettingsForm') {
            e.preventDefault();
            submitPaymentSettings(e.target);
        }
    });

    // Submit payment settings
    function submitPaymentSettings(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = 'Menyimpan...';

        fetch('{{ route("tutor.payment-settings.update") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                paymentSettingsModal.classList.add('hidden');
                showToast(data.message, 'success');
                // Refresh the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat menyimpan pengaturan', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    }

    // Toast notification function
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white transform transition-transform duration-300 translate-x-full ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' :
            'bg-blue-500'
        }`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);

        // Remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 5000);
    }

    // Load payment settings status
    function loadPaymentSettingsStatus() {
        fetch('{{ route("tutor.payment-settings.get") }}')
            .then(response => response.json())
            .then(data => {
                updatePaymentSettingsDisplay(data);
            })
            .catch(error => {
                console.error('Error loading payment settings:', error);
            });
    }

    // Update payment settings display
    function updatePaymentSettingsDisplay(data) {
        const settings = data.payment_settings;
        let completedMethods = 0;
        let taxCompleted = false;

        // Update bank transfer status
        const bankStatus = document.getElementById('bankStatus');
        const bankCard = document.getElementById('bankTransferCard');
        const bankButton = bankCard.querySelector('.setup-bank-btn');

        if (settings && settings.bank_name && settings.bank_account_number) {
            bankStatus.textContent = `${settings.bank_name} - ${settings.bank_account_number}`;
            bankStatus.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
            bankButton.innerHTML = '<span class="hidden sm:inline">Edit Bank</span><span class="sm:hidden">Edit</span>';
            bankButton.className = 'px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm setup-bank-btn';
            completedMethods++;
        } else {
            bankStatus.textContent = 'Belum diatur';
            bankStatus.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
        }

        // Update e-wallet status
        const ewalletStatus = document.getElementById('ewalletStatus');
        const ewalletCard = document.getElementById('ewalletCard');
        const ewalletButton = ewalletCard.querySelector('.setup-ewallet-btn');

        if (settings && (settings.gopay_number || settings.ovo_number || settings.dana_number || settings.shopeepay_number)) {
            let activeWallets = [];
            if (settings.gopay_number) activeWallets.push('GoPay');
            if (settings.ovo_number) activeWallets.push('OVO');
            if (settings.dana_number) activeWallets.push('DANA');
            if (settings.shopeepay_number) activeWallets.push('ShopeePay');

            ewalletStatus.textContent = activeWallets.join(', ');
            ewalletStatus.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
            ewalletButton.innerHTML = '<span class="hidden sm:inline">Edit E-Wallet</span><span class="sm:hidden">Edit</span>';
            ewalletButton.className = 'px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium text-sm setup-ewallet-btn';
            completedMethods++;
        } else {
            ewalletStatus.textContent = 'Belum diatur';
            ewalletStatus.className = 'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800';
        }

        // Update tax information section
        const taxSection = document.getElementById('taxInfoSection');
        const taxInfoStatus = document.getElementById('taxInfoStatus');

        if (settings && settings.npwp_number && settings.npwp_name) {
            taxCompleted = true;
            taxInfoStatus.textContent = 'Lengkap';
            taxSection.innerHTML = `
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Informasi Pajak Sudah Lengkap</h4>
                        <div class="bg-white rounded-lg p-4 mb-4">
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">NPWP:</span>
                                    <span class="font-medium text-gray-900">${settings.npwp_number}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Nama:</span>
                                    <span class="font-medium text-gray-900">${settings.npwp_name}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium text-gray-900">${settings.tax_status === 'pkp' ? 'PKP' : 'Non-PKP'}</span>
                                </div>
                            </div>
                        </div>
                        <button class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium save-tax-info-btn">
                            <span class="flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                <span>Edit Informasi Pajak</span>
                            </span>
                        </button>
                    </div>
                </div>
            `;
        } else {
            taxInfoStatus.textContent = 'Belum Lengkap';
        }

        // Update completion status
        const paymentMethodStatus = document.getElementById('paymentMethodStatus');
        const completionProgress = document.getElementById('completionProgress');
        const completionPercentage = document.getElementById('completionPercentage');

        paymentMethodStatus.textContent = `${completedMethods}/2 Lengkap`;

        // Calculate completion percentage (payment methods + tax info)
        const totalSteps = 3; // 2 payment methods + 1 tax info
        const completedSteps = completedMethods + (taxCompleted ? 1 : 0);
        const percentage = Math.round((completedSteps / totalSteps) * 100);

        completionProgress.style.width = `${percentage}%`;
        completionPercentage.textContent = `${percentage}%`;

        // Update progress bar color based on completion
        if (percentage === 100) {
            completionProgress.className = 'bg-green-600 h-2 rounded-full transition-all duration-300';
        } else if (percentage >= 50) {
            completionProgress.className = 'bg-emerald-600 h-2 rounded-full transition-all duration-300';
        } else {
            completionProgress.className = 'bg-amber-600 h-2 rounded-full transition-all duration-300';
        }
    }

    // Load payout history
    function loadPayoutHistory() {
        const content = document.getElementById('payoutHistoryContent');
        content.innerHTML = `
            <div class="text-center py-8">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                </div>
                <p class="text-gray-500">Memuat riwayat payout...</p>
            </div>
        `;

        fetch('{{ route("tutor.payout-history") }}')
            .then(response => response.json())
            .then(data => {
                if (data.payout_requests && data.payout_requests.length > 0) {
                    displayPayoutHistory(data.payout_requests);
                } else {
                    displayEmptyPayoutHistory();
                }
            })
            .catch(error => {
                console.error('Error loading payout history:', error);
                content.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <p class="text-red-600">Gagal memuat riwayat payout</p>
                        <button onclick="loadPayoutHistory()" class="mt-2 text-primary hover:text-primary-dark text-sm">Coba Lagi</button>
                    </div>
                `;
            });
    }

    // Display payout history
    function displayPayoutHistory(payouts) {
        const content = document.getElementById('payoutHistoryContent');
        const payoutItems = payouts.map(payout => {
            const statusColor = getStatusColor(payout.status);
            return `
                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-900">${payout.request_id}</span>
                            <span class="ml-3 px-2 py-1 text-xs font-medium rounded-full ${statusColor}">${payout.status_name}</span>
                        </div>
                        <span class="text-lg font-semibold text-gray-900">${payout.amount}</span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Jumlah Bersih:</span>
                            <span class="font-medium text-gray-900 ml-1">${payout.net_amount}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Metode:</span>
                            <span class="font-medium text-gray-900 ml-1">${payout.payment_method}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Tanggal:</span>
                            <span class="font-medium text-gray-900 ml-1">${payout.requested_at}</span>
                        </div>
                    </div>

                    ${payout.notes ? `
                        <div class="mt-3 pt-3 border-t border-gray-100">
                            <span class="text-xs text-gray-500">Catatan Admin:</span>
                            <p class="text-sm text-gray-700 mt-1">${payout.notes}</p>
                        </div>
                    ` : ''}

                    ${payout.tutor_notes ? `
                        <div class="mt-2">
                            <span class="text-xs text-gray-500">Catatan Anda:</span>
                            <p class="text-sm text-gray-700 mt-1">${payout.tutor_notes}</p>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        content.innerHTML = `
            <div class="space-y-4">
                ${payoutItems}
            </div>
        `;
    }

    // Display empty payout history
    function displayEmptyPayoutHistory() {
        const content = document.getElementById('payoutHistoryContent');
        content.innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Riwayat Payout</h3>
                <p class="text-gray-500 mb-6">Anda belum pernah mengajukan permintaan payout. Mulai dengan mengajukan payout pertama Anda!</p>
                <button onclick="document.getElementById('requestPayoutBtn').click()" class="btn btn-primary">
                    Ajukan Payout Pertama
                </button>
            </div>
        `;
    }

    // Get status color classes
    function getStatusColor(status) {
        switch(status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'processing':
                return 'bg-blue-100 text-blue-800';
            case 'approved':
                return 'bg-green-100 text-green-800';
            case 'paid':
                return 'bg-emerald-100 text-emerald-800';
            case 'rejected':
                return 'bg-red-100 text-red-800';
            case 'cancelled':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    }
});
</script>
@endsection
