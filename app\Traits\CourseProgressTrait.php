<?php

namespace App\Traits;

use App\Models\LessonProgress;
use App\Models\CourseEnrollment;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

trait CourseProgressTrait
{
    /**
     * Calculate course progress for a user consistently across all pages.
     *
     * @param Course $course
     * @param int|null $userId
     * @return array
     */
    public function calculateCourseProgress(Course $course, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [
                'total_lessons' => 0,
                'completed_lessons' => 0,
                'progress_percentage' => 0,
                'last_accessed' => null,
                'enrollment' => null,
                'is_enrolled' => false
            ];
        }

        // Load course with published chapters and lessons only
        $course->load([
            'chapters' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            }
        ]);

        // Get all published lesson IDs for this course
        $lessonIds = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('id');
        });

        $totalLessons = $lessonIds->count();

        // Get enrollment info
        $enrollment = CourseEnrollment::where('user_id', $userId)
            ->where('course_id', $course->id)
            ->where('status', 'active')
            ->first();

        $isEnrolled = $enrollment !== null;

        if ($totalLessons === 0) {
            return [
                'total_lessons' => 0,
                'completed_lessons' => 0,
                'progress_percentage' => 0,
                'last_accessed' => $enrollment ? $enrollment->updated_at : null,
                'enrollment' => $enrollment,
                'is_enrolled' => $isEnrolled
            ];
        }

        // Get user's progress for this course
        $courseProgress = LessonProgress::where('user_id', $userId)
            ->whereIn('lesson_id', $lessonIds)
            ->get();

        $completedLessons = $courseProgress->where('status', 'completed')->count();
        $progressPercentage = round(($completedLessons / $totalLessons) * 100);

        // Get last accessed time
        $lastAccessed = $courseProgress->max('last_accessed_at') ?? ($enrollment ? $enrollment->updated_at : null);

        return [
            'total_lessons' => $totalLessons,
            'completed_lessons' => $completedLessons,
            'progress_percentage' => $progressPercentage,
            'last_accessed' => $lastAccessed,
            'enrollment' => $enrollment,
            'is_enrolled' => $isEnrolled,
            'lesson_progress' => $courseProgress->keyBy('lesson_id')
        ];
    }

    /**
     * Check if user is enrolled in a course.
     *
     * @param Course $course
     * @param int|null $userId
     * @return bool
     */
    public function isUserEnrolledInCourse(Course $course, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return false;
        }

        // Check if user is the course tutor
        if ($course->tutor_id === $userId) {
            return true;
        }

        // Check if course is free
        if ($course->is_free || $course->price == 0) {
            return true;
        }

        // Check enrollment
        return CourseEnrollment::where('user_id', $userId)
            ->where('course_id', $course->id)
            ->where('status', 'active')
            ->exists();
    }

    /**
     * Get enrollment status for course detail page.
     *
     * @param Course $course
     * @param int|null $userId
     * @return array
     */
    public function getCourseEnrollmentStatus(Course $course, $userId = null)
    {
        $userId = $userId ?? Auth::id();
        
        if (!$userId) {
            return [
                'is_enrolled' => false,
                'is_tutor' => false,
                'enrollment' => null,
                'can_access' => false
            ];
        }

        $isTutor = $course->tutor_id === $userId;
        
        if ($isTutor) {
            return [
                'is_enrolled' => false,
                'is_tutor' => true,
                'enrollment' => null,
                'can_access' => true
            ];
        }

        // Check for active enrollment
        $enrollment = CourseEnrollment::where('user_id', $userId)
            ->where('course_id', $course->id)
            ->where('status', 'active')
            ->first();

        $isEnrolled = $enrollment !== null;
        
        // For free courses, user can access but may not be formally enrolled
        $isFree = $course->is_free || $course->price == 0;
        $canAccess = $isEnrolled || $isFree;

        return [
            'is_enrolled' => $isEnrolled, // Only true for actual enrollments
            'is_tutor' => false,
            'enrollment' => $enrollment,
            'can_access' => $canAccess,
            'is_free' => $isFree,
            'has_paid_enrollment' => $isEnrolled // Actual paid enrollment status
        ];
    }
}