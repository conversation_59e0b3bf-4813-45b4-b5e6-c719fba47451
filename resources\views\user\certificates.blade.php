@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON><PERSON>')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/dashboard-responsive.css') }}">
@endpush

@section('content')
<div class="dashboard-container p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="text-center sm:text-left">
                <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">Serti<PERSON>kat Saya</h1>
                <p class="text-gray-600 mt-1 text-sm sm:text-base">Kole<PERSON>i sertifikat dan pencapaian belajar <PERSON></p>
            </div>
        </div>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6 mb-6 lg:mb-8">
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-green-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Sertifikat</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_certificates'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-blue-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Kursus Selesai</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['course_certificates'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-purple-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Ujian Lulus</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['exam_certificates'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-yellow-300 sm:col-span-2 lg:col-span-1">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Rating Rata-rata</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ number_format($stats['average_rating'], 1) }}</p>
                </div>
            </div>
        </div>
    </div>

    @if($allCertificates->count() > 0)
        <!-- Certificates Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
                @foreach($allCertificates as $certificate)
                    <div class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <!-- Certificate Preview -->
                        <div class="h-36 sm:h-40 lg:h-48 bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-700 relative">
                            <div class="absolute inset-0 bg-black bg-opacity-10"></div>
                            <div class="absolute inset-0 flex items-center justify-center p-3">
                                <div class="text-center text-white">
                                    <div class="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2 sm:mb-3">
                                        <svg class="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <h3 class="font-bold text-sm sm:text-base lg:text-lg line-clamp-2">{{ $certificate['title'] }}</h3>
                                    <p class="text-xs sm:text-sm opacity-90">
                                        @if($certificate['type'] === 'course')
                                            Sertifikat Kursus
                                        @else
                                            Sertifikat Ujian
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <div class="absolute top-2 sm:top-3 lg:top-4 right-2 sm:right-3 lg:right-4">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                    Verified
                                </span>
                            </div>
                        </div>

                        <!-- Certificate Info -->
                        <div class="p-4 sm:p-5 lg:p-6">
                            <div class="mb-4">
                                <h4 class="font-semibold text-gray-900 text-sm sm:text-base line-clamp-2">{{ $certificate['title'] }}</h4>
                                <p class="text-xs sm:text-sm text-gray-600 truncate">{{ $certificate['tutor'] }}</p>
                            </div>

                            <div class="space-y-2 text-xs sm:text-sm text-gray-600 mb-4">
                                <div class="flex justify-between items-center">
                                    <span>Tanggal Selesai:</span>
                                    <span class="font-medium">{{ $certificate['completed_at']->format('d M Y') }}</span>
                                </div>
                                @if($certificate['type'] === 'exam')
                                    <div class="flex justify-between items-center">
                                        <span>Skor:</span>
                                        <span class="font-semibold text-green-600">{{ $certificate['score'] }}%</span>
                                    </div>
                                @else
                                    <div class="flex justify-between items-center">
                                        <span>Level:</span>
                                        <span class="font-semibold text-blue-600">{{ ucfirst($certificate['level']) }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span>Durasi:</span>
                                        <span class="font-medium">{{ $certificate['duration'] }}</span>
                                    </div>
                                @endif
                                <div class="flex justify-between items-start">
                                    <span>ID Sertifikat:</span>
                                    <span class="font-mono text-xs break-all text-right ml-2">{{ $certificate['certificate_id'] }}</span>
                                </div>
                            </div>

                            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                                @if($certificate['type'] === 'course')
                                    @php
                                        $hasCourseCertificateAccess = true;
                                        // For free courses, check if user has paid membership
                                        $course = \App\Models\Course::find($certificate['course_id']);
                                        if ($course && ($course->is_free || $course->price == 0)) {
                                            $membership = $user->activeMembership;
                                            $hasCourseCertificateAccess = $membership && $membership->has_free_certifications;
                                        }
                                    @endphp
                                    
                                    @if($hasCourseCertificateAccess)
                                        <a href="{{ route('course.certificate.preview', $certificate['course_id']) }}"
                                           class="flex-1 btn btn-primary text-xs sm:text-sm min-h-[44px] justify-center" target="_blank">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            <span>Preview</span>
                                        </a>
                                        <a href="{{ route('course.certificate.download', $certificate['course_id']) }}"
                                           class="flex-1 btn btn-outline text-xs sm:text-sm min-h-[44px] justify-center">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Download</span>
                                        </a>
                                    @else
                                        <a href="{{ route('payment.pricing') }}"
                                           class="flex-1 btn btn-primary text-xs sm:text-sm min-h-[44px] justify-center">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                            <span>Upgrade untuk Download Sertifikat</span>
                                        </a>
                                    @endif
                                @else
                                    @php
                                        $hasExamCertificateAccess = $user->activeMembership !== null;
                                    @endphp
                                    
                                    @if($hasExamCertificateAccess)
                                        <a href="{{ route('exam.certificate.preview', $certificate['exam_id']) }}"
                                           class="flex-1 btn btn-primary text-xs sm:text-sm min-h-[44px] justify-center" target="_blank">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            <span>Preview</span>
                                        </a>
                                        <a href="{{ route('exam.certificate.download', $certificate['exam_id']) }}"
                                           class="flex-1 btn btn-outline text-xs sm:text-sm min-h-[44px] justify-center">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Download</span>
                                        </a>
                                    @else
                                        <a href="{{ route('payment.pricing') }}"
                                           class="flex-1 btn btn-primary text-xs sm:text-sm min-h-[44px] justify-center">
                                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                            <span>Upgrade untuk Download Sertifikat</span>
                                        </a>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm p-8 sm:p-12 text-center border border-gray-200">
            <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <svg class="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                </svg>
            </div>
            <h3 class="text-base sm:text-lg font-bold text-gray-900 mb-2">Belum Ada Sertifikat</h3>
            <p class="text-gray-600 mb-6 max-w-md mx-auto text-sm sm:text-base px-4">
                Anda belum memiliki sertifikat. Selesaikan kursus pertama Anda untuk mendapatkan sertifikat yang dapat diverifikasi.
            </p>
            <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
                <a href="{{ route('user.courses') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto">
                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <span>Lihat Kursus</span>
                </a>
                <a href="{{ route('home') }}#courses" class="btn btn-outline min-h-[44px] w-full sm:w-auto">
                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <span>Jelajahi Kursus</span>
                </a>
            </div>
        </div>
    @endif

    <!-- Certificate Verification Info -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 sm:p-5 lg:p-6 mt-6 lg:mt-8 border border-blue-200">
        <div class="space-y-3 sm:space-y-0 sm:flex sm:items-start sm:space-x-3">
            <div class="flex-shrink-0 mx-auto sm:mx-0 w-fit">
                <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
            </div>
            <div class="flex-1 text-center sm:text-left">
                <h3 class="text-sm font-medium text-blue-800 mb-1">Verifikasi Sertifikat</h3>
                <div class="text-xs sm:text-sm text-blue-700">
                    <p class="mb-2">Semua sertifikat Ngambiskuy Advance Learning Assistance dapat diverifikasi secara online menggunakan ID sertifikat unik. Sertifikat ini diakui oleh industri dan dapat digunakan untuk meningkatkan profil profesional Anda.</p>
                    <div>
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500 inline-flex items-center min-h-[44px] px-2 py-1 rounded hover:bg-blue-100 transition-colors">
                            Pelajari lebih lanjut tentang verifikasi sertifikat →
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
