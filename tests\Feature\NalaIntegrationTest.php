<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\UserMembership;
use App\Models\MembershipPlan;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\Category;
use App\Models\Role;
use App\Models\UserRole;
use App\Services\NalaPromptTrackingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;

class NalaIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock Gemini API responses
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => json_encode([
                                        'title' => 'Test Course',
                                        'category' => 'Programming',
                                        'description' => 'Test description',
                                        'level' => 'beginner',
                                        'duration' => 30,
                                        'price' => 100000,
                                        'is_free' => false,
                                        'learning_outcomes' => ['Learn basics'],
                                        'target_audience' => ['Beginners'],
                                        'requirements' => ['None']
                                    ])
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);
    }

    private function createTutorUser($attributes = [])
    {
        $user = User::factory()->create($attributes);

        // Create tutor role if it doesn't exist
        $tutorRole = Role::firstOrCreate([
            'name' => 'tutor'
        ]);

        // Assign tutor role to user
        UserRole::create([
            'user_id' => $user->id,
            'role_id' => $tutorRole->id
        ]);

        return $user;
    }

    /** @test */
    public function authenticated_user_can_access_nala_chat()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)
            ->postJson('/api/nala-chat', [
                'message' => 'Hello Nala',
                'context' => [],
                'current_route' => 'home',
                'current_context' => 'general'
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);
    }

    /** @test */
    public function user_with_membership_can_use_course_builder()
    {
        // Create membership plan
        $membershipPlan = MembershipPlan::create([
            'id' => \Illuminate\Support\Str::uuid(),
            'name' => 'Basic',
            'slug' => 'basic',
            'description' => 'Basic membership',
            'type' => 'individual',
            'price' => 99000,
            'duration_months' => 1,
            'is_free' => false,
            'nala_prompts' => 100,
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create user with active membership
        $user = $this->createTutorUser([
            'current_membership_id' => $membershipPlan->id
        ]);

        // Create active membership
        UserMembership::create([
            'id' => \Illuminate\Support\Str::uuid(),
            'user_id' => $user->id,
            'membership_plan_id' => $membershipPlan->id,
            'status' => 'active',
            'starts_at' => now(),
            'expires_at' => now()->addMonth(),
            'nala_prompts_allocated' => 100,
            'nala_prompts_used_today' => 0,
            'nala_prompts_remaining' => 100,
            'nala_usage_reset_date' => now()->toDateString()
        ]);

        $response = $this->actingAs($user)
            ->postJson('/tutor/ai-course-builder/generate');

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true
        ]);
    }

    /** @test */
    public function free_user_cannot_access_course_builder()
    {
        $user = $this->createTutorUser([
            'current_membership_id' => null
        ]);

        $response = $this->actingAs($user)
            ->postJson('/tutor/ai-course-builder/generate');

        $response->assertStatus(403);
        $response->assertJson([
            'success' => false,
            'error' => 'membership_required'
        ]);
    }

    /** @test */
    public function prompt_usage_is_tracked_across_both_features()
    {
        // Create membership plan
        $membershipPlan = MembershipPlan::create([
            'id' => \Illuminate\Support\Str::uuid(),
            'name' => 'Basic',
            'slug' => 'basic',
            'description' => 'Basic membership',
            'type' => 'individual',
            'price' => 99000,
            'duration_months' => 1,
            'is_free' => false,
            'nala_prompts' => 100,
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create user with active membership
        $user = $this->createTutorUser([
            'current_membership_id' => $membershipPlan->id
        ]);

        // Create active membership with limited prompts
        UserMembership::create([
            'id' => \Illuminate\Support\Str::uuid(),
            'user_id' => $user->id,
            'membership_plan_id' => $membershipPlan->id,
            'status' => 'active',
            'starts_at' => now(),
            'expires_at' => now()->addMonth(),
            'nala_prompts_allocated' => 100,
            'nala_prompts_used_today' => 99, // Almost at limit
            'nala_prompts_remaining' => 1,   // Only 1 prompt left
            'nala_usage_reset_date' => now()->toDateString()
        ]);

        // Use the last prompt via chat
        $chatResponse = $this->actingAs($user)
            ->postJson('/api/nala-chat', [
                'message' => 'Hello Nala',
                'context' => [],
                'current_route' => 'home',
                'current_context' => 'general'
            ]);

        $chatResponse->assertStatus(200);

        // Now course builder should be blocked due to no remaining prompts
        $courseBuilderResponse = $this->actingAs($user)
            ->postJson('/tutor/ai-course-builder/generate');

        $courseBuilderResponse->assertStatus(429); // Too Many Requests
        $courseBuilderResponse->assertJson([
            'success' => false,
            'error' => 'daily_limit_reached'
        ]);
    }

    /** @test */
    public function prompt_tracking_service_works_correctly()
    {
        $user = User::factory()->create();
        $service = new \App\Services\NalaPromptTrackingService();

        // Test basic functionality
        $membershipLevel = $service->getUserMembershipLevel($user);
        $this->assertEquals('free', $membershipLevel);

        $canUse = $service->canUsePrompts($user, 1);
        $this->assertTrue($canUse);

        $statistics = $service->getUsageStatistics($user);
        $this->assertEquals('free', $statistics['membership_level']);
        $this->assertEquals(10, $statistics['daily_limit']);
    }
}
