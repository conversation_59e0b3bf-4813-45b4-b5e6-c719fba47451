<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payout_requests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tutor_id'); // Foreign key to users table

            // Payout Details
            $table->string('request_id')->unique(); // Human-readable request ID
            $table->decimal('amount', 10, 2); // Amount requested for payout
            $table->decimal('platform_fee', 10, 2)->default(0); // Platform fee deducted
            $table->decimal('net_amount', 10, 2); // Net amount after fees
            $table->string('currency', 3)->default('IDR');

            // Status and Processing
            $table->enum('status', ['pending', 'processing', 'approved', 'paid', 'rejected', 'cancelled'])->default('pending');
            $table->text('notes')->nullable(); // Admin notes or rejection reason
            $table->text('tutor_notes')->nullable(); // Tutor's notes for the request

            // Payment Information
            $table->string('payment_method'); // bank_transfer, e_wallet, etc.
            $table->json('payment_details'); // Bank account, e-wallet details, etc.

            // Processing Timestamps
            $table->timestamp('requested_at');
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();

            // Processing Staff
            $table->uuid('processed_by')->nullable(); // Admin who processed the request

            // Metadata
            $table->json('metadata')->nullable(); // Additional data like transaction references

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index(['tutor_id', 'status']);
            $table->index(['status', 'requested_at']);
            $table->index('request_id');
            $table->index('processed_at');
            $table->index('paid_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payout_requests');
    }
};
