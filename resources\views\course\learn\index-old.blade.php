{{-- BACKUP FILE - Original course learning page design --}}
@extends('layouts.app')

@section('title', 'Belajar: ' . $course->title . ' - Ngambiskuy')

@push('styles')
<style>
/* Course Learning Mobile-First Responsive Styles */
.course-learning-page {
    min-height: 100vh;
    background: #f8fafc;
    padding-top: 64px; /* Account for fixed header */
    display: flex;
    flex-direction: column;
}

/* Header positioning fix */
body {
    position: relative !important;
    overflow-x: hidden;
}

header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    width: 100% !important;
}

/* Course Header */
.course-header {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    position: relative;
    padding: 1.5rem 0;
}

@media (min-width: 768px) {
    .course-header {
        padding: 2rem 0;
    }
}

.course-header-pattern {
    position: absolute;
    inset: 0;
    opacity: 0.05;
    background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');
}

.course-header-content {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

@media (min-width: 1024px) {
    .course-header-content {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

/* Breadcrumb */
.course-breadcrumb {
    margin-bottom: 1.5rem;
}

.course-breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    flex-wrap: wrap;
}

.course-breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    min-height: 44px;
    padding: 0.25rem 0;
}

.course-breadcrumb-link:hover {
    color: white;
}

.course-breadcrumb-current {
    color: white;
    font-weight: 500;
}

.course-breadcrumb-icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.25rem;
}

.course-breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 0.5rem;
}

/* Course Info */
.course-info {
    flex: 1;
}

.course-title {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .course-title {
        font-size: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .course-title {
        font-size: 2.5rem;
    }
}

.course-description {
    font-size: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    max-width: 48rem;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .course-description {
        font-size: 1.125rem;
    }
}

.course-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    font-size: 0.875rem;
}

.course-stat-item {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.8);
}

.course-stat-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* Progress Card */
.course-progress-card {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
}

.course-progress-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.course-progress-ring {
    position: relative;
    width: 4rem;
    height: 4rem;
}

.course-progress-svg {
    width: 4rem;
    height: 4rem;
    transform: rotate(-90deg);
}

.course-progress-bg {
    color: rgba(255, 255, 255, 0.3);
}

.course-progress-fill {
    color: white;
    transition: stroke-dasharray 0.35s ease;
}

.course-progress-text {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    font-weight: 700;
    color: white;
}

.course-progress-info {
    color: white;
}

.course-progress-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.course-progress-stats {
    font-size: 1.125rem;
    font-weight: 600;
}

/* Main Content Layout - Professional like Udemy/Coursera */
.course-main-content {
    position: relative;
    flex: 1;
    display: flex;
    background: #f8fafc;
}

.course-content-wrapper {
    display: flex;
    width: 100%;
    min-height: calc(100vh - 200px);
    position: relative;
}

/* Desktop Layout with Sidebar */
@media (min-width: 1024px) {
    .course-content-wrapper {
        padding-left: 320px; /* Space for fixed sidebar */
    }
}

.course-content-layout {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

@media (max-width: 1023px) {
    .course-content-layout {
        padding: 1rem;
    }
}

/* Professional Curriculum Sidebar - Udemy/Coursera Style */
.curriculum-toggle {
    position: fixed;
    top: 50%;
    left: 1rem;
    z-index: 60;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    padding: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 48px;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e7eb;
    cursor: pointer;
    transform: translateY(-50%);
}

@media (min-width: 1024px) {
    .curriculum-toggle {
        display: none;
    }
}

.curriculum-toggle:hover {
    background: #f8fafc;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    transform: translateY(-50%) scale(1.05);
}

.curriculum-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

.curriculum-sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e5e7eb;
    overflow: hidden;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 50;
    display: flex;
    flex-direction: column;
}

/* Mobile Sidebar */
@media (max-width: 1023px) {
    .curriculum-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 55;
        transform: translateX(-100%);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border-right: none;
    }

    .curriculum-sidebar.show {
        transform: translateX(0);
    }
}

/* Desktop Sidebar */
@media (min-width: 1024px) {
    .curriculum-sidebar {
        position: fixed;
        top: 64px; /* Below header */
        left: 0;
        height: calc(100vh - 64px);
        border-radius: 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
}

/* Professional Sidebar Header */
.curriculum-header {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.curriculum-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.curriculum-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    letter-spacing: -0.025em;
}

.curriculum-close {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    min-height: 40px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

@media (min-width: 1024px) {
    .curriculum-close {
        display: none;
    }
}

.curriculum-close:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.curriculum-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-weight: 400;
}

/* Scrollable Content Area */
.curriculum-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #fafbfc;
}

/* Custom Scrollbar */
.curriculum-content::-webkit-scrollbar {
    width: 6px;
}

.curriculum-content::-webkit-scrollbar-track {
    background: #f1f3f4;
}

.curriculum-content::-webkit-scrollbar-thumb {
    background: #d1d7dc;
    border-radius: 3px;
}

.curriculum-content::-webkit-scrollbar-thumb:hover {
    background: #a7b0b8;
}

.curriculum-chapters {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* Professional Chapter Components */
.chapter-container {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.chapter-container:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chapter-header {
    padding: 1rem;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

.chapter-header-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.chapter-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
    flex: 1;
}

.chapter-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.chapter-lesson-count {
    font-size: 0.75rem;
    color: #6b7280;
    background: #e5e7eb;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    white-space: nowrap;
}

.chapter-description {
    font-size: 0.8125rem;
    color: #6b7280;
    line-height: 1.4;
    margin: 0;
}

/* Professional Lesson List */
.chapter-lessons {
    padding: 0;
    background: #fafbfc;
}

.lesson-item {
    display: block;
    padding: 0.75rem 1rem;
    margin: 0;
    border: none;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 60px;
    position: relative;
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-item:hover {
    background: #f8fafc;
    border-left: 3px solid #2563eb;
    padding-left: calc(1rem - 3px);
}

.lesson-item-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
}

.lesson-type-icon {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.lesson-item:hover .lesson-type-icon {
    transform: scale(1.1);
}

.lesson-type-video {
    background: #dbeafe;
}

.lesson-item:hover .lesson-type-video {
    background: #bfdbfe;
}

.lesson-type-text {
    background: #dcfce7;
}

.lesson-item:hover .lesson-type-text {
    background: #bbf7d0;
}

.lesson-type-quiz {
    background: #f3e8ff;
}

.lesson-item:hover .lesson-type-quiz {
    background: #e9d5ff;
}

.lesson-type-assignment {
    background: #fed7aa;
}

.lesson-item:hover .lesson-type-assignment {
    background: #fdba74;
}

.lesson-info {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
    transition: color 0.2s ease;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.lesson-item:hover .lesson-title {
    color: #2563eb;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    flex-wrap: wrap;
}

.lesson-duration {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.lesson-type-label {
    background: #e5e7eb;
    color: #374151;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-weight: 500;
    text-transform: capitalize;
    font-size: 0.6875rem;
}

/* Professional Lesson Status Indicators */
.lesson-status {
    flex-shrink: 0;
    margin-left: auto;
}

.lesson-status-completed {
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3);
}

.lesson-status-in-progress {
    background: #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3);
}

.lesson-status-not-started {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    background: white;
    transition: all 0.2s ease;
}

.lesson-item:hover .lesson-status-not-started {
    border-color: #2563eb;
}

/* Professional Enhancements */
.curriculum-sidebar {
    backdrop-filter: blur(10px);
}

/* Smooth animations for better UX */
.lesson-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus states for accessibility */
.curriculum-toggle:focus,
.curriculum-close:focus,
.lesson-item:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Loading state for lessons */
.lesson-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Active lesson highlight */
.lesson-item.active {
    background: #eff6ff;
    border-left: 3px solid #2563eb;
    padding-left: calc(1rem - 3px);
}

.lesson-item.active .lesson-title {
    color: #2563eb;
    font-weight: 600;
}

/* Professional Main Content Area */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-height: 0; /* Allow content to shrink */
}

/* No additional margin needed - handled by course-content-wrapper padding */

/* Progress Overview Card */
.progress-overview-card {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    color: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
    .progress-overview-card {
        padding: 2rem;
    }
}

.progress-overview-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .progress-overview-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

.progress-overview-info h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
    .progress-overview-info h2 {
        font-size: 1.875rem;
    }
}

.progress-overview-info p {
    color: rgba(219, 234, 254, 0.9);
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .progress-overview-info p {
        font-size: 1rem;
    }
}

.progress-overview-percentage {
    text-align: center;
}

.progress-overview-percentage-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
    .progress-overview-percentage-value {
        font-size: 3rem;
    }
}

.progress-overview-percentage-label {
    color: rgba(219, 234, 254, 0.8);
    font-size: 0.875rem;
}

.progress-overview-bar {
    margin-bottom: 1.5rem;
}

.progress-overview-bar-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: rgba(219, 234, 254, 0.8);
    margin-bottom: 0.5rem;
}

.progress-overview-bar-track {
    width: 100%;
    background: rgba(37, 99, 235, 0.3);
    border-radius: 9999px;
    height: 0.75rem;
}

.progress-overview-bar-fill {
    background: white;
    height: 0.75rem;
    border-radius: 9999px;
    transition: width 0.5s ease;
}

.progress-overview-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.progress-overview-stat {
    text-align: center;
}

.progress-overview-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
}

@media (min-width: 768px) {
    .progress-overview-stat-value {
        font-size: 1.875rem;
    }
}

.progress-overview-stat-label {
    color: rgba(219, 234, 254, 0.8);
    font-size: 0.875rem;
}

/* Continue Learning Card */
.continue-learning-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
    .continue-learning-card {
        padding: 2rem;
    }
}

.continue-learning-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
    .continue-learning-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

.continue-learning-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    display: flex;
    align-items: center;
}

.continue-learning-title-icon {
    width: 2rem;
    height: 2rem;
    background: #10b981;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
}

.continue-learning-badge {
    font-size: 0.875rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    font-weight: 500;
}

.continue-learning-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .continue-learning-content {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

.continue-learning-lesson {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex: 1;
}

.continue-learning-lesson-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.continue-learning-lesson-info h4 {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.25rem;
}

@media (min-width: 768px) {
    .continue-learning-lesson-info h4 {
        font-size: 1.25rem;
    }
}

.continue-learning-lesson-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.continue-learning-lesson-description {
    color: #6b7280;
    font-size: 0.875rem;
}

.continue-learning-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .continue-learning-actions {
        flex-direction: row;
        gap: 0.75rem;
    }
}

.continue-learning-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    border: none;
    cursor: pointer;
}

.continue-learning-btn-primary {
    background: #2563eb;
    color: white;
}

.continue-learning-btn-primary:hover {
    background: rgba(37, 99, 235, 0.9);
}

.continue-learning-btn-secondary {
    background: #dbeafe;
    color: #1e40af;
}

.continue-learning-btn-secondary:hover {
    background: #bfdbfe;
}

/* Completion Card */
.completion-card {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-radius: 0.75rem;
    padding: 2rem;
    border: 1px solid #a7f3d0;
    text-align: center;
}

.completion-icon {
    width: 5rem;
    height: 5rem;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.completion-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.75rem;
}

@media (min-width: 768px) {
    .completion-title {
        font-size: 1.875rem;
    }
}

.completion-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

@media (min-width: 768px) {
    .completion-description {
        font-size: 1.125rem;
    }
}

.completion-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
}

@media (min-width: 640px) {
    .completion-actions {
        flex-direction: row;
        gap: 1rem;
    }
}

.completion-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.completion-btn-secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
}

.completion-btn-secondary:hover {
    background: #f9fafb;
}

.completion-btn-primary {
    background: #2563eb;
    color: white;
}

.completion-btn-primary:hover {
    background: rgba(37, 99, 235, 0.9);
}

/* NALA AI Assistant CTA */
.nala-cta-card {
    background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%);
    border-radius: 0.75rem;
    padding: 1.5rem;
    border: 1px solid #c7d2fe;
}

@media (min-width: 768px) {
    .nala-cta-card {
        padding: 2rem;
    }
}

.nala-cta-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .nala-cta-content {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
}

.nala-cta-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media (min-width: 768px) {
    .nala-cta-info {
        flex-direction: row;
        align-items: center;
        gap: 1.5rem;
    }
}

.nala-cta-icon {
    width: 4rem;
    height: 4rem;
    background: #3b82f6;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.nala-cta-text h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
    .nala-cta-text h3 {
        font-size: 1.5rem;
    }
}

.nala-cta-text p {
    color: #6b7280;
    margin-bottom: 0.75rem;
    line-height: 1.5;
}

.nala-cta-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    font-size: 0.875rem;
    color: #6b7280;
}

@media (min-width: 640px) {
    .nala-cta-features {
        flex-direction: row;
        gap: 1rem;
    }
}

.nala-cta-feature {
    display: flex;
    align-items: center;
}

.nala-cta-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .nala-cta-actions {
        flex-direction: row;
        gap: 0.75rem;
    }
}

.nala-cta-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.2s ease;
    min-height: 44px;
    border: none;
    cursor: pointer;
}

.nala-cta-btn-primary {
    background: #2563eb;
    color: white;
}

.nala-cta-btn-primary:hover {
    background: #1d4ed8;
}

.nala-cta-link {
    color: #2563eb;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    text-align: center;
    transition: color 0.2s ease;
}

.nala-cta-link:hover {
    color: #1d4ed8;
}

/* Learning Progress Insights */
.learning-insights-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .learning-insights-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.insight-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
}

.insight-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.insight-card-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}

.insight-card-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #111827;
}

.progress-detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.progress-detail-item:last-child {
    margin-bottom: 0;
}

.progress-detail-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.progress-detail-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-detail-label {
    font-weight: 500;
    color: #111827;
}

.progress-detail-value {
    font-size: 1.125rem;
    font-weight: 700;
}

.motivation-card {
    padding: 1rem;
    border-radius: 0.5rem;
    border: 1px solid;
    margin-bottom: 1rem;
}

.motivation-card h4 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.motivation-card p {
    font-size: 0.875rem;
    line-height: 1.4;
}

.motivation-stats {
    text-align: center;
}

.motivation-stats-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 0.25rem;
}

.motivation-stats-label {
    color: #6b7280;
    font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
    text-decoration: none;
}

.quick-action-btn:hover {
    background: #f3f4f6;
    transform: translateY(-1px);
}

.quick-action-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.quick-action-btn:hover .quick-action-icon {
    transform: scale(1.05);
}

.quick-action-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    text-align: center;
}

/* Instructor Card */
.instructor-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #f3f4f6;
}

@media (min-width: 768px) {
    .instructor-card {
        padding: 2rem;
    }
}

.instructor-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 1.5rem;
}

.instructor-card-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media (min-width: 640px) {
    .instructor-card-content {
        flex-direction: row;
        align-items: center;
        gap: 1.5rem;
    }
}

.instructor-avatar {
    width: 4rem;
    height: 4rem;
    background: #2563eb;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.instructor-info {
    flex: 1;
}

.instructor-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.25rem;
}

.instructor-role {
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.instructor-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.instructor-stat {
    display: flex;
    align-items: center;
}

.instructor-contact-btn {
    background: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.875rem;
    transition: background-color 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.instructor-contact-btn:hover {
    background: rgba(37, 99, 235, 0.9);
}
</style>
@endpush

@section('content')
<div class="course-learning-page">
    <!-- Course Header -->
    <div class="course-header">
        <div class="course-header-pattern"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="course-header-content">
                <div class="course-info">
                    <!-- Enhanced Breadcrumb -->
                    <nav class="course-breadcrumb">
                        <ol class="course-breadcrumb-list">
                            <li>
                                <a href="{{ route('home') }}" class="course-breadcrumb-link">
                                    <svg class="course-breadcrumb-icon" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                                    </svg>
                                    Beranda
                                </a>
                            </li>
                            <li><span class="course-breadcrumb-separator">•</span></li>
                            <li><a href="{{ route('courses.index') }}" class="course-breadcrumb-link">Kursus</a></li>
                            <li><span class="course-breadcrumb-separator">•</span></li>
                            <li><a href="{{ route('course.show', $course) }}" class="course-breadcrumb-link">{{ Str::limit($course->title, 20) }}</a></li>
                            <li><span class="course-breadcrumb-separator">•</span></li>
                            <li class="course-breadcrumb-current">Belajar</li>
                        </ol>
                    </nav>

                    <h1 class="course-title">{{ $course->title }}</h1>
                    <p class="course-description">{{ $course->description }}</p>

                    <!-- Course Stats -->
                    <div class="course-stats">
                        <div class="course-stat-item">
                            <svg class="course-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                            {{ $totalLessons }} Materi
                        </div>
                        <div class="course-stat-item">
                            <svg class="course-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            {{ $completedLessons }} Selesai
                        </div>
                        <div class="course-stat-item">
                            <svg class="course-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                            {{ $course->tutor->name }}
                        </div>
                    </div>
                </div>

                <!-- Progress Card -->
                <div class="course-progress-card">
                    <div class="course-progress-content">
                        <!-- Circular Progress -->
                        <div class="course-progress-ring">
                            <svg class="course-progress-svg" viewBox="0 0 36 36">
                                <path class="course-progress-bg" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                <path class="course-progress-fill" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="{{ $progressPercentage }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            </svg>
                            <div class="course-progress-text">{{ $progressPercentage }}%</div>
                        </div>
                        <div class="course-progress-info">
                            <div class="course-progress-label">Progress Kursus</div>
                            <div class="course-progress-stats">{{ $completedLessons }}/{{ $totalLessons }} Materi</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="course-main-content">
        <!-- Curriculum Sidebar Toggle Button -->
        <button id="curriculum-toggle" class="curriculum-toggle">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>

        <div class="course-content-wrapper">
            <div class="course-content-layout">

                <!-- Collapsible Curriculum Sidebar -->
                <div id="curriculum-sidebar" class="curriculum-sidebar">
                    <!-- Sidebar Header -->
                    <div class="curriculum-header">
                        <div class="curriculum-header-content">
                            <h2 class="curriculum-title">Kurikulum Kursus</h2>
                            <button id="sidebar-close" class="curriculum-close">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="curriculum-subtitle">
                            {{ $course->chapters->count() }} Bab • {{ $totalLessons }} Materi
                        </div>
                    </div>

                    <!-- Curriculum Content -->
                    <div class="curriculum-content">
                        <div class="curriculum-chapters">
                            @foreach($course->chapters as $chapter)
                                <div class="chapter-container">
                                    <!-- Chapter Header -->
                                    <div class="chapter-header">
                                        <div class="chapter-header-content">
                                            <h3 class="chapter-title">{{ $chapter->title }}</h3>
                                            <div class="chapter-meta">
                                                <span class="chapter-lesson-count">
                                                    {{ $chapter->lessons->count() }} materi
                                                </span>
                                            </div>
                                        </div>
                                        @if($chapter->description)
                                            <p class="chapter-description">{{ $chapter->description }}</p>
                                        @endif
                                    </div>

                                    <!-- Chapter Lessons -->
                                    <div class="chapter-lessons">
                                        @foreach($chapter->lessons as $lesson)
                                            @php
                                                $progress = $userProgress->get($lesson->id);
                                                $isCompleted = $progress && $progress->status === 'completed';
                                                $isInProgress = $progress && $progress->status === 'in_progress';
                                            @endphp

                                            <a href="{{ route('course.lesson', [$course, $lesson]) }}" class="lesson-item">
                                                <div class="lesson-item-content">
                                                    <!-- Lesson Type Icon -->
                                                    <div class="lesson-type-icon
                                                        @if($lesson->type === 'video') lesson-type-video
                                                        @elseif($lesson->type === 'text') lesson-type-text
                                                        @elseif($lesson->type === 'quiz') lesson-type-quiz
                                                        @else lesson-type-assignment
                                                        @endif">
                                                        @if($lesson->type === 'video')
                                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                            </svg>
                                                        @elseif($lesson->type === 'text')
                                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                            </svg>
                                                        @elseif($lesson->type === 'quiz')
                                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                            </svg>
                                                        @else
                                                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                                            </svg>
                                                        @endif
                                                    </div>

                                                    <!-- Lesson Info -->
                                                    <div class="lesson-info">
                                                        <h4 class="lesson-title">{{ $lesson->title }}</h4>
                                                        <div class="lesson-meta">
                                                            @if($lesson->duration_minutes)
                                                                <span class="lesson-duration">
                                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                                    </svg>
                                                                    {{ $lesson->duration_minutes }} menit
                                                                </span>
                                                            @endif
                                                            <span class="lesson-type-label">{{ $lesson->type }}</span>
                                                        </div>
                                                    </div>

                                                    <!-- Progress Status -->
                                                    <div class="lesson-status">
                                                        @if($isCompleted)
                                                            <div class="lesson-status-completed">
                                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2.5">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"/>
                                                                </svg>
                                                            </div>
                                                        @elseif($isInProgress)
                                                            <div class="lesson-status-in-progress">
                                                                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                                                    <circle cx="12" cy="12" r="12"/>
                                                                </svg>
                                                            </div>
                                                        @else
                                                            <div class="lesson-status-not-started"></div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </a>
                                        @endforeach
                                    </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Main Learning Content -->
            <div class="main-content">
                <!-- Progress Overview Card -->
                <div class="progress-overview-card">
                    <div class="progress-overview-header">
                        <div class="progress-overview-info">
                            <h2>Progress Pembelajaran Anda</h2>
                            <p>Terus tingkatkan skill dan raih tujuan karir Anda!</p>
                        </div>
                        <div class="progress-overview-percentage">
                            <div class="progress-overview-percentage-value">{{ $progressPercentage }}%</div>
                            <div class="progress-overview-percentage-label">Selesai</div>
                        </div>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-overview-bar">
                        <div class="progress-overview-bar-info">
                            <span>{{ $completedLessons }} dari {{ $totalLessons }} materi selesai</span>
                            <span>{{ $totalLessons - $completedLessons }} materi tersisa</span>
                        </div>
                        <div class="progress-overview-bar-track">
                            <div class="progress-overview-bar-fill" style="width: {{ $progressPercentage }}%"></div>
                        </div>
                    </div>

                    <!-- Learning Stats -->
                    <div class="progress-overview-stats">
                        <div class="progress-overview-stat">
                            <div class="progress-overview-stat-value">{{ $enrollment->created_at->diffInDays(now()) }}</div>
                            <div class="progress-overview-stat-label">Hari Belajar</div>
                        </div>
                        <div class="progress-overview-stat">
                            <div class="progress-overview-stat-value">{{ floor($progressPercentage/25) }}</div>
                            <div class="progress-overview-stat-label">Achievement</div>
                        </div>
                        <div class="progress-overview-stat">
                            <div class="progress-overview-stat-value">{{ $completedLessons }}</div>
                            <div class="progress-overview-stat-label">Materi Selesai</div>
                        </div>
                    </div>
                </div>

                <!-- Continue Learning Section -->
                @if($nextLesson)
                    <div class="continue-learning-card">
                        <div class="continue-learning-header">
                            <h3 class="continue-learning-title">
                                <div class="continue-learning-title-icon">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </div>
                                Lanjutkan Pembelajaran
                            </h3>
                            <span class="continue-learning-badge">
                                Materi Selanjutnya
                            </span>
                        </div>

                        <div class="continue-learning-content">
                            <div class="continue-learning-lesson">
                                <!-- Lesson Icon -->
                                @if($nextLesson->type === 'video')
                                    <div class="continue-learning-lesson-icon" style="background-color: #3b82f6;">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                    </div>
                                @elseif($nextLesson->type === 'text')
                                    <div class="continue-learning-lesson-icon" style="background-color: #10b981;">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                @elseif($nextLesson->type === 'quiz')
                                    <div class="continue-learning-lesson-icon" style="background-color: #8b5cf6;">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                @else
                                    <div class="continue-learning-lesson-icon" style="background-color: #f59e0b;">
                                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                        </svg>
                                    </div>
                                @endif

                                <div class="continue-learning-lesson-info">
                                    <h4>{{ $nextLesson->title }}</h4>
                                    <div class="continue-learning-lesson-meta">
                                        <span style="display: flex; align-items: center;">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                            </svg>
                                            {{ $nextLesson->chapter->title }}
                                        </span>
                                        @if($nextLesson->duration_minutes)
                                            <span style="display: flex; align-items: center;">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                {{ $nextLesson->duration_minutes }} menit
                                            </span>
                                        @endif
                                        <span style="background: #f3f4f6; color: #374151; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 500; text-transform: capitalize;">
                                            {{ $nextLesson->type }}
                                        </span>
                                    </div>
                                    <p class="continue-learning-lesson-description">Lanjutkan progress pembelajaran Anda dan tingkatkan skill!</p>
                                </div>
                            </div>

                            <div class="continue-learning-actions">
                                <a href="{{ route('course.lesson', [$course, $nextLesson]) }}"
                                   class="continue-learning-btn continue-learning-btn-primary">
                                    <span>Lanjutkan Belajar</span>
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </a>
                                <button class="continue-learning-btn continue-learning-btn-secondary">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    <span>Chat dengan NALA</span>
                                </button>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="completion-card">
                        <div class="completion-icon">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="completion-title">🎉 Selamat! Pembelajaran Selesai</h3>
                        <p class="completion-description">Anda telah menyelesaikan semua materi dalam kursus ini dengan baik!</p>
                        <div class="completion-actions">
                            <a href="{{ route('course.show', $course) }}" class="completion-btn completion-btn-secondary">
                                Lihat Detail Kursus
                            </a>
                            <a href="{{ route('courses.index') }}" class="completion-btn completion-btn-primary">
                                Jelajahi Kursus Lain
                            </a>
                        </div>
                    </div>
                @endif

                <!-- NALA AI Assistant CTA -->
                <div class="nala-cta-card">
                    <div class="nala-cta-content">
                        <div class="nala-cta-info">
                            <div class="nala-cta-icon">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                            <div class="nala-cta-text">
                                <h3>Tingkatkan Pembelajaran dengan NALA AI</h3>
                                <p>Dapatkan bantuan personal AI untuk memahami materi lebih dalam, latihan soal, dan panduan karir yang disesuaikan dengan progress Anda.</p>
                                <div class="nala-cta-features">
                                    <span class="nala-cta-feature">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Penjelasan materi personal
                                    </span>
                                    <span class="nala-cta-feature">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Latihan soal adaptif
                                    </span>
                                    <span class="nala-cta-feature">
                                        <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Prediksi karir
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="nala-cta-actions">
                            <button class="nala-cta-btn nala-cta-btn-primary">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                <span>Chat dengan NALA</span>
                            </button>
                            <a href="#" class="nala-cta-link">
                                Upgrade ke NALA Membership →
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Learning Progress Insights -->
                <div class="learning-insights-grid">
                    <!-- Progress Breakdown -->
                    <div class="insight-card">
                        <div class="insight-card-header">
                            <div class="insight-card-icon" style="background-color: #10b981;">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                            <h3 class="insight-card-title">Progress Detail</h3>
                        </div>
                        <div>
                            <div class="progress-detail-item">
                                <div class="progress-detail-info">
                                    <div class="progress-detail-icon" style="background-color: #dcfce7;">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                    </div>
                                    <span class="progress-detail-label">Materi Selesai</span>
                                </div>
                                <span class="progress-detail-value" style="color: #059669;">{{ $completedLessons }}</span>
                            </div>
                            <div class="progress-detail-item">
                                <div class="progress-detail-info">
                                    <div class="progress-detail-icon" style="background-color: #fed7aa;">
                                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <span class="progress-detail-label">Sisa Materi</span>
                                </div>
                                <span class="progress-detail-value" style="color: #ea580c;">{{ $totalLessons - $completedLessons }}</span>
                            </div>
                            <div class="progress-detail-item">
                                <div class="progress-detail-info">
                                    <div class="progress-detail-icon" style="background-color: #dbeafe;">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                        </svg>
                                    </div>
                                    <span class="progress-detail-label">Achievement</span>
                                </div>
                                <span class="progress-detail-value" style="color: #2563eb;">{{ floor($progressPercentage/25) }}/4</span>
                            </div>
                        </div>
                    </div>

                    <!-- Learning Motivation -->
                    <div class="insight-card">
                        <div class="insight-card-header">
                            <div class="insight-card-icon" style="background-color: #8b5cf6;">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                </svg>
                            </div>
                            <h3 class="insight-card-title">Motivasi Belajar</h3>
                        </div>
                        <div>
                            @if($progressPercentage >= 75)
                                <div class="motivation-card" style="background-color: #f0fdf4; border-color: #bbf7d0; color: #166534;">
                                    <h4 style="color: #166534;">🎉 Hampir Selesai!</h4>
                                    <p>Anda sudah sangat dekat dengan menyelesaikan kursus ini. Tetap semangat!</p>
                                </div>
                            @elseif($progressPercentage >= 50)
                                <div class="motivation-card" style="background-color: #eff6ff; border-color: #bfdbfe; color: #1e40af;">
                                    <h4 style="color: #1e40af;">💪 Setengah Perjalanan!</h4>
                                    <p>Progress yang luar biasa! Anda sudah melewati setengah dari materi kursus.</p>
                                </div>
                            @elseif($progressPercentage >= 25)
                                <div class="motivation-card" style="background-color: #fffbeb; border-color: #fed7aa; color: #92400e;">
                                    <h4 style="color: #92400e;">🚀 Momentum Bagus!</h4>
                                    <p>Anda sudah membangun momentum yang baik. Lanjutkan pembelajaran!</p>
                                </div>
                            @else
                                <div class="motivation-card" style="background-color: #faf5ff; border-color: #e9d5ff; color: #7c2d12;">
                                    <h4 style="color: #7c2d12;">🌟 Mulai Perjalanan!</h4>
                                    <p>Setiap ahli pernah menjadi pemula. Mulai perjalanan pembelajaran Anda!</p>
                                </div>
                            @endif

                            <div class="motivation-stats">
                                <div class="motivation-stats-value">{{ $enrollment->created_at->diffInDays(now()) }} Hari</div>
                                <div class="motivation-stats-label">Sudah belajar di kursus ini</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions-card">
                    <div class="insight-card-header">
                        <div class="insight-card-icon" style="background-color: #f59e0b;">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="insight-card-title">Aksi Cepat</h3>
                    </div>

                    <div class="quick-actions-grid">
                        <button class="quick-action-btn">
                            <div class="quick-action-icon" style="background-color: #3b82f6;">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                            </div>
                            <span class="quick-action-label">Download Materi</span>
                        </button>

                        <button class="quick-action-btn">
                            <div class="quick-action-icon" style="background-color: #10b981;">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <span class="quick-action-label">Catatan</span>
                        </button>

                        <button class="quick-action-btn">
                            <div class="quick-action-icon" style="background-color: #8b5cf6;">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"/>
                                </svg>
                            </div>
                            <span class="quick-action-label">Forum</span>
                        </button>

                        <button class="quick-action-btn">
                            <div class="quick-action-icon" style="background-color: #ef4444;">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                                </svg>
                            </div>
                            <span class="quick-action-label">Bagikan</span>
                        </button>
                    </div>
                </div>

                <!-- Instructor Card -->
                <div class="instructor-card">
                    <h3 class="instructor-card-title">Tentang Instruktur</h3>
                    <div class="instructor-card-content">
                        <div class="instructor-avatar">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <div class="instructor-info">
                            <h4 class="instructor-name">{{ $course->tutor->name }}</h4>
                            <p class="instructor-role">Instruktur Kursus</p>
                            <div class="instructor-stats">
                                <span class="instructor-stat">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                    </svg>
                                    Instruktur Berpengalaman
                                </span>
                                <span class="instructor-stat">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    1,200+ Students
                                </span>
                            </div>
                        </div>
                        <button class="instructor-contact-btn">
                            Kirim Pesan
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden transition-opacity duration-300"></div>
</div>

@push('scripts')
<script>
// Course Learning Page JavaScript - Scoped to avoid conflicts
(function() {
    'use strict';

    // Only run on course learning page
    if (!document.querySelector('.course-learning-page')) {
        return;
    }

    class CourseLearningPage {
        constructor() {
            this.curriculumToggle = document.getElementById('curriculum-toggle');
            this.curriculumSidebar = document.getElementById('curriculum-sidebar');
            this.sidebarClose = document.getElementById('sidebar-close');
            this.sidebarOverlay = document.getElementById('sidebar-overlay');

            this.init();
        }

        init() {
            this.bindEvents();
            this.setupProgressAnimations();
            this.setupStatsCardAnimations();
            this.addKeyboardNavigation();
            this.addProgressTracking();
            this.addSmoothScrolling();
        }

        bindEvents() {
            // Toggle sidebar on mobile
            if (this.curriculumToggle) {
                this.curriculumToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.openSidebar();
                });
            }

            // Close sidebar events
            if (this.sidebarClose) {
                this.sidebarClose.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.closeSidebar();
                });
            }

            if (this.sidebarOverlay) {
                this.sidebarOverlay.addEventListener('click', () => this.closeSidebar());
            }

            // Close sidebar on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.curriculumSidebar?.classList.contains('show')) {
                    this.closeSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    this.closeSidebar();
                }
            });

            // Prevent body scroll when sidebar is open on mobile
            this.handleBodyScroll();
        }

        openSidebar() {
            if (this.curriculumSidebar) {
                this.curriculumSidebar.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent background scroll
            }
            if (this.sidebarOverlay) {
                this.sidebarOverlay.classList.remove('hidden');
            }
        }

        closeSidebar() {
            if (this.curriculumSidebar) {
                this.curriculumSidebar.classList.remove('show');
                document.body.style.overflow = ''; // Restore scroll
            }
            if (this.sidebarOverlay) {
                this.sidebarOverlay.classList.add('hidden');
            }
        }

        handleBodyScroll() {
            // Ensure body scroll is restored when sidebar is closed
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        if (!this.curriculumSidebar?.classList.contains('show')) {
                            document.body.style.overflow = '';
                        }
                    }
                });
            });

            if (this.curriculumSidebar) {
                observer.observe(this.curriculumSidebar, { attributes: true });
            }
        }

        // Enhanced functionality for better UX
        addKeyboardNavigation() {
            // Add keyboard navigation for lesson items
            const lessonItems = document.querySelectorAll('.lesson-item');
            lessonItems.forEach((item, index) => {
                item.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowDown' && lessonItems[index + 1]) {
                        e.preventDefault();
                        lessonItems[index + 1].focus();
                    } else if (e.key === 'ArrowUp' && lessonItems[index - 1]) {
                        e.preventDefault();
                        lessonItems[index - 1].focus();
                    } else if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        item.click();
                    }
                });
            });
        }

        addProgressTracking() {
            // Track lesson progress visually
            const lessonItems = document.querySelectorAll('.lesson-item');
            lessonItems.forEach(item => {
                item.addEventListener('click', () => {
                    // Add loading state
                    item.classList.add('loading');

                    // Remove loading state after navigation
                    setTimeout(() => {
                        item.classList.remove('loading');
                    }, 1000);
                });
            });
        }

        addSmoothScrolling() {
            // Smooth scroll to active lesson in sidebar
            const activeLesson = document.querySelector('.lesson-item.active');
            if (activeLesson && this.curriculumSidebar) {
                const sidebarContent = this.curriculumSidebar.querySelector('.curriculum-content');
                if (sidebarContent) {
                    const offsetTop = activeLesson.offsetTop - sidebarContent.offsetTop - 100;
                    sidebarContent.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        }

        setupProgressAnimations() {
            // Animate progress rings
            const progressRings = document.querySelectorAll('.course-learning-page .progress-ring-circle');
            progressRings.forEach(ring => {
                const strokeDasharray = ring.getAttribute('stroke-dasharray');
                if (strokeDasharray) {
                    ring.style.strokeDasharray = '0, 100';
                    setTimeout(() => {
                        ring.style.strokeDasharray = strokeDasharray;
                    }, 500);
                }
            });
        }

        setupStatsCardAnimations() {
            // Add intersection observer for stats cards
            const statsCards = document.querySelectorAll('.course-learning-page .stats-card');

            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }
                    });
                }, { threshold: 0.1 });

                statsCards.forEach(card => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    observer.observe(card);
                });
            }
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        new CourseLearningPage();
    });

})();
</script>
@endpush

