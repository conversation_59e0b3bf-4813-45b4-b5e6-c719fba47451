<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Course;
use App\Models\Exam;
use App\Models\CourseEnrollment;
use App\Models\ExamAttempt;
use App\Models\TutorProfile;
use App\Models\MembershipPlan;
use App\Models\UserMembership;
use App\Models\Role;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class CertificateGenerationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tutor;
    protected $course;
    protected $exam;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a tutor user
        $this->tutor = User::factory()->create([
            'name' => 'Test Tutor',
            'email' => '<EMAIL>',
            'tutor_status' => 'approved',
        ]);

        // Assign tutor role
        $this->tutor->syncRoles([Role::USER, Role::TUTOR]);

        // Create tutor profile
        TutorProfile::create([
            'user_id' => $this->tutor->id,
            'full_name' => 'Test Tutor',
            'public_name' => 'Test Tutor',
            'description' => 'Test tutor bio',
            'status' => 'approved',
        ]);

        // Create a regular user
        $this->user = User::factory()->create([
            'name' => 'Test Student',
            'email' => '<EMAIL>',
        ]);

        // Assign user role
        $this->user->syncRoles([Role::USER]);

        // Create a category
        $this->category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'description' => 'Programming courses',
            'is_active' => true,
        ]);

        // Create membership plan and assign to user
        $membershipPlan = MembershipPlan::create([
            'name' => 'Standard',
            'slug' => 'standard',
            'price' => 99000,
            'duration_months' => 1,
            'features' => ['feature1', 'feature2'],
            'is_active' => true,
        ]);

        UserMembership::create([
            'user_id' => $this->user->id,
            'membership_plan_id' => $membershipPlan->id,
            'starts_at' => now(),
            'expires_at' => now()->addMonth(),
            'is_active' => true,
        ]);

        // Create a course
        $this->course = Course::create([
            'title' => 'Test Course for Certificate',
            'description' => 'A test course for certificate generation',
            'tutor_id' => $this->tutor->id,
            'category_id' => $this->category->id,
            'price' => 100000,
            'level' => 'beginner',
            'duration_hours' => 10,
            'status' => 'published',
        ]);

        // Create an exam
        $this->exam = Exam::create([
            'title' => 'Test Exam for Certificate',
            'description' => 'A test exam for certificate generation',
            'tutor_id' => $this->tutor->id,
            'price' => 50000,
            'type' => 'berbayar',
            'time_limit' => 60,
            'max_attempts' => 3,
            'passing_score' => 70,
            'status' => 'published',
        ]);
    }

    /** @test */
    public function course_certificate_template_renders_without_errors()
    {
        // Create enrollment
        $enrollment = CourseEnrollment::create([
            'user_id' => $this->user->id,
            'course_id' => $this->course->id,
            'enrolled_at' => now()->subDays(10),
            'completed_at' => now(),
        ]);

        // Prepare certificate data (same as controller)
        $certificateData = [
            'user' => $this->user,
            'course' => $this->course,
            'enrollment' => $enrollment,
            'completion_date' => Carbon::parse($enrollment->completed_at),
            'certificate_id' => 'TEST-CERT-001',
            'total_lessons' => 5,
            'total_duration' => 600,
            'issue_date' => now(),
        ];

        // Test that the view renders without errors
        $view = view('certificates.course-certificate', $certificateData);
        $html = $view->render();

        // Assert that the HTML contains expected content
        $this->assertStringContainsString('Certificate of Completion', $html);
        $this->assertStringContainsString($this->user->name, $html);
        $this->assertStringContainsString($this->course->title, $html);
        $this->assertStringContainsString($this->tutor->name, $html);
        $this->assertStringContainsString('Ngambiskuy', $html);
        $this->assertStringContainsString('TEST-CERT-001', $html);

        // Assert that no logo references exist
        $this->assertStringNotContainsString('logo_base64', $html);
        $this->assertStringNotContainsString('<img', $html);
        $this->assertStringNotContainsString('logo-section', $html);
    }

    /** @test */
    public function exam_certificate_template_renders_without_errors()
    {
        // Create a passed exam attempt
        $examAttempt = ExamAttempt::create([
            'user_id' => $this->user->id,
            'exam_id' => $this->exam->id,
            'score_percentage' => 85.5,
            'total_questions' => 20,
            'correct_answers' => 17,
            'is_passed' => true,
            'completed_at' => now(),
        ]);

        // Prepare certificate data (same as controller)
        $certificateData = [
            'user' => $this->user,
            'exam' => $this->exam,
            'attempt' => $examAttempt,
            'completion_date' => Carbon::parse($examAttempt->completed_at),
            'certificate_id' => 'EXAM-TEST-001',
            'score_percentage' => $examAttempt->score_percentage,
            'total_questions' => $examAttempt->total_questions,
            'correct_answers' => $examAttempt->correct_answers,
            'issue_date' => now(),
        ];

        // Test that the view renders without errors
        $view = view('certificates.exam-certificate', $certificateData);
        $html = $view->render();

        // Assert that the HTML contains expected content
        $this->assertStringContainsString('Certificate of Achievement', $html);
        $this->assertStringContainsString($this->user->name, $html);
        $this->assertStringContainsString($this->exam->title, $html);
        $this->assertStringContainsString($this->tutor->name, $html);
        $this->assertStringContainsString('Ngambiskuy', $html);
        $this->assertStringContainsString('EXAM-TEST-001', $html);
        $this->assertStringContainsString('85.5%', $html);
        $this->assertStringContainsString('17/20', $html);

        // Assert that no logo references exist
        $this->assertStringNotContainsString('logo_base64', $html);
        $this->assertStringNotContainsString('<img', $html);
        $this->assertStringNotContainsString('logo-section', $html);
    }

    /** @test */
    public function certificate_templates_use_landscape_format()
    {
        // Test course certificate dimensions
        $certificateData = [
            'user' => $this->user,
            'course' => $this->course,
            'enrollment' => new CourseEnrollment(),
            'completion_date' => now(),
            'certificate_id' => 'TEST-CERT-001',
            'total_lessons' => 5,
            'total_duration' => 600,
            'issue_date' => now(),
        ];

        $courseHtml = view('certificates.course-certificate', $certificateData)->render();
        
        // Check for landscape dimensions (297mm x 210mm)
        $this->assertStringContainsString('width: 297mm', $courseHtml);
        $this->assertStringContainsString('height: 210mm', $courseHtml);

        // Test exam certificate dimensions
        $examData = [
            'user' => $this->user,
            'exam' => $this->exam,
            'attempt' => new ExamAttempt(),
            'completion_date' => now(),
            'certificate_id' => 'EXAM-TEST-001',
            'score_percentage' => 85.5,
            'total_questions' => 20,
            'correct_answers' => 17,
            'issue_date' => now(),
        ];

        $examHtml = view('certificates.exam-certificate', $examData)->render();
        
        // Check for landscape dimensions (297mm x 210mm)
        $this->assertStringContainsString('width: 297mm', $examHtml);
        $this->assertStringContainsString('height: 210mm', $examHtml);
    }
}
