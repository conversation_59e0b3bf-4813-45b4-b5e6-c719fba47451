<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use App\Http\Controllers\Nala\UserProfileController;
use App\Http\Controllers\Nala\CareerController;
use App\Http\Controllers\Nala\CourseEngineController;
use App\Http\Controllers\Nala\ExamController;
use App\Http\Controllers\Nala\TutorController;
use App\Services\NalaPromptTrackingService;

class ChatController extends Controller
{
    protected $promptTrackingService;
    private $geminiApiKey;
    private $geminiModel;

    public function __construct(NalaPromptTrackingService $promptTrackingService)
    {
        $this->promptTrackingService = $promptTrackingService;
        $this->geminiApiKey = config('services.gemini.api_key', env('GEMINI_API_KEY'));
        $this->geminiModel = config('services.gemini.model', env('GEMINI_MODEL', 'gemini-2.0-flash'));
    }

    /**
     * Test endpoint for debugging new responses
     */
    public function testChat(Request $request)
    {
        $message = $request->input('message', 'halo nala');

        // Force new conversation for testing
        $conversation = NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => auth()->id(),
            'started_route' => 'test',
            'started_context' => 'test'
        ]);

        $userProfile = ['basic_info' => ['name' => 'Test User']];
        $response = $this->generateGeneralResponse($message, 'test', $userProfile, $conversation);

        return response()->json([
            'success' => true,
            'response' => $response,
            'message' => $message,
            'debug' => 'This is a fresh response with new system prompt'
        ]);
    }

    /**
     * Handle NALA AI chat requests
     */
    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'context' => 'nullable|array',
            'current_route' => 'nullable|string',
            'current_context' => 'nullable|string',
            'conversation_id' => 'nullable|string|exists:nala_chat_conversations,id',
            'tutor_context' => 'nullable|array',
            'has_previous_context' => 'nullable|boolean',
            'message_count' => 'nullable|integer'
        ]);

        try {
            $userMessage = $request->input('message');
            $currentRoute = $request->input('current_route');
            $currentContext = $request->input('current_context');
            $conversationId = $request->input('conversation_id');

            // Get or create conversation
            $conversation = $this->getOrCreateConversation($conversationId, $currentRoute, $currentContext);

            // Store user message
            $this->storeMessage($conversation, 'user', $userMessage);

            // Check for prohibited content first
            $prohibitionCheck = $this->checkProhibitedContent($userMessage);
            if ($prohibitionCheck) {
                $this->storeMessage($conversation, 'ai', $prohibitionCheck, ['is_prohibited' => true]);
                return response()->json([
                    'success' => true,
                    'response' => $prohibitionCheck,
                    'prohibited' => true,
                    'conversation_id' => $conversation->id
                ]);
            }

            // Get user profile and membership
            $userProfile = null;
            $userMembership = 'free';
            $user = null;
            if (auth()->check()) {
                $user = auth()->user();
                $userProfile = app(UserProfileController::class)->buildUserProfile($user);
                $userMembership = $this->promptTrackingService->getUserMembershipLevel($user);
            }

            // Check prompt limits using unified tracking service
            if (auth()->check() && !$this->promptTrackingService->canUsePrompts($user, 1)) {
                $limitResponse = $this->promptTrackingService->getMembershipLimitResponse($userMembership);
                $this->storeMessage($conversation, 'ai', $limitResponse, ['is_limit_reached' => true]);
                return response()->json([
                    'success' => true,
                    'response' => $limitResponse,
                    'limit_reached' => true,
                    'conversation_id' => $conversation->id
                ]);
            }

            // Route to appropriate controller based on context
            $response = $this->routeToSpecializedController($userMessage, $currentContext, $userProfile, $userMembership, $request);

            // If no specialized response, use general AI
            if (!$response) {
                $response = $this->generateGeneralResponse($userMessage, $currentContext, $userProfile, $conversation, $request);
            }

            // Store AI response
            $this->storeMessage($conversation, 'ai', $response);

            // Use prompt from user's daily allocation
            if (auth()->check() && $user) {
                $this->promptTrackingService->usePrompts($user, 1);
            }

            return response()->json([
                'success' => true,
                'response' => $response,
                'conversation_id' => $conversation->id
            ]);

        } catch (\Exception $e) {
            Log::error('Nala Chat Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'response' => 'Maaf, saya sedang mengalami gangguan. Silakan coba lagi dalam beberapa saat.',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Route message to specialized controller
     */
    private function routeToSpecializedController($message, $context, $userProfile, $membership, $request = null)
    {
        $lowerMessage = strtolower($message);

        // Skip routing for basic greetings and simple messages
        if ($this->isBasicGreeting($lowerMessage)) {
            return null; // Handle with general response
        }

        // Only route if message is clearly asking for specific domain help
        if ($this->isSpecificCareerQuestion($lowerMessage)) {
            return app(CareerController::class)->handleCareerQuestion($message, $context, $userProfile, $membership);
        }

        if ($this->isSpecificCourseQuestion($lowerMessage, $context)) {
            // Pass course context if available
            $courseContext = $request ? $request->input('course_context', []) : [];
            return app(CourseEngineController::class)->handleCourseQuestion($message, $context, $userProfile, $membership, $courseContext);
        }

        if ($this->isSpecificExamQuestion($lowerMessage, $context)) {
            return app(ExamController::class)->handleExamQuestion($message, $context, $userProfile, $membership);
        }

        if ($this->isSpecificTutorQuestion($lowerMessage, $context)) {
            // Pass tutor context if available
            $tutorContext = $request ? $request->input('tutor_context', []) : [];

            // Enhanced debug logging
            Log::info('Nala ChatController: Routing to TutorController', [
                'message' => $message,
                'context' => $context,
                'tutor_context_keys' => array_keys($tutorContext),
                'tutor_context_full' => $tutorContext,
                'courses_count' => count($tutorContext['courses'] ?? []),
                'exams_count' => count($tutorContext['exams'] ?? []),
                'blogs_count' => count($tutorContext['blogs'] ?? []),
                'tutor_info' => $tutorContext['tutor'] ?? null,
                'stats_info' => $tutorContext['stats'] ?? null,
                'request_all_data' => $request ? $request->all() : null
            ]);

            return app(TutorController::class)->handleTutorQuestion($message, $context, $userProfile, $membership, $tutorContext);
        }

        return null; // Handle with general response
    }

    /**
     * Generate general AI response
     */
    private function generateGeneralResponse($message, $context, $userProfile, $conversation, $request = null)
    {
        // Build route-specific system prompt with course context if available
        $systemPrompt = $this->buildRouteSpecificSystemPrompt($context, $userProfile, $conversation, $request);

        // Get conversation history (enhanced for better context)
        $hasPreviousContext = $request->input('has_previous_context', false);
        $messageCount = $request->input('message_count', 0);
        $conversationHistory = $this->getConversationHistory($conversation, $hasPreviousContext ? 8 : 3);

        // Build contextual prompt with session continuity awareness
        $contextualPrompt = $this->buildContextualPrompt($message, $context, $conversationHistory, $hasPreviousContext, $messageCount);

        try {
            return $this->callGeminiAPI($systemPrompt, $contextualPrompt);
        } catch (\Exception $e) {
            Log::error('Gemini API Error: ' . $e->getMessage());
            return $this->getFallbackResponse($context);
        }
    }

    /**
     * Build route-specific system prompt
     */
    private function buildRouteSpecificSystemPrompt($context, $userProfile, $conversation, $request = null)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';
        $route = $conversation->started_route ?? 'unknown';
        $isAuthenticated = auth()->check();

        // Get route-specific context
        $routeContext = $this->getRouteSpecificContext($route, $context);

        // Add homepage platform data context if on homepage
        $homepageContextInfo = '';
        if ($route === 'home' || $context === 'homepage') {
            $homepageContextInfo = $this->getHomepageContextInfo();
        }

        // Add course context if available
        $courseContextInfo = '';
        if ($request && $request->input('course_context.course')) {
            $courseData = $request->input('course_context.course');
            $courseContextInfo = "\n\nKONTEKS KURSUS SAAT INI:";
            $courseContextInfo .= "\n- Judul: " . ($courseData['title'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Level: " . ($courseData['level'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Harga: " . ($courseData['price'] ?? 'Tidak diketahui');
            $courseContextInfo .= "\n- Deskripsi: " . ($courseData['description'] ?? 'Tidak diketahui');
            if (isset($courseData['tutor'])) {
                $courseContextInfo .= "\n- Tutor: " . $courseData['tutor'];

                // Add tutor profile link instruction for course detail pages
                if ($context === 'course_detail' || $route === 'course.show') {
                    $tutorSlug = $courseData['tutor_slug'] ?? null;
                    if ($tutorSlug) {
                        $courseContextInfo .= "\n- Tutor Slug: " . $tutorSlug;
                        $courseContextInfo .= "\n\nINSTRUKSI KHUSUS UNTUK TUTOR:";
                        $courseContextInfo .= "\n- Ketika menyebutkan nama tutor, gunakan format natural seperti 'instruktur [Nama](/tutor/" . $tutorSlug . ")' atau 'tutor [Nama](/tutor/" . $tutorSlug . ")'";
                        $courseContextInfo .= "\n- Contoh: 'Kursus ini diajarkan oleh instruktur [" . $courseData['tutor'] . "](/tutor/" . $tutorSlug . ")'";
                        $courseContextInfo .= "\n- Gunakan variasi kata: instruktur, tutor, pengajar, atau mentor";
                        $courseContextInfo .= "\n- WAJIB gunakan format markdown link setiap kali menyebutkan tutor";
                        $courseContextInfo .= "\n- Jangan pernah menyebutkan nama tutor tanpa link";
                    } else {
                        $courseContextInfo .= "\n\nINSTRUKSI KHUSUS UNTUK TUTOR:";
                        $courseContextInfo .= "\n- Tutor belum memiliki profil publik, sebutkan nama tanpa link";
                    }
                }
            }
            $courseContextInfo .= "\n\nGunakan informasi ini untuk memberikan respons yang lebih personal dan relevan tentang kursus ini.";
        }

        // Add tutor context if available
        $tutorContextInfo = '';
        if ($request && $request->input('tutor_context.tutor')) {
            $tutorData = $request->input('tutor_context.tutor');
            $tutorStats = $request->input('tutor_context.stats', []);
            $tutorCourses = $request->input('tutor_context.courses', []);
            $tutorExams = $request->input('tutor_context.exams', []);

            $tutorContextInfo = "\n\nKONTEKS TUTOR SAAT INI:";
            $tutorContextInfo .= "\n- Nama: " . ($tutorData['name'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Posisi: " . ($tutorData['job_title'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Perusahaan: " . ($tutorData['company'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Lokasi: " . ($tutorData['location'] ?? 'Tidak diketahui');


            $tutorContextInfo .= "\n- Pendidikan: " . ($tutorData['education_level'] ?? 'Tidak diketahui');
            $tutorContextInfo .= "\n- Bergabung: " . ($tutorData['joined_date'] ?? 'Tidak diketahui');

            if (!empty($tutorData['skills'])) {
                $tutorContextInfo .= "\n- Keahlian: " . implode(', ', $tutorData['skills']);
            }

            if (!empty($tutorData['description'])) {
                $tutorContextInfo .= "\n- Deskripsi: " . $tutorData['description'];
            }

            $tutorContextInfo .= "\n\nSTATISTIK TUTOR:";
            $tutorContextInfo .= "\n- Total Kursus: " . ($tutorStats['total_courses'] ?? 0);
            $tutorContextInfo .= "\n- Total Ujian: " . ($tutorStats['total_exams'] ?? 0);
            $tutorContextInfo .= "\n- Total Siswa: " . ($tutorStats['total_students'] ?? 0);
            $tutorContextInfo .= "\n- Rating Rata-rata: " . ($tutorStats['average_rating'] ?? 0);

            if (!empty($tutorCourses)) {
                $tutorContextInfo .= "\n\nKURSUS TUTOR:";
                foreach (array_slice($tutorCourses, 0, 3) as $course) {
                    $tutorContextInfo .= "\n- " . ($course['title'] ?? 'Tidak diketahui') . " (" . ($course['level'] ?? 'Tidak diketahui') . ")";
                }
            }

            $tutorContextInfo .= "\n\nGunakan informasi ini untuk memberikan respons yang personal dan relevan tentang tutor ini. Bantu user memahami keahlian dan pengalaman tutor.";
        }

        // Add tutors listing context if available
        $tutorsListingContextInfo = '';
        if ($request && $request->input('tutors_listing_context')) {
            $tutorsData = $request->input('tutors_listing_context');
            $tutorsListingContextInfo = "\n\nKONTEKS HALAMAN TUTOR:";
            $tutorsListingContextInfo .= "\n- Total Tutor: " . ($tutorsData['total_tutors'] ?? 0);
            $tutorsListingContextInfo .= "\n- Filter Aktif: " . ($tutorsData['current_filters']['search'] ? "Pencarian: " . $tutorsData['current_filters']['search'] : "Semua tutor");

            if (!empty($tutorsData['platform_stats'])) {
                $stats = $tutorsData['platform_stats'];
                $tutorsListingContextInfo .= "\n\nSTATISTIK PLATFORM:";
                $tutorsListingContextInfo .= "\n- Total Tutor: " . ($stats['total_tutors'] ?? 0);
                $tutorsListingContextInfo .= "\n- Total Kursus: " . ($stats['total_courses'] ?? 0);
                $tutorsListingContextInfo .= "\n- Total Siswa: " . ($stats['total_students'] ?? 0);
                $tutorsListingContextInfo .= "\n- Rating Rata-rata: " . ($stats['average_rating'] ?? 0);
            }

            if (!empty($tutorsData['featured_tutors'])) {
                $tutorsListingContextInfo .= "\n\nTUTOR UNGGULAN:";
                foreach (array_slice($tutorsData['featured_tutors'], 0, 3) as $tutor) {
                    $tutorsListingContextInfo .= "\n- " . ($tutor['name'] ?? 'Tutor') . " (" . ($tutor['education_level'] ?? 'Unknown') . ")";
                    $tutorsListingContextInfo .= " - " . ($tutor['courses_count'] ?? 0) . " kursus, " . ($tutor['students_count'] ?? 0) . " siswa";
                }
            }

            $tutorsListingContextInfo .= "\n\nINSTRUKSI KHUSUS UNTUK HALAMAN TUTOR:";
            $tutorsListingContextInfo .= "\n- Bantu user menemukan tutor yang sesuai dengan kebutuhan belajar";
            $tutorsListingContextInfo .= "\n- Berikan rekomendasi berdasarkan keahlian, rating, dan pengalaman";
            $tutorsListingContextInfo .= "\n- Sertakan CTA seperti 'Lihat profil tutor terbaik kami', 'Ingin jadi tutor? Daftar sekarang!', atau 'Temukan tutor yang cocok untuk skill yang ingin kamu pelajari'";
            $tutorsListingContextInfo .= "\n- Gunakan data statistik platform untuk menunjukkan kredibilitas";
            $tutorsListingContextInfo .= "\n- Fokus pada manfaat belajar dengan tutor berpengalaman";
        }

        // Add user dashboard context for user dashboard pages
        $userDashboardContextInfo = '';
        if ($isAuthenticated && $this->isUserDashboardRoute($route)) {
            $userDashboardContext = app(UserProfileController::class)->buildUserDashboardContext(auth()->user());

            $userDashboardContextInfo = "\n\nKONTEKS DASHBOARD USER:";
            $userDashboardContextInfo .= "\n- Nama: " . $userDashboardContext['user_info']['name'];
            $userDashboardContextInfo .= "\n- Membership: " . ucfirst($userDashboardContext['user_info']['membership_level']);
            $userDashboardContextInfo .= "\n- Bergabung: " . $userDashboardContext['user_info']['joined_date'];
            $userDashboardContextInfo .= "\n- Profil Lengkap: " . $userDashboardContext['user_info']['profile_completion'] . "%";

            // Learning statistics
            $stats = $userDashboardContext['learning_stats'];
            $userDashboardContextInfo .= "\n\nSTATISTIK BELAJAR:";
            $userDashboardContextInfo .= "\n- Kursus Diambil: " . $stats['total_enrolled'];
            $userDashboardContextInfo .= "\n- Kursus Selesai: " . $stats['completed_courses'];
            $userDashboardContextInfo .= "\n- Total Jam Belajar: " . $stats['total_learning_hours'];
            $userDashboardContextInfo .= "\n- Ujian Lulus: " . $stats['passed_exams'] . "/" . $stats['exam_attempts'];

            // Enrolled courses with tutors
            if ($userDashboardContext['enrolled_courses']->count() > 0) {
                $userDashboardContextInfo .= "\n\nKURSUS YANG DIAMBIL:";
                foreach ($userDashboardContext['enrolled_courses']->take(5) as $course) {
                    $tutorInfo = $course['tutor_slug'] ?
                        "instruktur [{$course['tutor_name']}](/tutor/{$course['tutor_slug']})" :
                        "instruktur {$course['tutor_name']}";
                    $userDashboardContextInfo .= "\n- {$course['title']} ({$course['level']}) - {$tutorInfo}";
                }
            }

            // Favorite tutors
            if ($userDashboardContext['favorite_tutors']->count() > 0) {
                $userDashboardContextInfo .= "\n\nTUTOR FAVORIT:";
                foreach ($userDashboardContext['favorite_tutors']->take(3) as $tutor) {
                    $tutorLink = $tutor['slug'] ?
                        "instruktur [{$tutor['name']}](/tutor/{$tutor['slug']})" :
                        "instruktur {$tutor['name']}";
                    $userDashboardContextInfo .= "\n- {$tutorLink} ({$tutor['job_title']}) - {$tutor['courses_with_user']} kursus";
                }
            }

            $userDashboardContextInfo .= "\n\nINSTRUKSI KHUSUS UNTUK USER DASHBOARD:";
            $userDashboardContextInfo .= "\n- Gunakan data real di atas untuk memberikan respons yang personal";
            $userDashboardContextInfo .= "\n- Ketika menyebutkan tutor, gunakan format natural seperti 'instruktur [Nama](/tutor/slug)' atau 'tutor [Nama](/tutor/slug)'";
            $userDashboardContextInfo .= "\n- Berikan rekomendasi berdasarkan kursus dan progress user yang sebenarnya";
            $userDashboardContextInfo .= "\n- Motivasi user berdasarkan pencapaian mereka";
            $userDashboardContextInfo .= "\n- Jangan gunakan data placeholder atau contoh generik";
        }

        // Special handling for non-authenticated users
        if (!$isAuthenticated) {
            $nonAuthPrompt = "Anda adalah Nala, asisten belajar yang ramah di Ngambiskuy.

PENTING - PENGGUNA BELUM LOGIN:
Pengguna ini belum memiliki akun atau belum login. Berikan respons yang:
- Memperkenalkan platform Ngambiskuy dengan antusias
- Menjelaskan manfaat membuat akun (tracking progress, sertifikat, akses penuh)
- Menyertakan ajakan untuk daftar/login dengan ramah
- Tetap membantu menjawab pertanyaan umum tentang platform

{$routeContext}{$courseContextInfo}{$tutorContextInfo}{$tutorsListingContextInfo}{$homepageContextInfo}

KONTEKS PLATFORM NGAMBISKUY:
Ngambiskuy adalah platform edukasi teknologi terdepan yang menawarkan:
- Kursus Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business Skills (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

MANFAAT MEMBUAT AKUN:
- Akses ke ribuan kursus berkualitas tinggi
- Tracking progress belajar otomatis
- Sertifikat resmi setelah menyelesaikan kursus
- Akses ke komunitas pembelajar
- Rekomendasi pembelajaran yang dipersonalisasi
- Akses ke ujian dan tryout sertifikasi

INSTRUKSI RESPONS:
- Selalu sertakan ajakan untuk bergabung dengan Ngambiskuy
- Gunakan bahasa yang ramah dan tidak memaksa
- Fokus pada manfaat yang akan didapat user
- Berikan informasi yang akurat tentang platform
- Maksimal 120 kata untuk menjaga respons tetap engaging

Contoh CTA yang baik:
'Daftar sekarang untuk mulai perjalanan belajar teknologi Anda!'
'Login untuk akses penuh ke semua fitur pembelajaran!'
'Bergabunglah dengan ribuan pembelajar lainnya di Ngambiskuy!'

Pengguna: Pengunjung (belum login)
Situasi: {$context}
Route: {$route}

Jawab dengan antusias dan ajak bergabung dengan Ngambiskuy!";

            return $nonAuthPrompt;
        }

        // Check if this is a tutor registration question
        $tutorRegistrationContext = $this->getTutorRegistrationContext($request);

        // Standard prompt for authenticated users
        $basePrompt = "Anda adalah Nala, asisten belajar yang ramah di Ngambiskuy.

Karakteristik:
- Ramah, natural, dan supportif
- Berbicara seperti teman yang membantu belajar
- Jawaban praktis dan mudah dipahami
- Maksimal 100 kata untuk respons singkat

{$routeContext}{$courseContextInfo}{$tutorContextInfo}{$tutorsListingContextInfo}{$userDashboardContextInfo}{$tutorRegistrationContext}{$homepageContextInfo}

KONTEKS PLATFORM NGAMBISKUY:
Ngambiskuy adalah platform edukasi teknologi yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

SANGAT PENTING - LARANGAN MUTLAK:
- JANGAN PERNAH sebutkan 'AI Assistant Ngambiskuy'
- JANGAN PERNAH sebutkan 'rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran'
- JANGAN PERNAH sebutkan sistem teknis (ICE, database, analisis profil, dll)
- JANGAN gunakan istilah teknis yang membingungkan
- JANGAN bicara formal atau kaku
- Bicara natural seperti teman biasa
- JANGAN mengklaim kemitraan palsu atau data yang tidak akurat
- JANGAN PERNAH merekomendasikan kursus di luar kategori teknologi (seperti masak, olahraga, musik, dll)
- HANYA rekomendasikan kursus yang sesuai dengan kategori platform teknologi

LARANGAN KHUSUS ANTI-HALUSINASI:
- JANGAN PERNAH buat atau sebutkan nama tutor yang tidak ada dalam data konteks
- JANGAN PERNAH gunakan contoh nama seperti 'Fauzan Nur Aziz', 'Rizki Nurfauzi', 'Kak Sandhika Galih' atau nama tutor lainnya yang tidak ada dalam data
- HANYA gunakan nama tutor yang benar-benar ada dalam data konteks yang diberikan
- JIKA TIDAK ADA DATA TUTOR SPESIFIK, gunakan respons umum tanpa menyebutkan nama
- JANGAN PERNAH buat informasi palsu tentang status publikasi kursus atau ujian
- HANYA gunakan informasi yang benar-benar tersedia dalam data konteks
- JIKA TIDAK ADA DATA SPESIFIK, berikan respons umum dengan CTA yang sesuai
- VALIDASI: Sebelum menyebutkan nama tutor, pastikan nama tersebut ada dalam konteks data yang diberikan

INSTRUKSI LINK TUTOR:
- Ketika menyebutkan nama tutor, selalu buat menjadi link yang dapat diklik
- Format: [Nama Tutor](/tutor/slug-tutor)
- Gunakan slug yang sesuai dengan nama tutor (lowercase, spasi jadi dash)

INSTRUKSI KHUSUS USER DASHBOARD:
- Gunakan data real user (statistik, kursus, tutor favorit) untuk respons yang personal
- Berikan motivasi berdasarkan pencapaian aktual user
- Rekomendasikan kursus berdasarkan tutor yang sudah dikenal user
- Sebutkan progress spesifik (jam belajar, kursus selesai, ujian lulus)
- Jangan gunakan contoh generik atau placeholder data
- Fokus pada actionable insights berdasarkan learning pattern user

Untuk greeting sederhana: jawab singkat dan ramah (maksimal 20 kata)
Contoh: 'Halo! Ada yang bisa saya bantu dengan belajar hari ini?'

Pengguna: {$name}
Situasi: {$context}
Route: {$route}

Jawab dengan natural dan ramah seperti teman.";

        return $basePrompt;
    }

    /**
     * Check if route is a user dashboard route
     */
    private function isUserDashboardRoute($route)
    {
        $userDashboardRoutes = [
            'user.dashboard',
            'user.profile',
            'user.courses',
            'user.exams',
            'user.blog',
            // 'user.progress', // Commented out due to functionality overlap
            'user.certificates',
            'user.settings'
        ];

        return in_array($route, $userDashboardRoutes);
    }

    /**
     * Get route-specific context for system prompt
     */
    private function getRouteSpecificContext($route, $context)
    {
        $routeContexts = [
            // Public routes (matching actual Laravel route names)
            'home' => 'KONTEKS: Halaman utama Ngambiskuy. Bantu user memahami platform dan mulai belajar.',
            'courses.index' => 'KONTEKS: Halaman daftar kursus. Bantu user menemukan kursus yang sesuai minat.',
            'exams.index' => 'KONTEKS: Halaman daftar ujian/tryout. Bantu user memilih ujian untuk sertifikasi.',
            'exams.show' => 'KONTEKS: Detail ujian. Jelaskan tentang ujian ini dan cara persiapannya.',
            'exams.take' => 'KONTEKS: User sedang mengerjakan ujian. JANGAN berikan jawaban ujian!',
            'exams.result' => 'KONTEKS: Hasil ujian. Berikan motivasi dan saran untuk improvement.',
            'blog.index' => 'KONTEKS: Halaman blog. Bantu user temukan artikel pembelajaran.',
            'blog.show' => 'KONTEKS: Detail artikel blog. Diskusi konten artikel dan topik terkait.',
            'blog.category' => 'KONTEKS: Kategori blog. Bantu user eksplorasi artikel dalam kategori ini.',
            'course.show' => 'KONTEKS: Detail kursus. Jelaskan manfaat dan isi kursus ini.',
            'course.learn' => 'KONTEKS: User sedang belajar. Bantu jelaskan materi yang sulit.',
            'tutors.index' => 'KONTEKS: Halaman daftar tutor. Bantu user menemukan tutor terbaik yang sesuai dengan kebutuhan belajar mereka. Berikan rekomendasi tutor berdasarkan keahlian, rating, dan pengalaman. Sertakan CTA seperti "Lihat profil tutor terbaik kami", "Ingin jadi tutor? Daftar sekarang!", atau "Temukan tutor yang cocok untuk skill yang ingin kamu pelajari".',
            'tutor.public-profile' => 'KONTEKS: Profil tutor. Jelaskan keahlian dan pengalaman tutor.',
            'payment.pricing' => 'KONTEKS: Halaman pricing. Jelaskan manfaat setiap paket membership.',

            // User Dashboard routes - Enhanced with data-driven context
            'user.dashboard' => 'KONTEKS: Dashboard user. Gunakan data statistik belajar real user untuk memberikan insight personal tentang progress, motivasi untuk melanjutkan kursus yang belum selesai, dan rekomendasi berdasarkan tutor favorit mereka.',
            'user.profile' => 'KONTEKS: Profil user. Bantu user melengkapi profil berdasarkan data yang sudah ada, berikan tips untuk meningkatkan profile completion percentage, dan saran untuk mengoptimalkan profil karir.',
            'user.courses' => 'KONTEKS: Kursus user. Gunakan data kursus yang sudah diambil user untuk memberikan rekomendasi lanjutan, motivasi untuk menyelesaikan kursus yang in-progress, dan saran kursus dari tutor favorit mereka.',
            'user.exams' => 'KONTEKS: Ujian user. Analisis performa ujian user berdasarkan data real, berikan tips improvement untuk ujian yang belum lulus, dan motivasi berdasarkan pencapaian mereka.',
            'user.blog' => 'KONTEKS: Blog user. Bantu user manage artikel yang disimpan dan berikan rekomendasi artikel berdasarkan minat belajar mereka.',
            // 'user.progress' => 'KONTEKS: Progress belajar. Gunakan data real learning hours, completed courses, dan recent activity untuk memberikan motivasi personal dan target pembelajaran yang realistis.', // Commented out due to functionality overlap
            'user.certificates' => 'KONTEKS: Sertifikat user. Bantu user dengan sertifikat yang sudah diperoleh berdasarkan ujian yang lulus, tips untuk share di LinkedIn, dan motivasi untuk mendapatkan sertifikat lainnya.',
            'user.settings' => 'KONTEKS: Pengaturan user. Bantu user konfigurasi akun dan berikan tips untuk mengoptimalkan pengalaman belajar berdasarkan preferensi mereka.',
            'user.membership' => 'KONTEKS: Membership user. Jelaskan benefit upgrade membership berdasarkan usage pattern dan kebutuhan belajar user yang sebenarnya.',

            // Tutor Dashboard routes (matching actual web.php routes)
            'tutor.dashboard' => 'KONTEKS: Dashboard tutor. Bantu tutor manage konten dan siswa.',
            'tutor.courses' => 'KONTEKS: Kursus tutor. Bantu tutor manage kursus yang dibuat.',
            'tutor.create-course' => 'KONTEKS: Buat kursus baru. Bantu tutor dengan tips membuat kursus berkualitas.',
            'tutor.edit-course' => 'KONTEKS: Edit kursus. Bantu tutor improve kursus yang ada.',
            'tutor.curriculum.index' => 'KONTEKS: Kurikulum kursus. Bantu tutor struktur pembelajaran.',
            'tutor.curriculum.create-material' => 'KONTEKS: Buat materi. Tips membuat konten engaging.',
            'tutor.curriculum.edit-material' => 'KONTEKS: Edit materi. Bantu improve konten existing.',
            'tutor.exams' => 'KONTEKS: Ujian tutor. Bantu tutor manage ujian yang dibuat.',
            'tutor.exams.create' => 'KONTEKS: Buat ujian. Tips membuat soal berkualitas.',
            'tutor.exams.edit' => 'KONTEKS: Edit ujian. Bantu improve soal dan penilaian.',
            'tutor.exams.show' => 'KONTEKS: Detail ujian tutor. Bantu tutor analisis performa ujian.',
            'tutor.blogs' => 'KONTEKS: Blog tutor. Bantu tutor manage artikel yang dibuat.',
            'tutor.blogs.create' => 'KONTEKS: Buat artikel blog. Tips menulis konten menarik.',
            'tutor.blogs.edit' => 'KONTEKS: Edit artikel blog. Bantu improve konten artikel.',
            'tutor.blogs.show' => 'KONTEKS: Detail artikel tutor. Bantu analisis performa artikel.',
            'tutor.students' => 'KONTEKS: Siswa tutor. Bantu tutor engage dengan siswa.',
            'tutor.analytics' => 'KONTEKS: Analytics tutor. Bantu tutor pahami performa konten.',
            'tutor.earnings' => 'KONTEKS: Penghasilan tutor. Bantu tutor optimasi income.',
            'tutor.profile' => 'KONTEKS: Profil tutor. Bantu tutor buat profil menarik.',
            'tutor.settings' => 'KONTEKS: Pengaturan tutor. Bantu tutor konfigurasi akun.',

            // Tutor Registration routes
            'tutor.register.terms' => 'KONTEKS: Syarat jadi tutor. Jelaskan proses dan requirement.',
            'tutor.register.profile' => 'KONTEKS: Daftar tutor. Bantu lengkapi profil tutor.',
            'tutor.register.review' => 'KONTEKS: Review aplikasi tutor. Jelaskan proses review.',
            'tutor.register.status' => 'KONTEKS: Status aplikasi tutor. Update status dan next steps.',

            // Tutor Public Profile
            'tutor.public-profile' => 'KONTEKS: Profil publik tutor. Bantu user dengan informasi tentang tutor ini.',

            // Payment routes
            'payment.membership.checkout' => 'KONTEKS: Checkout membership. Bantu user pilih paket yang tepat.',
            'payment.course.checkout' => 'KONTEKS: Checkout kursus. Konfirmasi pilihan dan benefit kursus.'
        ];

        return $routeContexts[$route] ?? 'KONTEKS: Halaman umum Ngambiskuy. Bantu user dengan pertanyaan pembelajaran.';
    }

    /**
     * Build simplified system prompt (fallback)
     */
    private function buildSimpleSystemPrompt($context, $userProfile)
    {
        $name = $userProfile['basic_info']['name'] ?? 'Pengguna';

        $prompt = "Anda adalah Nala, asisten belajar yang ramah di Ngambiskuy.

Karakteristik:
- Ramah, natural, dan supportif
- Berbicara seperti teman yang membantu belajar
- Jawaban praktis dan mudah dipahami
- Maksimal 100 kata untuk respons singkat

KONTEKS PLATFORM NGAMBISKUY:
Ngambiskuy adalah platform edukasi teknologi yang fokus pada:
- Programming (Python, JavaScript, PHP, Java, React, Vue, Laravel)
- Web Development (Frontend, Backend, Full Stack)
- Mobile Development (Android, iOS, React Native, Flutter)
- Data Science (Machine Learning, AI, Data Analysis)
- UI/UX Design (Design Thinking, Prototyping, User Research)
- Digital Marketing (SEO, Social Media, Content Marketing)
- Business (Entrepreneurship, Project Management, Leadership)
- Cybersecurity (Network Security, Ethical Hacking)

SANGAT PENTING - LARANGAN MUTLAK:
- JANGAN PERNAH sebutkan 'AI Assistant Ngambiskuy'
- JANGAN PERNAH sebutkan 'rekomendasi kursus, analisis jalur karir, dan panduan pembelajaran'
- JANGAN PERNAH sebutkan sistem teknis (ICE, database, analisis profil, dll)
- JANGAN gunakan istilah teknis yang membingungkan
- JANGAN bicara formal atau kaku
- Bicara natural seperti teman biasa
- JANGAN PERNAH merekomendasikan kursus di luar kategori teknologi (seperti masak, olahraga, musik, dll)
- HANYA rekomendasikan kursus yang sesuai dengan kategori platform teknologi

Untuk greeting sederhana: jawab singkat dan ramah (maksimal 20 kata)
Contoh: 'Halo! Ada yang bisa saya bantu dengan belajar hari ini?'

Pengguna: {$name}
Situasi: {$context}

Jawab dengan natural dan ramah seperti teman.";

        return $prompt;
    }

    /**
     * Build contextual prompt
     */
    private function buildContextualPrompt($message, $context, $history, $hasPreviousContext = false, $messageCount = 0)
    {
        // Handle basic greetings differently
        if ($this->isBasicGreeting(strtolower($message))) {
            if ($hasPreviousContext && $messageCount > 0) {
                return "Pesan: {$message}\n\nIni adalah sapaan dalam percakapan yang sudah berlangsung ({$messageCount} pesan sebelumnya). Jawab dengan ramah dan natural, akui bahwa kalian sudah pernah berbicara sebelumnya.";
            }
            return "Pesan: {$message}\n\nIni adalah sapaan sederhana. Jawab dengan ramah dan singkat (maksimal 30 kata). Jangan berlebihan atau terlalu panjang.";
        }

        $prompt = "Situasi: {$context}\n\n";

        // Enhanced conversation history handling
        if (!empty($history) && count($history) > 0) {
            $prompt .= "Percakapan sebelumnya:\n";
            $historyToShow = $hasPreviousContext ? array_slice($history, -6) : array_slice($history, -3);

            foreach ($historyToShow as $msg) {
                $sender = $msg['sender'] === 'user' ? 'User' : 'Nala';
                $content = strlen($msg['content']) > 100 ? substr($msg['content'], 0, 100) . '...' : $msg['content'];
                $prompt .= "{$sender}: {$content}\n";
            }
            $prompt .= "\n";

            // Add context awareness note
            if ($hasPreviousContext) {
                $prompt .= "PENTING: Ini adalah kelanjutan percakapan yang sudah berlangsung. Gunakan konteks dari pesan sebelumnya untuk memberikan jawaban yang relevan dan berkesinambungan.\n\n";
            }
        }

        $prompt .= "Pesan: {$message}\n\n";
        $prompt .= "Jawab dengan natural dan helpful. Maksimal 100 kata.";

        return $prompt;
    }

    /**
     * Check if message is a basic greeting
     */
    private function isBasicGreeting($message)
    {
        $greetings = [
            'halo', 'hai', 'hello', 'hi', 'selamat pagi', 'selamat siang', 'selamat sore', 'selamat malam',
            'good morning', 'good afternoon', 'good evening', 'good night',
            'apa kabar', 'how are you', 'bagaimana kabar', 'apa saja', 'apa aja',
            'terima kasih', 'thank you', 'thanks', 'makasih', 'ok', 'oke', 'baik',
            'baru saja', 'test', 'testing', 'coba', 'tes'
        ];

        $message = trim($message);

        // Check if message is very short (likely greeting)
        if (strlen($message) <= 15) {
            foreach ($greetings as $greeting) {
                if (strpos($message, $greeting) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if message is specifically asking about career (not just mentioning it)
     */
    private function isSpecificCareerQuestion($message)
    {
        $specificCareerQuestions = [
            'analisis karir', 'jalur karir', 'career path', 'roadmap karir',
            'gaji berapa', 'salary range', 'penghasilan', 'prospek karir',
            'skill apa yang dibutuhkan', 'kemampuan apa', 'skill gap',
            'industri apa', 'tren industri', 'peluang kerja'
        ];

        foreach ($specificCareerQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message is specifically asking about courses
     */
    private function isSpecificCourseQuestion($message, $context)
    {
        $specificCourseQuestions = [
            'rekomendasi kursus', 'kursus apa', 'course recommendation',
            'belajar apa', 'materi apa', 'pembelajaran apa',
            'jalur belajar', 'learning path', 'roadmap belajar'
        ];

        // Strong course contexts
        $strongCourseContexts = ['course_detail', 'course_listing', 'learning_page'];

        foreach ($specificCourseQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return in_array($context, $strongCourseContexts);
    }

    /**
     * Check if message is specifically asking about exams
     */
    private function isSpecificExamQuestion($message, $context)
    {
        $specificExamQuestions = [
            'ujian apa', 'exam apa', 'sertifikat apa', 'certification',
            'persiapan ujian', 'tips ujian', 'cara lulus',
            'sertifikasi apa', 'certificate'
        ];

        // Strong exam contexts
        $strongExamContexts = ['exam_detail', 'exam_listing', 'exam_taking'];

        foreach ($specificExamQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        return in_array($context, $strongExamContexts);
    }

    /**
     * Check if message is specifically asking about tutors
     */
    private function isSpecificTutorQuestion($message, $context)
    {
        // General tutor registration questions that should NOT be routed to TutorController
        $tutorRegistrationQuestions = [
            'cara jadi tutor', 'cara menjadi tutor', 'daftar tutor', 'daftar jadi tutor',
            'jadi pengajar', 'menjadi pengajar', 'bergabung tutor', 'apply tutor',
            'syarat tutor', 'persyaratan tutor', 'mendaftar tutor', 'registrasi tutor',
            'cara daftar tutor', 'bagaimana jadi tutor', 'proses jadi tutor'
        ];

        // Check if this is a general tutor registration question
        foreach ($tutorRegistrationQuestions as $regQuestion) {
            if (strpos($message, $regQuestion) !== false) {
                return false; // Don't route to TutorController, handle with general response
            }
        }

        // Specific tutor profile questions that should be routed to TutorController
        $specificTutorQuestions = [
            'tentang tutor', 'profil tutor', 'siapa tutor', 'tutor ini',
            'pengalaman tutor', 'keahlian tutor', 'background tutor',
            'kursus tutor', 'tutor mengajar', 'cara mengajar',
            'rating tutor', 'review tutor', 'testimoni tutor'
        ];

        // Strong tutor contexts where specific tutor questions should be routed
        $strongTutorContexts = ['tutor_public_profile', 'tutors_listing'];

        // Only route to TutorController if asking specific questions about tutors
        foreach ($specificTutorQuestions as $question) {
            if (strpos($message, $question) !== false) {
                return true;
            }
        }

        // For tutors_listing context, only route if asking specific tutor questions
        // For tutor_public_profile context, only route if asking about the specific tutor
        if ($context === 'tutor_public_profile') {
            // Only route if asking about the specific tutor, not general registration
            return false; // Let general response handle tutor registration questions
        }

        return false; // Default to general response
    }

    /**
     * Get tutor registration context for system prompt
     */
    private function getTutorRegistrationContext($request)
    {
        if (!$request) {
            return '';
        }

        $message = strtolower($request->input('message', ''));

        // Check if this is a tutor registration question
        $tutorRegistrationQuestions = [
            'cara jadi tutor', 'cara menjadi tutor', 'daftar tutor', 'daftar jadi tutor',
            'jadi pengajar', 'menjadi pengajar', 'bergabung tutor', 'apply tutor',
            'syarat tutor', 'persyaratan tutor', 'mendaftar tutor', 'registrasi tutor',
            'cara daftar tutor', 'bagaimana jadi tutor', 'proses jadi tutor'
        ];

        foreach ($tutorRegistrationQuestions as $regQuestion) {
            if (strpos($message, $regQuestion) !== false) {
                return "\n\nKONTEKS KHUSUS - PERTANYAAN REGISTRASI TUTOR:
User bertanya tentang cara menjadi tutor di Ngambiskuy. Berikan informasi tentang:
- Proses pendaftaran tutor yang mudah dan cepat
- Syarat minimal: keahlian di bidang teknologi, pengalaman mengajar/berbagi ilmu
- Keuntungan menjadi tutor: penghasilan tambahan, berbagi ilmu, membangun personal brand
- Langkah-langkah: daftar akun → lengkapi profil tutor → buat kursus pertama
- CTA: 'Yuk, [daftar jadi tutor sekarang](/tutor/register)!' (gunakan format markdown link)

PENTING:
- Jangan berikan informasi tentang tutor spesifik, fokus pada proses registrasi umum
- WAJIB gunakan format markdown link [text](url) untuk semua link
- Contoh: [daftar di sini](/tutor/register) atau [mulai sekarang](/tutor/register)";
            }
        }

        return '';
    }

    /**
     * Check for prohibited content
     */
    private function checkProhibitedContent($message)
    {
        $lowerMessage = strtolower($message);
        
        $prohibitions = [
            'bad_language' => [
                'keywords' => ['anjing', 'babi', 'bangsat', 'fuck', 'shit', 'bodoh', 'tolol', 'goblok'],
                'response' => 'Maaf, mari kita jaga percakapan tetap sopan dan fokus pada pembelajaran! Bagaimana kalau kita bahas topik yang berkaitan dengan kursus atau karir Anda?'
            ],
            'religion' => [
                'keywords' => ['agama', 'islam', 'kristen', 'hindu', 'buddha', 'doa', 'tuhan', 'allah'],
                'response' => 'Saya fokus pada topik edukasi dan tidak membahas masalah agama. Mari kita jelajahi skill yang ingin Anda pelajari atau jalur karir yang Anda minati!'
            ],
            'politics' => [
                'keywords' => ['politik', 'partai', 'pemilu', 'jokowi', 'prabowo', 'pemerintah', 'presiden'],
                'response' => 'Saya tidak terlibat dalam diskusi politik. Mari kita bicarakan sesuatu yang lebih selaras dengan perjalanan belajar Anda!'
            ],
            'competitors' => [
                'keywords' => ['ruangguru', 'zenius', 'udemy', 'coursera', 'skillshare'],
                'response' => 'Mari kita fokus pada apa yang bisa Ngambiskuy tawarkan untuk membantu Anda berkembang. Apa yang ingin Anda pelajari?'
            ]
        ];

        foreach ($prohibitions as $data) {
            foreach ($data['keywords'] as $keyword) {
                if (strpos($lowerMessage, $keyword) !== false) {
                    return $data['response'];
                }
            }
        }

        return null;
    }

    /**
     * Get fallback response when AI fails
     */
    private function getFallbackResponse($context)
    {
        $responses = [
            'course_detail' => 'Saya bisa bantu jelaskan tentang kursus ini. Ada yang ingin ditanyakan?',
            'learning_page' => 'Saya siap bantu kalau ada materi yang kurang jelas!',
            'exam_detail' => 'Butuh tips untuk ujian ini? Tanya aja!',
            'homepage' => 'Halo! Selamat datang di Ngambiskuy! 🎓 Platform pembelajaran teknologi terdepan dengan ribuan kursus berkualitas. Mau mulai belajar skill baru? Jelajahi kursus kami atau daftar sekarang untuk akses penuh!',
            'home' => 'Halo! Selamat datang di Ngambiskuy! 🎓 Platform pembelajaran teknologi terdepan dengan ribuan kursus berkualitas. Mau mulai belajar skill baru? Jelajahi kursus kami atau daftar sekarang untuk akses penuh!',
            'default' => 'Halo! Saya Nala, siap bantu dengan pertanyaan belajar Anda. Ada yang bisa dibantu?'
        ];

        return $responses[$context] ?? $responses['default'];
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.7,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 512, // Reduced for faster responses
            ]
        ];

        $response = Http::timeout(15)->post($url, $payload); // Reduced timeout

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Get or create conversation
     */
    private function getOrCreateConversation($conversationId, $route, $context)
    {
        if ($conversationId) {
            $conversation = NalaChatConversation::find($conversationId);
            if ($conversation && (!auth()->check() || $conversation->user_id === auth()->id())) {
                return $conversation;
            }
        }

        return NalaChatConversation::create([
            'id' => Str::uuid(),
            'user_id' => auth()->id(),
            'started_route' => $route,
            'started_context' => $context
        ]);
    }

    /**
     * Store message in database
     */
    private function storeMessage($conversation, $sender, $content, $metadata = [])
    {
        $message = NalaChatMessage::create([
            'conversation_id' => $conversation->id,
            'sender' => $sender,
            'content' => $content,
            'metadata' => $metadata
        ]);

        // Update conversation stats
        $conversation->updateStats();

        // Maintain message limit (keep only recent 15 exchanges = 30 messages)
        $conversation->maintainMessageLimit(30);

        // Clean up old conversations for the user (keep only 5 recent conversations)
        if (auth()->check()) {
            NalaChatConversation::cleanupOldConversations(auth()->id(), 5);
        }

        return $message;
    }

    /**
     * Get conversation history
     */
    private function getConversationHistory($conversation, $limit = 5)
    {
        return $conversation->messages()
            ->where('status', '!=', 'deleted')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get(['sender', 'content'])
            ->reverse()
            ->toArray();
    }

    /**
     * Get conversation history for context (enhanced version)
     */
    private function getConversationHistoryForContext($conversation, $maxExchanges = 5)
    {
        $messages = $conversation->messages()
            ->where('status', '!=', 'deleted')
            ->orderBy('created_at', 'desc')
            ->limit($maxExchanges * 2) // Each exchange = user + ai message
            ->get(['sender', 'content', 'created_at'])
            ->reverse()
            ->values();

        return $messages->toArray();
    }

    /**
     * Get user membership level
     */
    private function getUserMembershipLevel($user)
    {
        if (!$user || !$user->current_membership_id) {
            return 'free';
        }

        // Get membership plan slug from the relationship
        $membershipPlan = \App\Models\MembershipPlan::find($user->current_membership_id);
        return $membershipPlan ? $membershipPlan->slug : 'free';
    }

    /**
     * Check if user can send message based on limits
     */
    private function canUserSendMessage($membershipLevel)
    {
        if (!auth()->check()) {
            return true; // Allow guest users
        }

        $limits = [
            'free' => 10,
            'basic' => 100,
            'standard' => 300,
            'pro' => 400
        ];

        $limit = $limits[$membershipLevel] ?? 10;
        $today = now()->format('Y-m-d');

        $todayCount = DB::table('nala_chat_messages')
            ->join('nala_chat_conversations', 'nala_chat_messages.conversation_id', '=', 'nala_chat_conversations.id')
            ->where('nala_chat_conversations.user_id', auth()->id())
            ->where('nala_chat_messages.sender', 'user')
            ->whereDate('nala_chat_messages.created_at', $today)
            ->count();

        return $todayCount < $limit;
    }

    /**
     * Get membership limit response
     */
    private function getMembershipLimitResponse($membershipLevel)
    {
        $responses = [
            'free' => 'Anda telah mencapai batas 10 pesan harian untuk akun gratis. Upgrade ke membership Basic untuk mendapatkan 100 pesan per hari!',
            'basic' => 'Anda telah mencapai batas 100 pesan harian. Upgrade ke Standard untuk mendapatkan 300 pesan per hari!',
            'standard' => 'Anda telah mencapai batas 300 pesan harian. Upgrade ke Pro untuk mendapatkan 400 pesan per hari!',
            'pro' => 'Anda telah mencapai batas 400 pesan harian. Silakan coba lagi besok!'
        ];

        return $responses[$membershipLevel] ?? $responses['free'];
    }

    /**
     * Increment user message count
     */
    private function incrementMessageCount($userId)
    {
        // This could be implemented with a daily counter table if needed
        // For now, we rely on the message count from the database
    }

    /**
     * Get homepage context info with real platform data
     */
    private function getHomepageContextInfo()
    {
        try {
            // Get real platform statistics
            $totalCourses = \App\Models\Course::published()->count();
            $totalTutors = \App\Models\TutorProfile::where('status', 'approved')->count();
            $totalStudents = \App\Models\User::count();
            $totalExams = \App\Models\Exam::where('is_published', true)->count();

            // Get featured tutors (real data)
            $featuredTutors = \App\Models\TutorProfile::with(['user'])
                ->where('status', 'approved')
                ->whereHas('user.courses', function($query) {
                    $query->published();
                })
                ->limit(3)
                ->get()
                ->map(function($tutor) {
                    return [
                        'name' => $tutor->public_name ?? $tutor->user->name,
                        'slug' => $tutor->public_name_slug,
                        'job_title' => $tutor->job_title,
                        'education_level' => $tutor->education_level,
                        'courses_count' => $tutor->user->courses()->published()->count()
                    ];
                });

            // Get popular course categories
            $popularCategories = \App\Models\Category::withCount(['courses' => function($query) {
                $query->published();
            }])
                ->having('courses_count', '>', 0)
                ->orderBy('courses_count', 'desc')
                ->limit(5)
                ->get()
                ->pluck('name')
                ->toArray();

            $contextInfo = "\n\nKONTEKS HOMEPAGE NGAMBISKUY (DATA REAL):";
            $contextInfo .= "\n- Total Kursus: " . $totalCourses;
            $contextInfo .= "\n- Total Tutor: " . $totalTutors;
            $contextInfo .= "\n- Total Siswa: " . $totalStudents;
            $contextInfo .= "\n- Total Ujian: " . $totalExams;

            if ($featuredTutors->count() > 0) {
                $contextInfo .= "\n\nTUTOR UNGGULAN (REAL DATA):";
                foreach ($featuredTutors as $tutor) {
                    $tutorLink = $tutor['slug'] ?
                        "[{$tutor['name']}](/tutor/{$tutor['slug']})" :
                        $tutor['name'];
                    $contextInfo .= "\n- {$tutorLink} ({$tutor['job_title']}) - {$tutor['courses_count']} kursus";
                }
            }

            if (!empty($popularCategories)) {
                $contextInfo .= "\n\nKATEGORI POPULER:";
                $contextInfo .= "\n- " . implode(', ', $popularCategories);
            }

            $contextInfo .= "\n\nINSTRUKSI KHUSUS HOMEPAGE:";
            $contextInfo .= "\n- HANYA gunakan data tutor yang tercantum di atas";
            $contextInfo .= "\n- JANGAN PERNAH buat nama tutor palsu seperti 'Fauzan Nur Aziz' atau 'Rizki Nurfauzi'";
            $contextInfo .= "\n- Jika tidak ada tutor spesifik yang relevan, berikan respons umum tentang platform";
            $contextInfo .= "\n- Fokus pada statistik platform yang real dan kategori yang tersedia";
            $contextInfo .= "\n- Berikan CTA seperti 'Jelajahi kursus kami', 'Daftar sekarang', atau 'Temukan tutor terbaik'";
            $contextInfo .= "\n- Untuk pertanyaan tentang belajar, arahkan ke kategori yang tersedia tanpa menyebutkan tutor spesifik";

            return $contextInfo;

        } catch (\Exception $e) {
            \Log::error('Error fetching homepage context: ' . $e->getMessage());

            // Fallback context without specific data
            return "\n\nKONTEKS HOMEPAGE NGAMBISKUY:
- Platform pembelajaran teknologi terdepan
- Ribuan kursus berkualitas tinggi
- Tutor berpengalaman dan tersertifikasi
- Sertifikat resmi setelah menyelesaikan kursus

INSTRUKSI KHUSUS HOMEPAGE:
- JANGAN menyebutkan nama tutor spesifik
- Berikan respons umum tentang platform dan manfaatnya
- Fokus pada ajakan untuk mendaftar dan menjelajahi kursus
- Gunakan CTA seperti 'Daftar sekarang untuk mulai belajar!'";
        }
    }
}
