@extends('layouts.app')

@section('title', $course->title . ' - Ngambiskuy')

@section('content')
<div class="bg-gray-50 min-h-screen">
    <!-- Course Header -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Course Info -->
                <div class="lg:col-span-2">
                    <!-- Breadcrumb -->
                    <nav class="mb-6">
                        <ol class="flex items-center space-x-2 text-sm opacity-80">
                            <li><a href="{{ route('home') }}" class="hover:text-white">Beranda</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="{{ route('courses.index') }}" class="hover:text-white">Kurikulum</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="{{ route('courses.index', ['category' => $course->category->slug]) }}" class="hover:text-white">{{ $course->category->name }}</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li class="text-white">{{ $course->title }}</li>
                        </ol>
                    </nav>

                    <h1 class="text-3xl md:text-4xl font-bold mb-4">{{ $course->title }}</h1>
                    <p class="text-lg mb-6 opacity-90" data-course-description>{{ $course->description }}</p>

                    <!-- Course Meta -->
                    <div class="flex flex-wrap items-center gap-6 text-sm">
                        @if($course->average_rating > 0)
                            <div class="flex items-center">
                                <span class="text-yellow-300 font-semibold mr-1">{{ number_format($course->average_rating, 1) }}</span>
                                <div class="flex items-center mr-2">
                                    @for($i = 1; $i <= 5; $i++)
                                        <svg class="w-4 h-4 {{ $i <= $course->average_rating ? 'text-yellow-300' : 'text-gray-400' }}" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    @endfor
                                </div>
                                <span class="text-yellow-300 underline">({{ number_format($course->total_reviews) }} rating)</span>
                            </div>
                        @endif
                        <div class="flex items-center">
                            <span>{{ number_format($course->total_students) }} siswa</span>
                        </div>
                    </div>

                    <div class="flex flex-wrap items-center gap-6 text-sm mt-3">
                        <div class="flex items-center">
                            <span>Dibuat oleh</span>
                            <a href="#" class="ml-1 text-yellow-300 underline hover:text-yellow-200">{{ $course->tutor->name }}</a>
                        </div>
                    </div>

                    <div class="flex flex-wrap items-center gap-6 text-sm mt-3 opacity-80">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>Terakhir diperbarui {{ $course->updated_at->format('m/Y') }}</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                            </svg>
                            <span>Bahasa Indonesia</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden sticky top-6">
                        <!-- Course Preview -->
                        <div class="relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-primary to-secondary flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                            @endif
                            <!-- Preview Button Overlay -->
                            <div class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer">
                                <div class="bg-white rounded-full p-3">
                                    <svg class="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div class="p-6">
                            <!-- Price Section -->
                            <div class="mb-6">
                                @if($course->price == 0)
                                    <div class="text-3xl font-bold text-green-600" data-course-price>GRATIS</div>
                                @else
                                    <div class="text-3xl font-bold text-gray-900" data-course-price>{{ $course->formatted_price }}</div>
                                @endif
                                <div class="flex items-center mt-2 text-sm text-gray-600">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>Akses selamanya</span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3 mb-6">
                                @auth
                                    @php
                                        $isEnrolled = auth()->user()->enrollments()
                                            ->where('course_id', $course->id)
                                            ->where('status', 'active')
                                            ->exists();
                                        $isTutor = $course->tutor_id === auth()->id();
                                    @endphp

                                    @if($isTutor)
                                        <a href="{{ route('tutor.curriculum.index', $course) }}" class="w-full btn btn-primary text-center block">
                                            Kelola Kurikulum
                                        </a>
                                    @elseif($isEnrolled)
                                        <a href="{{ route('course.learn', $course) }}" class="w-full btn btn-primary text-center block">
                                            Lanjutkan Belajar
                                        </a>
                                    @else
                                        <form action="{{ route('course.enroll', $course) }}" method="POST" class="w-full">
                                            @csrf
                                            <button type="submit" class="w-full btn btn-primary">
                                                @if($course->price == 0)
                                                    Mulai Belajar Gratis
                                                @else
                                                    Daftar Sekarang
                                                @endif
                                            </button>
                                        </form>
                                    @endif
                                @else
                                    <a href="{{ route('login') }}" class="w-full btn btn-primary text-center block">
                                        Masuk untuk Mendaftar
                                    </a>
                                @endauth

                                <button class="w-full border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    Simpan ke Wishlist
                                </button>
                            </div>

                            <!-- Course Features -->
                            <div class="border-t border-gray-200 pt-6 mb-6">
                                <h4 class="font-semibold text-gray-900 mb-4">Kursus ini termasuk:</h4>
                                <ul class="space-y-3 text-sm">
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>{{ floor($totalDuration / 60) }} jam video on-demand</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span>{{ $totalLessons }} sumber daya yang dapat diunduh</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                        <span>Akses di mobile dan TV</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>Akses penuh seumur hidup</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                        </svg>
                                        <span>Sertifikat penyelesaian</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                        <span>Akses offline dengan aplikasi mobile</span>
                                    </li>
                                    <li class="flex items-center text-gray-700">
                                        <svg class="w-4 h-4 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                                        </svg>
                                        <span>Forum diskusi dan Q&A</span>
                                    </li>
                                </ul>
                            </div>

                            <!-- Share Section -->
                            <div class="border-t border-gray-200 pt-6">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-900">Bagikan kursus ini</span>
                                    <div class="flex space-x-2">
                                        <button class="p-2 text-gray-400 hover:text-gray-600">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        <button class="p-2 text-gray-400 hover:text-gray-600">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid lg:grid-cols-4 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- 1. Tentang Kursus Ini (About This Course) -->
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang Kursus Ini</h2>
                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <p class="text-lg mb-6">{{ $course->long_description ?: $course->description }}</p>

                        <!-- Course Highlights -->
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Mengapa Memilih Kursus Ini?</h3>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">Pembelajaran Praktis</h4>
                                        <p class="text-sm text-gray-600">Belajar dengan contoh nyata dan project hands-on</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">Fleksibel</h4>
                                        <p class="text-sm text-gray-600">Belajar kapan saja, di mana saja sesuai jadwal Anda</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">Sertifikat Resmi</h4>
                                        <p class="text-sm text-gray-600">Dapatkan sertifikat yang diakui industri</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">Dukungan Komunitas</h4>
                                        <p class="text-sm text-gray-600">Akses forum diskusi dan tanya jawab</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Course Stats -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 py-6 border-t border-gray-200">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">{{ $course->chapters->count() }}</div>
                                <div class="text-sm text-gray-600">Bagian</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">{{ $totalLessons }}</div>
                                <div class="text-sm text-gray-600">Pelajaran</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">{{ floor($totalDuration / 60) }}j</div>
                                <div class="text-sm text-gray-600">Total Durasi</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-primary">{{ $course->total_students ?? 0 }}</div>
                                <div class="text-sm text-gray-600">Siswa</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 2. Kurikulum Kursus (Course Curriculum) - Main Content -->
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Kurikulum Kursus</h2>

                    @if($course->chapters->count() > 0)
                        <div class="flex items-center justify-between mb-6">
                            <div class="text-sm text-gray-600">
                                <span>{{ $course->chapters->count() }} bagian</span>
                                <span class="mx-2">•</span>
                                <span>{{ $totalLessons }} pelajaran</span>
                                <span class="mx-2">•</span>
                                <span>{{ floor($totalDuration / 60) }}j {{ $totalDuration % 60 }}m total durasi</span>
                            </div>
                        </div>

                        <div class="space-y-4">
                            @foreach($course->chapters as $chapterIndex => $chapter)
                                <div class="border border-gray-200 rounded-lg overflow-hidden">
                                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <button class="mr-3 text-gray-600 hover:text-gray-800">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                    </svg>
                                                </button>
                                                <h3 class="font-semibold text-gray-900">
                                                    Bagian {{ $chapterIndex + 1 }}: {{ $chapter->title }}
                                                </h3>
                                            </div>
                                            <div class="text-sm text-gray-600">
                                                {{ $chapter->lessons->count() }} pelajaran
                                                @if($chapter->lessons->sum('duration_minutes') > 0)
                                                    <span class="mx-2">•</span>
                                                    <span>{{ $chapter->lessons->sum('duration_minutes') }} menit</span>
                                                @endif
                                            </div>
                                        </div>
                                        @if($chapter->description)
                                            <p class="text-sm text-gray-600 mt-2 ml-8">{{ $chapter->description }}</p>
                                        @endif
                                    </div>

                                    <div class="divide-y divide-gray-100">
                                        @foreach($chapter->lessons as $lesson)
                                            <div class="px-6 py-4 hover:bg-gray-50">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-3">
                                                        @if($lesson->type === 'video')
                                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                            </svg>
                                                        @elseif($lesson->type === 'text')
                                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                        @elseif($lesson->type === 'quiz')
                                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        @elseif($lesson->type === 'assignment')
                                                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                                            </svg>
                                                        @endif
                                                        <div>
                                                            <h4 class="text-sm font-medium text-gray-900">{{ $lesson->title }}</h4>
                                                            @if($lesson->description)
                                                                <p class="text-xs text-gray-600 mt-1">{{ Str::limit($lesson->description, 80) }}</p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center space-x-3 text-sm text-gray-600">
                                                        @if($lesson->is_preview)
                                                            <span class="text-primary font-medium">Preview</span>
                                                        @endif
                                                        @if($lesson->duration_minutes)
                                                            <span>{{ $lesson->duration_minutes }}:00</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Kurikulum Sedang Disiapkan</h3>
                            <p class="text-gray-600 max-w-md mx-auto">
                                Materi pembelajaran sedang dalam tahap persiapan. Silakan kembali lagi nanti untuk melihat konten kursus yang lengkap.
                            </p>
                        </div>
                    @endif
                </div>

                <!-- 3. Yang Akan Anda Pelajari (What You'll Learn) -->
                @if($course->learning_outcomes)
                    <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Yang Akan Anda Pelajari</h2>
                        <div class="grid md:grid-cols-2 gap-4">
                            @foreach($course->learning_outcomes as $outcome)
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-gray-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $outcome }}</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- 4. Persyaratan (Requirements) -->
                @if($course->requirements)
                    <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Persyaratan</h2>
                        <ul class="space-y-3">
                            @foreach($course->requirements as $requirement)
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $requirement }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- 5. Untuk Siapa Kursus Ini (Who This Course Is For) -->
                @if($course->target_audience)
                    <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Untuk Siapa Kursus Ini</h2>
                        <ul class="space-y-3">
                            @foreach($course->target_audience as $audience)
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-gray-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $audience }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>

            <!-- Enhanced Sidebar -->
            <div class="lg:col-span-1">
                <!-- Instructor Section -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Instruktur</h3>
                    <div class="text-center">
                        <div class="w-20 h-20 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                            @if($course->tutor->profile_picture)
                                <img src="{{ asset('storage/' . $course->tutor->profile_picture) }}" alt="{{ $course->tutor->name }}" class="w-20 h-20 rounded-full object-cover">
                            @else
                                <svg class="w-10 h-10 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            @endif
                        </div>
                        <h4 class="font-semibold text-gray-900 text-lg">{{ $course->tutor->name }}</h4>
                        @if($course->tutor->job_title)
                            <p class="text-sm text-gray-600 mb-3">{{ $course->tutor->job_title }}</p>
                        @endif
                    </div>

                    <!-- Instructor Stats -->
                    <div class="grid grid-cols-2 gap-4 py-4 border-t border-gray-200 mt-4">
                        <div class="text-center">
                            <div class="text-lg font-semibold text-gray-900">{{ $course->tutor->courses()->published()->count() }}</div>
                            <div class="text-xs text-gray-600">Kursus</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold text-gray-900">{{ number_format($course->tutor->courses()->published()->sum('total_students')) }}</div>
                            <div class="text-xs text-gray-600">Siswa</div>
                        </div>
                    </div>

                    @if($course->tutor->bio)
                        <div class="pt-4 border-t border-gray-200">
                            <p class="text-sm text-gray-600 leading-relaxed">{{ Str::limit($course->tutor->bio, 120) }}</p>
                        </div>
                    @endif

                    <!-- Show More Instructor Info -->
                    <div class="pt-4">
                        <a href="#" class="text-primary text-sm font-medium hover:text-primary-dark">Lihat Profil Lengkap</a>
                    </div>
                </div>

                <!-- More Courses by This Instructor -->
                @php
                    $instructorCourses = $course->tutor->courses()
                        ->published()
                        ->where('id', '!=', $course->id)
                        ->limit(3)
                        ->get();
                @endphp
                @if($instructorCourses->count() > 0)
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Kursus Lain dari Instruktur Ini</h3>
                        <div class="space-y-4">
                            @foreach($instructorCourses as $instructorCourse)
                                <div class="flex space-x-3">
                                    <div class="w-16 h-12 bg-gradient-to-br from-primary to-secondary rounded flex-shrink-0 overflow-hidden">
                                        @if($instructorCourse->thumbnail)
                                            <img src="{{ asset('storage/' . $instructorCourse->thumbnail) }}" alt="{{ $instructorCourse->title }}" class="w-full h-full object-cover">
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 line-clamp-2 leading-tight">
                                            <a href="{{ route('course.show', $instructorCourse) }}" class="hover:text-primary">
                                                {{ $instructorCourse->title }}
                                            </a>
                                        </h4>
                                        <div class="flex items-center justify-between mt-1">
                                            <p class="text-xs text-gray-600">
                                                @if($instructorCourse->price == 0)
                                                    <span class="text-green-600 font-medium">Gratis</span>
                                                @else
                                                    <span class="font-medium">{{ $instructorCourse->formatted_price }}</span>
                                                @endif
                                            </p>
                                            @if($instructorCourse->average_rating > 0)
                                                <div class="flex items-center">
                                                    <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                    <span class="text-xs text-gray-600">{{ number_format($instructorCourse->average_rating, 1) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Students Also Bought -->
                @if($relatedCourses->count() > 0)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Siswa Juga Membeli</h3>
                        <div class="space-y-4">
                            @foreach($relatedCourses as $relatedCourse)
                                <div class="flex space-x-3">
                                    <div class="w-16 h-12 bg-gradient-to-br from-primary to-secondary rounded flex-shrink-0 overflow-hidden">
                                        @if($relatedCourse->thumbnail)
                                            <img src="{{ asset('storage/' . $relatedCourse->thumbnail) }}" alt="{{ $relatedCourse->title }}" class="w-full h-full object-cover">
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 line-clamp-2 leading-tight">
                                            <a href="{{ route('course.show', $relatedCourse) }}" class="hover:text-primary">
                                                {{ $relatedCourse->title }}
                                            </a>
                                        </h4>
                                        <div class="flex items-center justify-between mt-1">
                                            <p class="text-xs text-gray-600">
                                                @if($relatedCourse->price == 0)
                                                    <span class="text-green-600 font-medium">Gratis</span>
                                                @else
                                                    <span class="font-medium">{{ $relatedCourse->formatted_price }}</span>
                                                @endif
                                            </p>
                                            @if($relatedCourse->average_rating > 0)
                                                <div class="flex items-center">
                                                    <svg class="w-3 h-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                                    </svg>
                                                    <span class="text-xs text-gray-600">{{ number_format($relatedCourse->average_rating, 1) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
