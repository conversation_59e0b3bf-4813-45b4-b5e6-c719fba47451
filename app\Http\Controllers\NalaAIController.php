<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use App\Http\Controllers\Nala\ChatController;

class NalaAIController extends Controller
{
    /**
     * Handle Nala AI chat requests - Delegate to ChatController
     */
    public function chat(Request $request)
    {
        return app(ChatController::class)->chat($request);
    }

    /**
     * Get user's chat history
     */
    public function getChatHistory(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        try {
            // Rate limiting: max 10 requests per minute per user
            $cacheKey = 'nala_history_requests_' . auth()->id();
            $requests = cache()->get($cacheKey, 0);

            if ($requests >= 10) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many requests. Please try again later.'
                ], 429);
            }

            cache()->put($cacheKey, $requests + 1, 60); // 1 minute

            // Optimized query with eager loading
            $conversations = NalaChatConversation::forUser(auth()->id())
                ->active()
                ->with(['messages' => function ($query) {
                    $query->where('status', '!=', 'deleted')
                          ->orderBy('created_at', 'desc')
                          ->limit(1)
                          ->select(['id', 'conversation_id', 'content', 'created_at']);
                }])
                ->select(['id', 'title', 'message_count', 'last_message_at', 'started_context'])
                ->orderBy('last_message_at', 'desc')
                ->limit(10) // Limit to recent 10 conversations for performance
                ->get();

            // Transform data efficiently
            $conversations->transform(function ($conversation) {
                return [
                    'id' => $conversation->id,
                    'title' => $conversation->title ?: 'Chat dengan Nala',
                    'message_preview' => $conversation->messages->first()?->content ?? '',
                    'total_messages' => $conversation->message_count,
                    'last_message_at' => $conversation->last_message_at,
                    'started_context' => $conversation->started_context
                ];
            });

            return response()->json([
                'success' => true,
                'conversations' => ['data' => $conversations]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching chat history for user ' . auth()->id() . ': ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch chat history'
            ], 500);
        }
    }

    /**
     * Get specific conversation with messages
     */
    public function getConversation(Request $request, $conversationId)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        // Validate conversation ID format
        if (!preg_match('/^[a-f0-9\-]{36}$/i', $conversationId)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid conversation ID format'
            ], 400);
        }

        try {
            // Rate limiting for conversation access
            $cacheKey = 'nala_conversation_requests_' . auth()->id();
            $requests = cache()->get($cacheKey, 0);

            if ($requests >= 20) {
                return response()->json([
                    'success' => false,
                    'message' => 'Too many requests. Please try again later.'
                ], 429);
            }

            cache()->put($cacheKey, $requests + 1, 60);

            // Secure query - ensure user owns the conversation
            $conversation = NalaChatConversation::where('id', $conversationId)
                ->where('user_id', auth()->id())
                ->where('status', 'active')
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            // Get only recent exchanges with optimized query
            $recentMessages = $conversation->messages()
                ->where('status', '!=', 'deleted')
                ->orderBy('created_at', 'desc')
                ->limit(30) // Last 15 exchanges = 30 messages max
                ->select(['id', 'sender', 'content', 'created_at'])
                ->get()
                ->reverse()
                ->values();

            return response()->json([
                'success' => true,
                'conversation' => [
                    'id' => $conversation->id,
                    'title' => $conversation->title ?: 'Chat dengan Nala',
                    'started_route' => $conversation->started_route,
                    'started_context' => $conversation->started_context,
                    'message_count' => $conversation->message_count,
                    'last_message_at' => $conversation->last_message_at,
                    'messages' => $recentMessages
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Error fetching conversation ' . $conversationId . ' for user ' . auth()->id() . ': ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to fetch conversation'
            ], 500);
        }
    }

    /**
     * Delete conversation
     */
    public function deleteConversation(Request $request, $conversationId)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        // Validate conversation ID format
        if (!preg_match('/^[a-f0-9\-]{36}$/i', $conversationId)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid conversation ID format'
            ], 400);
        }

        try {
            // Secure query - ensure user owns the conversation
            $conversation = NalaChatConversation::where('id', $conversationId)
                ->where('user_id', auth()->id())
                ->first();

            if (!$conversation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Conversation not found or access denied'
                ], 404);
            }

            // Soft delete the conversation (changes status to 'deleted')
            $conversation->softDelete();

            Log::info('Conversation deleted', [
                'conversation_id' => $conversationId,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Conversation deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting conversation ' . $conversationId . ' for user ' . auth()->id() . ': ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to delete conversation'
            ], 500);
        }
    }

    /**
     * Clear all chat history for user
     */
    public function clearChatHistory(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        try {
            // Rate limiting for clear history action
            $cacheKey = 'nala_clear_history_' . auth()->id();
            $lastClear = cache()->get($cacheKey);

            if ($lastClear && now()->diffInMinutes($lastClear) < 5) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please wait 5 minutes before clearing history again'
                ], 429);
            }

            // Get conversation count before deletion for logging
            $conversationCount = NalaChatConversation::forUser(auth()->id())->active()->count();

            // Soft delete all conversations for the user
            NalaChatConversation::forUser(auth()->id())
                ->active()
                ->update(['status' => 'deleted']);

            // Cache the clear action timestamp
            cache()->put($cacheKey, now(), 300); // 5 minutes

            Log::info('Chat history cleared', [
                'user_id' => auth()->id(),
                'conversations_deleted' => $conversationCount
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Chat history cleared successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error clearing chat history for user ' . auth()->id() . ': ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Unable to clear chat history'
            ], 500);
        }
    }

    /**
     * Auto-update user profile from conversation
     */
    public function updateProfileFromConversation(Request $request)
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $request->validate([
            'field' => 'required|string',
            'value' => 'required',
            'conversation_id' => 'nullable|string|exists:nala_chat_conversations,id'
        ]);

        $user = auth()->user();
        $field = $request->input('field');
        $value = $request->input('value');

        // Map conversation fields to database fields
        $fieldMapping = [
            'job_title' => 'job_title',
            'company' => 'company',
            'experience_years' => 'experience_years',
            'skills' => 'skills',
            'learning_interests' => 'minat_belajar',
            'career_goals' => 'career_goals',
            'industry_interests' => 'industry_interests',
            'salary_expectations' => 'salary_expectations',
            'work_preferences' => 'work_preferences',
            'education_level' => 'pendidikan',
            'location' => 'location',
        ];

        if (!isset($fieldMapping[$field])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid field'
            ], 400);
        }

        $dbField = $fieldMapping[$field];

        // Handle JSON fields - Laravel will auto-cast these based on model casts
        $jsonFields = ['skills', 'minat_belajar', 'career_goals', 'industry_interests', 'salary_expectations', 'work_preferences'];
        if (in_array($dbField, $jsonFields)) {
            $value = is_array($value) ? $value : [$value];
        }

        // Update user profile using DB query builder
        DB::table('users')
            ->where('id', $user->id)
            ->update([$dbField => $value]);

        // Log the profile update in conversation metadata
        $conversation = NalaChatConversation::find($request->input('conversation_id'));
        if ($conversation && $conversation->user_id === $user->id) {
            $metadata = $conversation->context_data ?? [];
            $metadata['profile_updates'] = $metadata['profile_updates'] ?? [];
            $metadata['profile_updates'][] = [
                'field' => $field,
                'value' => $value,
                'updated_at' => now()->toISOString()
            ];
            $conversation->update(['context_data' => $metadata]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'field' => $field,
            'value' => $value
        ]);
    }
}