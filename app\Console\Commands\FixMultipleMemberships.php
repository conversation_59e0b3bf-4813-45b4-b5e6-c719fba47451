<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserMembership;
use Illuminate\Support\Facades\DB;

class FixMultipleMemberships extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:multiple-memberships {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix users with multiple active memberships by keeping only the most recent one';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        if ($email) {
            $this->fixUserMemberships($email);
        } else {
            $this->fixAllUserMemberships();
        }
    }

    /**
     * Fix memberships for a specific user
     */
    private function fixUserMemberships(string $email)
    {
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return;
        }

        $this->info("Fixing memberships for {$user->email}");
        $this->processUserMemberships($user);
    }

    /**
     * Fix memberships for all users
     */
    private function fixAllUserMemberships()
    {
        $this->info("Finding users with multiple active memberships...");
        
        $usersWithMultipleMemberships = DB::table('user_memberships')
            ->select('user_id', DB::raw('COUNT(*) as membership_count'))
            ->where('status', 'active')
            ->groupBy('user_id')
            ->having('membership_count', '>', 1)
            ->get();

        if ($usersWithMultipleMemberships->isEmpty()) {
            $this->info("No users found with multiple active memberships.");
            return;
        }

        $this->info("Found {$usersWithMultipleMemberships->count()} users with multiple active memberships.");

        foreach ($usersWithMultipleMemberships as $userInfo) {
            $user = User::find($userInfo->user_id);
            if ($user) {
                $this->info("Processing {$user->email} ({$userInfo->membership_count} active memberships)");
                $this->processUserMemberships($user);
            }
        }
    }

    /**
     * Process memberships for a single user
     */
    private function processUserMemberships(User $user)
    {
        $activeMemberships = UserMembership::where('user_id', $user->id)
            ->where('status', 'active')
            ->with('membershipPlan')
            ->orderBy('created_at', 'desc')
            ->get();

        if ($activeMemberships->count() <= 1) {
            $this->line("  User has {$activeMemberships->count()} active membership(s). No action needed.");
            return;
        }

        $this->line("  Found {$activeMemberships->count()} active memberships:");
        
        foreach ($activeMemberships as $index => $membership) {
            $this->line("    {$index}: {$membership->membershipPlan->name} (Created: {$membership->created_at})");
        }

        // Keep the most recent membership (first in the ordered list)
        $keepMembership = $activeMemberships->first();
        $expireMemberships = $activeMemberships->slice(1);

        $this->line("  Keeping: {$keepMembership->membershipPlan->name} (ID: {$keepMembership->id})");
        $this->line("  Expiring {$expireMemberships->count()} older membership(s)");

        DB::beginTransaction();
        try {
            // Expire older memberships
            foreach ($expireMemberships as $membership) {
                $membership->update(['status' => 'expired']);
                $this->line("    Expired: {$membership->membershipPlan->name} (ID: {$membership->id})");
            }

            // Update user's current_membership_id to the kept membership
            $user->update(['current_membership_id' => $keepMembership->id]);

            DB::commit();
            $this->info("  ✓ Successfully fixed memberships for {$user->email}");
            
        } catch (\Exception $e) {
            DB::rollback();
            $this->error("  ✗ Failed to fix memberships for {$user->email}: " . $e->getMessage());
        }
    }
}
