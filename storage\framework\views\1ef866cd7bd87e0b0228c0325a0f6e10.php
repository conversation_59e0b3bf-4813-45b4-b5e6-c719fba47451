<!-- Clean Header Component with Isolated Styling -->
<header class="header-main">
    <div class="header-container">
        <div class="header-content">
            <!-- Logo Section -->
            <div class="header-logo">
                <a href="<?php echo e(route('home')); ?>" class="header-logo-link">
                    <img src="<?php echo e(asset('images/logo.svg')); ?>" alt="Ngambiskuy Logo" class="header-logo-image">
                    <span class="header-logo-text">Ngambiskuy</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="header-nav">
                <a href="<?php echo e(route('courses.index')); ?>" class="header-nav-link">Belajar</a>
                <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-nav-link">Mengajar</a>
                <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="header-nav-link">Co<PERSON> Gratis</a>
                <a href="<?php echo e(route('blog.index')); ?>" class="header-nav-link">Blog</a>
                <a href="<?php echo e(route('about-us')); ?>" class="header-nav-link">Tentang Kami</a>
            </nav>

            <!-- Right Section -->
            <div class="header-actions">
                <!-- Authentication Section -->
                <div class="header-auth">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- User Dropdown -->
                        <div class="header-user-dropdown">
                            <button type="button" class="header-user-button" id="headerUserButton" aria-expanded="false" aria-haspopup="true">
                                <div class="header-user-avatar">
                                    <?php echo e(strtoupper(substr(Auth::user()->name, 0, 1))); ?>

                                </div>
                                <div class="header-user-info">
                                    <span class="header-user-name"><?php echo e(Auth::user()->name); ?></span>
                                    <span class="header-user-role">
                                        <?php if(Auth::user()->isTutor()): ?>
                                            Tutor
                                        <?php elseif(Auth::user()->hasTutorProfile()): ?>
                                            Calon Tutor
                                        <?php else: ?>
                                            Siswa
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <svg class="header-user-chevron" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div class="header-user-menu" id="headerUserMenu" role="menu">
                                <!-- User Info Header -->
                                <div class="header-user-menu-header">
                                    <div class="header-user-menu-avatar">
                                        <?php echo e(strtoupper(substr(Auth::user()->name, 0, 1))); ?>

                                    </div>
                                    <div class="header-user-menu-info">
                                        <p class="header-user-menu-name"><?php echo e(Auth::user()->name); ?></p>
                                        <p class="header-user-menu-email"><?php echo e(Auth::user()->email); ?></p>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                <div class="header-user-menu-items">
                                    
                                    <?php if(Auth::user()->isTutor()): ?>
                                        <a href="<?php echo e(route('tutor.dashboard')); ?>" class="header-user-menu-item">
                                            <div class="header-user-menu-icon header-user-menu-icon-primary">
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                                </svg>
                                            </div>
                                            <div class="header-user-menu-text">
                                                <p class="header-user-menu-title">Dashboard Tutor</p>
                                                <p class="header-user-menu-desc">Kelola kelas dan siswa</p>
                                            </div>
                                        </a>
                                    <?php endif; ?>

                                    
                                    <?php if(Auth::user()->hasTutorProfile() && !Auth::user()->isTutor()): ?>
                                        <a href="<?php echo e(route('tutor.register.status')); ?>" class="header-user-menu-item">
                                            <div class="header-user-menu-icon header-user-menu-icon-yellow">
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                            <div class="header-user-menu-text">
                                                <p class="header-user-menu-title">Status Aplikasi</p>
                                                <p class="header-user-menu-desc">Cek status pendaftaran tutor</p>
                                            </div>
                                        </a>
                                    <?php endif; ?>

                                    
                                    <a href="<?php echo e(route('user.dashboard')); ?>" class="header-user-menu-item">
                                        <div class="header-user-menu-icon header-user-menu-icon-blue">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                            </svg>
                                        </div>
                                        <div class="header-user-menu-text">
                                            <p class="header-user-menu-title">Dashboard Student</p>
                                            <p class="header-user-menu-desc">Lihat progress belajar</p>
                                        </div>
                                    </a>

                                    
                                    <?php if(!Auth::user()->hasTutorProfile()): ?>
                                        <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-user-menu-item">
                                            <div class="header-user-menu-icon header-user-menu-icon-green">
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                            </div>
                                            <div class="header-user-menu-text">
                                                <p class="header-user-menu-title">Jadi Pengajar</p>
                                                <p class="header-user-menu-desc">Mulai mengajar di platform</p>
                                            </div>
                                        </a>
                                    <?php endif; ?>
                                </div>

                                <!-- Logout Section -->
                                <div class="header-user-menu-divider"></div>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="header-user-menu-logout">
                                        <div class="header-user-menu-icon header-user-menu-icon-red">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                            </svg>
                                        </div>
                                        <div class="header-user-menu-text">
                                            <p class="header-user-menu-title">Keluar</p>
                                            <p class="header-user-menu-desc">Logout dari akun</p>
                                        </div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Guest Actions -->
                        <div class="header-guest-actions">
                            <a href="<?php echo e(route('login')); ?>" class="header-guest-login">Masuk</a>
                            <a href="<?php echo e(route('register')); ?>" class="header-guest-register">Daftar</a>
                            <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-guest-tutor">Jadi Pengajar</a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Mobile Menu Toggle -->
                <button class="header-mobile-toggle" type="button" id="headerMobileToggle" aria-expanded="false">
                    <svg class="header-mobile-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div class="header-mobile-menu" id="headerMobileMenu">
            <div class="header-mobile-content">
                <!-- Navigation Links -->
                <nav class="header-mobile-nav">
                    <a href="<?php echo e(route('courses.index')); ?>" class="header-mobile-nav-item">
                        <div class="header-mobile-nav-icon">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <span>Belajar</span>
                    </a>

                    <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-mobile-nav-item">
                        <div class="header-mobile-nav-icon">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <span>Mengajar</span>
                    </a>

                    <a href="<?php echo e(route('courses.index', ['price_type' => 'free'])); ?>" class="header-mobile-nav-item">
                        <div class="header-mobile-nav-icon">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <span>Coba Gratis</span>
                    </a>

                    <a href="<?php echo e(route('blog.index')); ?>" class="header-mobile-nav-item">
                        <div class="header-mobile-nav-icon">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                        </div>
                        <span>Blog</span>
                    </a>

                    <a href="<?php echo e(route('about-us')); ?>" class="header-mobile-nav-item">
                        <div class="header-mobile-nav-icon">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <span>Tentang Kami</span>
                    </a>
                </nav>

                <!-- User Section -->
                <div class="header-mobile-user">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- User Info -->
                        <div class="header-mobile-user-info">
                            <div class="header-mobile-user-avatar">
                                <?php echo e(strtoupper(substr(Auth::user()->name, 0, 1))); ?>

                            </div>
                            <div class="header-mobile-user-details">
                                <p class="header-mobile-user-name"><?php echo e(Auth::user()->name); ?></p>
                                <p class="header-mobile-user-role">
                                    <?php if(Auth::user()->isTutor()): ?>
                                        Tutor
                                    <?php elseif(Auth::user()->hasTutorProfile()): ?>
                                        Calon Tutor
                                    <?php else: ?>
                                        Siswa
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="header-mobile-actions">
                            
                            <?php if(Auth::user()->isTutor()): ?>
                                <a href="<?php echo e(route('tutor.dashboard')); ?>" class="header-mobile-btn header-mobile-btn-primary">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Dashboard Tutor
                                </a>
                            <?php endif; ?>

                            
                            <?php if(Auth::user()->hasTutorProfile() && !Auth::user()->isTutor()): ?>
                                <a href="<?php echo e(route('tutor.register.status')); ?>" class="header-mobile-btn header-mobile-btn-outline">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Status Aplikasi
                                </a>
                            <?php endif; ?>

                            
                            <a href="<?php echo e(route('user.dashboard')); ?>" class="header-mobile-btn header-mobile-btn-primary">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                                Dashboard Student
                            </a>

                            
                            <?php if(!Auth::user()->hasTutorProfile()): ?>
                                <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-mobile-btn header-mobile-btn-outline">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Jadi Pengajar
                                </a>
                            <?php endif; ?>
                        </div>

                        <!-- Logout Button -->
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="header-mobile-logout">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Keluar
                            </button>
                        </form>
                    <?php else: ?>
                        <!-- Guest Actions -->
                        <div class="header-mobile-guest">
                            <a href="<?php echo e(route('login')); ?>" class="header-mobile-btn header-mobile-btn-ghost">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Masuk
                            </a>
                            <a href="<?php echo e(route('register')); ?>" class="header-mobile-btn header-mobile-btn-primary">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                </svg>
                                Daftar
                            </a>
                            <a href="<?php echo e(route('tutor.register.terms')); ?>" class="header-mobile-btn header-mobile-btn-outline">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Jadi Pengajar
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Header Isolated CSS Styles -->
<style>
/* Header Main Container */
.header-main {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

/* Logo Section */
.header-logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.header-logo-image {
    width: 2.5rem;
    height: 2.5rem;
}

.header-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
}

/* Desktop Navigation */
.header-nav {
    display: none;
    align-items: center;
    gap: 2rem;
}

.header-nav-link {
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.header-nav-link:hover {
    color: #FF6B35;
}

/* Right Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-auth {
    display: none;
    align-items: center;
}

/* User Dropdown */
.header-user-dropdown {
    position: relative;
}

.header-user-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-user-button:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-user-avatar {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.header-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
}

.header-user-role {
    font-size: 0.75rem;
    color: #6b7280;
}

.header-user-chevron {
    width: 1rem;
    height: 1rem;
    color: #9ca3af;
    transition: transform 0.2s ease;
}

/* User Dropdown Menu */
.header-user-menu {
    position: absolute;
    right: 0;
    top: calc(100% + 0.5rem);
    width: 16rem;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    display: none;
    z-index: 1001;
}

.header-user-menu.show {
    display: block;
}

.header-user-menu-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    margin-bottom: 0.5rem;
}

.header-user-menu-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.header-user-menu-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.header-user-menu-email {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-user-menu-items {
    padding: 0.5rem 0;
}

.header-user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
}

.header-user-menu-item:hover {
    background: #FF6B35;
    color: white;
}

.header-user-menu-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.header-user-menu-icon svg {
    width: 1rem;
    height: 1rem;
}

.header-user-menu-icon-primary {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
}

.header-user-menu-icon-yellow {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.header-user-menu-icon-blue {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.header-user-menu-icon-green {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.header-user-menu-icon-red {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.header-user-menu-item:hover .header-user-menu-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.header-user-menu-title {
    font-weight: 500;
    margin: 0;
}

.header-user-menu-desc {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-user-menu-item:hover .header-user-menu-desc {
    color: rgba(255, 255, 255, 0.8);
}

.header-user-menu-divider {
    height: 1px;
    background: #f3f4f6;
    margin: 0.5rem 0;
}

.header-user-menu-logout {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: #374151;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-user-menu-logout:hover {
    background: #fef2f2;
    color: #dc2626;
}

/* Guest Actions */
.header-guest-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-guest-login {
    color: #6b7280;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.header-guest-login:hover {
    color: #FF6B35;
}

.header-guest-register {
    background: #FF6B35;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: background 0.2s ease;
}

.header-guest-register:hover {
    background: #E55A2B;
}

.header-guest-tutor {
    border: 1px solid #FF6B35;
    color: #FF6B35;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.header-guest-tutor:hover {
    background: #FF6B35;
    color: white;
}

/* Mobile Toggle */
.header-mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: none;
    border: none;
    border-radius: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-mobile-toggle:hover {
    background: #f3f4f6;
    color: #FF6B35;
}

.header-mobile-icon {
    width: 1.5rem;
    height: 1.5rem;
    transition: transform 0.2s ease;
}

/* Mobile Menu */
.header-mobile-menu {
    display: none;
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-mobile-menu.show {
    display: block;
}

.header-mobile-content {
    padding: 1.5rem 1rem;
}

.header-mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.header-mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.header-mobile-nav-item:hover {
    background: #f3f4f6;
    color: #FF6B35;
    transform: translateX(0.25rem);
}

.header-mobile-nav-icon {
    width: 2rem;
    height: 2rem;
    background: #f3f4f6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.2s ease;
}

.header-mobile-nav-item:hover .header-mobile-nav-icon {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
}

.header-mobile-nav-icon svg {
    width: 1rem;
    height: 1rem;
}

/* Mobile User Section */
.header-mobile-user {
    border-top: 1px solid #f3f4f6;
    padding-top: 1.5rem;
}

.header-mobile-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.header-mobile-user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.header-mobile-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.header-mobile-user-role {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.header-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    transform: scale(1);
}

.header-mobile-btn:hover {
    transform: scale(1.02);
}

.header-mobile-btn:active {
    transform: scale(0.98);
}

.header-mobile-btn svg {
    width: 1rem;
    height: 1rem;
}

.header-mobile-btn-primary {
    background: #FF6B35;
    color: white;
}

.header-mobile-btn-primary:hover {
    background: #E55A2B;
}

.header-mobile-btn-outline {
    border: 1px solid #d1d5db;
    color: #374151;
    background: white;
}

.header-mobile-btn-outline:hover {
    background: #f3f4f6;
}

.header-mobile-btn-ghost {
    color: #374151;
    background: transparent;
}

.header-mobile-btn-ghost:hover {
    background: #f3f4f6;
}

.header-mobile-logout {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: #dc2626;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-mobile-logout:hover {
    background: #fef2f2;
}

.header-mobile-logout svg {
    width: 1rem;
    height: 1rem;
}

.header-mobile-guest {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Responsive Design */
@media (min-width: 768px) {
    .header-auth {
        display: flex;
    }
}

@media (min-width: 1024px) {
    .header-nav {
        display: flex;
    }

    .header-mobile-toggle {
        display: none;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .header-user-info {
        display: none;
    }

    .header-user-name {
        display: block;
        max-width: 6rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 640px) {
    .header-container {
        padding: 0 0.75rem;
    }

    .header-logo-image {
        width: 2rem;
        height: 2rem;
    }

    .header-logo-text {
        font-size: 1.125rem;
    }

    .header-user-menu {
        width: calc(100vw - 2rem);
        right: -0.75rem;
    }
}
</style>

<!-- Header JavaScript Functionality -->
<script>
(function() {
    'use strict';

    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initHeaderFunctionality();
    });

    function initHeaderFunctionality() {
        initUserDropdown();
        initMobileMenu();
    }

    // User Dropdown Functionality
    function initUserDropdown() {
        const userButton = document.getElementById('headerUserButton');
        const userMenu = document.getElementById('headerUserMenu');

        if (!userButton || !userMenu) return;

        // Toggle dropdown on button click
        userButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = userMenu.classList.contains('show');
            const chevron = userButton.querySelector('.header-user-chevron');

            if (isOpen) {
                userMenu.classList.remove('show');
                userButton.setAttribute('aria-expanded', 'false');
                if (chevron) chevron.style.transform = 'rotate(0deg)';
            } else {
                userMenu.classList.add('show');
                userButton.setAttribute('aria-expanded', 'true');
                if (chevron) chevron.style.transform = 'rotate(180deg)';
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userButton.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.classList.remove('show');
                userButton.setAttribute('aria-expanded', 'false');
                const chevron = userButton.querySelector('.header-user-chevron');
                if (chevron) chevron.style.transform = 'rotate(0deg)';
            }
        });

        // Close dropdown on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && userMenu.classList.contains('show')) {
                userMenu.classList.remove('show');
                userButton.setAttribute('aria-expanded', 'false');
                const chevron = userButton.querySelector('.header-user-chevron');
                if (chevron) chevron.style.transform = 'rotate(0deg)';
                userButton.focus();
            }
        });
    }

    // Mobile Menu Functionality
    function initMobileMenu() {
        const mobileToggle = document.getElementById('headerMobileToggle');
        const mobileMenu = document.getElementById('headerMobileMenu');

        if (!mobileToggle || !mobileMenu) return;

        // Toggle mobile menu on button click
        mobileToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = mobileMenu.classList.contains('show');
            const icon = mobileToggle.querySelector('.header-mobile-icon');

            if (isOpen) {
                mobileMenu.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                if (icon) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                }
            } else {
                mobileMenu.classList.add('show');
                mobileToggle.setAttribute('aria-expanded', 'true');
                if (icon) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                }
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
                mobileMenu.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                const icon = mobileToggle.querySelector('.header-mobile-icon');
                if (icon) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                }
            }
        });

        // Close mobile menu on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu.classList.contains('show')) {
                mobileMenu.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                const icon = mobileToggle.querySelector('.header-mobile-icon');
                if (icon) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                }
                mobileToggle.focus();
            }
        });

        // Close mobile menu when window is resized to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024 && mobileMenu.classList.contains('show')) {
                mobileMenu.classList.remove('show');
                mobileToggle.setAttribute('aria-expanded', 'false');
                const icon = mobileToggle.querySelector('.header-mobile-icon');
                if (icon) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                }
            }
        });
    }
})();
</script>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/partials/header.blade.php ENDPATH**/ ?>