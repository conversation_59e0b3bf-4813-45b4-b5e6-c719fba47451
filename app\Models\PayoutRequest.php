<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class PayoutRequest extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tutor_id',
        'request_id',
        'amount',
        'platform_fee',
        'net_amount',
        'currency',
        'status',
        'notes',
        'tutor_notes',
        'payment_method',
        'payment_details',
        'requested_at',
        'processed_at',
        'paid_at',
        'rejected_at',
        'cancelled_at',
        'processed_by',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'payment_details' => 'array',
        'metadata' => 'array',
        'requested_at' => 'datetime',
        'processed_at' => 'datetime',
        'paid_at' => 'datetime',
        'rejected_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the tutor that made the payout request.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    /**
     * Get the admin who processed the request.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope a query to only include pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved requests.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include paid requests.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope a query to only include rejected requests.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Mark request as approved.
     */
    public function markAsApproved(?string $notes = null, $processedBy = null): void
    {
        $this->update([
            'status' => 'approved',
            'processed_at' => now(),
            'notes' => $notes,
            'processed_by' => $processedBy,
        ]);
    }

    /**
     * Mark request as paid.
     */
    public function markAsPaid(?string $notes = null): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'notes' => $notes,
        ]);
    }

    /**
     * Mark request as rejected.
     */
    public function markAsRejected(string $reason, $processedBy = null): void
    {
        $this->update([
            'status' => 'rejected',
            'rejected_at' => now(),
            'notes' => $reason,
            'processed_by' => $processedBy,
        ]);
    }

    /**
     * Mark request as cancelled.
     */
    public function markAsCancelled(?string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'tutor_notes' => $reason,
        ]);
    }

    /**
     * Get the formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'IDR ' . number_format($this->amount, 0, ',', '.');
    }

    /**
     * Get the formatted net amount.
     */
    public function getFormattedNetAmountAttribute(): string
    {
        return 'IDR ' . number_format($this->net_amount, 0, ',', '.');
    }

    /**
     * Get status name in Indonesian.
     */
    public function getStatusNameAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Menunggu',
            'processing' => 'Diproses',
            'approved' => 'Disetujui',
            'paid' => 'Dibayar',
            'rejected' => 'Ditolak',
            'cancelled' => 'Dibatalkan',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get payment method name in Indonesian.
     */
    public function getPaymentMethodNameAttribute(): string
    {
        return match($this->payment_method) {
            'bank_transfer' => 'Transfer Bank',
            'gopay' => 'GoPay',
            'ovo' => 'OVO',
            'dana' => 'DANA',
            'shopeepay' => 'ShopeePay',
            default => ucfirst($this->payment_method)
        };
    }

    /**
     * Generate unique request ID.
     */
    public static function generateRequestId(): string
    {
        $prefix = 'PO';
        $timestamp = now()->format('ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . $timestamp . $random;
    }

    /**
     * Calculate platform fee for payout.
     */
    public static function calculatePlatformFee(float $amount): float
    {
        return $amount * 0.02; // 2% platform fee for payouts
    }

    /**
     * Get status color classes for UI.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'processing' => 'bg-blue-100 text-blue-800',
            'approved' => 'bg-green-100 text-green-800',
            'paid' => 'bg-emerald-100 text-emerald-800',
            'rejected' => 'bg-red-100 text-red-800',
            'cancelled' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }
}
