<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('nala_chat_conversations', function (Blueprint $table) {
            try {
                // Add composite index for user queries
                $table->index(['user_id', 'status', 'last_message_at'], 'idx_user_status_last_message');
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
            
            try {
                // Add index for cleanup operations
                $table->index(['status', 'created_at'], 'idx_status_created');
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
        });

        Schema::table('nala_chat_messages', function (Blueprint $table) {
            try {
                // Add composite index for conversation message queries
                $table->index(['conversation_id', 'status', 'created_at'], 'idx_conversation_status_created');
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
            
            try {
                // Add index for message cleanup operations
                $table->index(['status', 'created_at'], 'idx_message_status_created');
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
            
            try {
                // Add index for sender-based queries
                $table->index(['sender', 'status'], 'idx_sender_status');
            } catch (\Exception $e) {
                // Index might already exist, ignore
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('nala_chat_conversations', function (Blueprint $table) {
            try {
                $table->dropIndex('idx_user_status_last_message');
            } catch (\Exception $e) {
                // Index might not exist, ignore
            }
            try {
                $table->dropIndex('idx_status_created');
            } catch (\Exception $e) {
                // Index might not exist, ignore
            }
        });

        Schema::table('nala_chat_messages', function (Blueprint $table) {
            try {
                $table->dropIndex('idx_conversation_status_created');
            } catch (\Exception $e) {
                // Index might not exist, ignore
            }
            try {
                $table->dropIndex('idx_message_status_created');
            } catch (\Exception $e) {
                // Index might not exist, ignore
            }
            try {
                $table->dropIndex('idx_sender_status');
            } catch (\Exception $e) {
                // Index might not exist, ignore
            }
        });
    }
};
