@extends('layouts.tutor')

@section('title', 'Publikasikan Kursus - ' . $course->title)

@section('content')
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <a href="{{ route('tutor.curriculum.index', $course) }}" 
                   class="text-gray-600 hover:text-gray-900 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Publikasikan Kursus</h1>
                    <p class="text-gray-600 mt-1">{{ $course->title }}</p>
                </div>
            </div>
        </div>

        <!-- Course Overview Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="p-6">
                <div class="flex items-start space-x-4">
                    @if($course->thumbnail)
                        <img src="{{ asset('storage/' . $course->thumbnail) }}" 
                             alt="{{ $course->title }}" 
                             class="w-24 h-16 object-cover rounded-lg">
                    @else
                        <div class="w-24 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    @endif
                    <div class="flex-1">
                        <h2 class="text-xl font-semibold text-gray-900 mb-2">{{ $course->title }}</h2>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                            <div>
                                <span class="font-medium">{{ $totalChapters }}</span> Bab
                            </div>
                            <div>
                                <span class="font-medium">{{ $totalLessons }}</span> Materi
                            </div>
                            <div>
                                <span class="font-medium">{{ $publishedLessons }}</span> Dipublikasi
                            </div>
                            <div>
                                <span class="font-medium">{{ floor($totalDuration / 60) }}j {{ $totalDuration % 60 }}m</span> Durasi
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Validation Checklist -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Persyaratan Publikasi</h3>
                <div class="space-y-3">
                    @foreach([
                        'has_chapters' => 'Memiliki minimal 1 bab',
                        'has_lessons' => 'Memiliki minimal 1 materi pembelajaran',
                        'has_published_content' => 'Memiliki minimal 1 materi yang dipublikasikan',
                        'has_thumbnail' => 'Memiliki thumbnail kursus',
                        'has_description' => 'Memiliki deskripsi kursus',
                        'has_learning_outcomes' => 'Memiliki tujuan pembelajaran'
                    ] as $key => $label)
                        <div class="flex items-center space-x-3">
                            @if($validationChecks[$key])
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-gray-900">{{ $label }}</span>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-red-600">{{ $label }}</span>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Publication Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Aksi Publikasi</h3>
                
                @if($canPublish)
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h4 class="text-green-800 font-medium">Siap untuk Dipublikasikan</h4>
                                <p class="text-green-700 text-sm mt-1">Kursus Anda memenuhi semua persyaratan dan siap untuk dipublikasikan. Setelah dipublikasikan, siswa dapat mendaftar dan mengakses kursus Anda.</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-3">
                        <form id="publishForm" action="{{ route('tutor.courses.publish', $course) }}" method="POST" class="flex-1">
                            @csrf
                            <button type="button"
                                    onclick="openPublishConfirmModal()"
                                    class="w-full btn bg-green-600 hover:bg-green-700 text-white">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Publikasikan Kursus
                            </button>
                        </form>

                        <a href="{{ route('tutor.curriculum.index', $course) }}"
                           class="btn bg-gray-600 hover:bg-gray-700 text-white text-center">
                            Kembali ke Kurikulum
                        </a>
                    </div>
                @else
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h4 class="text-red-800 font-medium">Belum Siap untuk Dipublikasikan</h4>
                                <p class="text-red-700 text-sm mt-1">Kursus Anda belum memenuhi semua persyaratan publikasi. Silakan lengkapi persyaratan yang belum terpenuhi terlebih dahulu.</p>
                            </div>
                        </div>
                    </div>

                    <a href="{{ route('tutor.curriculum.index', $course) }}" 
                       class="btn bg-teal-600 hover:bg-teal-700 text-white">
                        Lengkapi Persyaratan
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

    <!-- Publish Confirmation Modal -->
    <div id="publishConfirmModal" class="modal-backdrop fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50" style="display: none;">
        <div class="relative mx-auto p-0 border-0 w-full max-w-md">
            <div class="modal-content bg-white rounded-xl shadow-2xl overflow-hidden">
                <!-- Modal Header -->
                <div class="bg-green-500 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-white">Konfirmasi Publikasi</h3>
                        </div>
                        <button type="button" onclick="closePublishConfirmModal()" class="text-white hover:text-gray-200 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Body -->
                <div class="px-6 py-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 0 1 15 0v5z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Publikasikan Kursus</h4>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Apakah Anda yakin ingin menerbitkan kursus ini? Setelah dipublikasikan, siswa dapat mendaftar di kursus Anda dan mengakses semua materi pembelajaran.
                        </p>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row gap-3">
                    <button type="button"
                            onclick="confirmPublish()"
                            id="confirmPublishBtn"
                            class="flex-1 px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors duration-200 tutor-touch-friendly order-1 flex items-center justify-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Ya, Publikasikan
                    </button>
                    <button type="button"
                            onclick="closePublishConfirmModal()"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200 tutor-touch-friendly order-2">
                        Batal
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Publish confirmation modal functions
    function openPublishConfirmModal() {
        const modal = document.getElementById('publishConfirmModal');
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
        modal.classList.remove('hidden');
    }

    function closePublishConfirmModal() {
        const modal = document.getElementById('publishConfirmModal');
        modal.style.display = 'none';
        modal.classList.add('hidden');
    }

    function confirmPublish() {
        const confirmBtn = document.getElementById('confirmPublishBtn');

        // Show loading state
        confirmBtn.innerHTML = `
            <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Menerbitkan...
        `;
        confirmBtn.disabled = true;

        // Submit the form
        document.getElementById('publishForm').submit();
    }

    // Close modal when clicking outside
    document.addEventListener('DOMContentLoaded', function() {
        const publishConfirmModal = document.getElementById('publishConfirmModal');
        if (publishConfirmModal) {
            publishConfirmModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closePublishConfirmModal();
                }
            });
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePublishConfirmModal();
            }
        });
    });
    </script>
@endsection
