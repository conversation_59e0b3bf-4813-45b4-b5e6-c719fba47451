<!-- <PERSON>la AI Assistant Component for <PERSON><PERSON><PERSON><PERSON><PERSON> -->
<style>
/* <PERSON><PERSON>t Component - Light Theme, Bottom-Right Positioned */

/* Container positioned in bottom-right corner - Compact size */
.nala-chat-container {
    position: fixed;
    bottom: 90px;
    right: 20px;
    z-index: 999999 !important;
    width: 320px;
    max-width: calc(100vw - 40px);
    height: 420px;
    max-height: calc(100vh - 120px);
    pointer-events: none;
}

.nala-chat-container * {
    pointer-events: auto;
}

/* Chat toggle button - Enhanced for dev tools compatibility */
.nala-chat-toggle {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    border-radius: 50%;
    border: none;
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
    pointer-events: auto !important;
    isolation: isolate;
}

.nala-chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 107, 53, 0.4);
}

.nala-chat-toggle:active {
    transform: scale(0.95);
}

/* Avatar in toggle button - Smaller for compact design */
.nala-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.nala-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main chat window - Light theme, compact size */
.nala-chat-window {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    overflow: hidden;
    transform: translateY(100%) scale(0.8);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #E5E7EB;
    pointer-events: auto;
}

.nala-chat-window.open {
    transform: translateY(0) scale(1);
    opacity: 1;
}

/* Chat header - Light theme, compact */
.nala-chat-header {
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px 12px 0 0;
}

.nala-chat-header-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nala-chat-header-avatar {
    width: 36px;
    height: 36px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.nala-chat-header-text h3 {
    color: white;
    font-size: 15px;
    font-weight: 600;
    margin: 0;
}

.nala-chat-header-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 11px;
    margin: 0;
}

.nala-chat-header-actions {
    display: flex;
    align-items: center;
    gap: 6px;
}

.nala-header-btn {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    border-radius: 50%;
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: white;
    pointer-events: auto;
}

.nala-header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.nala-chat-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
    color: white;
    pointer-events: auto;
}

.nala-chat-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Messages area - Light theme, compact */
.nala-chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background: #F9FAFB;
    display: flex;
    flex-direction: column;
    gap: 12px;
    -webkit-overflow-scrolling: touch;
}

.nala-chat-messages::-webkit-scrollbar {
    width: 4px;
}

.nala-chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.nala-chat-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
}

/* Message bubbles - Compact design */
.nala-message {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    animation: slideUp 0.3s ease;
}

.nala-message.user {
    flex-direction: row-reverse;
}

.nala-message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    flex-shrink: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nala-message-avatar.ai {
    background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.nala-message-avatar.user {
    background: #6B7280;
}

.nala-message-content {
    background: white;
    color: #374151;
    padding: 10px 14px;
    border-radius: 14px;
    max-width: 75%;
    font-size: 13px;
    line-height: 1.4;
    border: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nala-message.user .nala-message-content {
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    color: white;
    border: none;
    border-radius: 14px 14px 4px 14px;
}

.nala-message.ai .nala-message-content {
    border-radius: 14px 14px 14px 4px;
}

/* Input area - Light theme, compact */
.nala-chat-input {
    padding: 12px 16px;
    background: white;
    border-top: 1px solid #E5E7EB;
    border-radius: 0 0 12px 12px;
}

.nala-input-container {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.nala-input-wrapper {
    flex: 1;
    position: relative;
}

.nala-input {
    width: 100%;
    background: #F9FAFB;
    border: 1px solid #D1D5DB;
    border-radius: 18px;
    padding: 10px 14px;
    color: #374151;
    font-size: 13px;
    resize: none;
    outline: none;
    transition: border-color 0.2s;
    min-height: 40px;
    max-height: 80px;
}

.nala-input:focus {
    border-color: #FF6B35;
    background: white;
}

.nala-input::placeholder {
    color: #9CA3AF;
}

.nala-send-button {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: white;
    flex-shrink: 0;
}

.nala-send-button:hover:not(:disabled) {
    transform: scale(1.05);
}

.nala-send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Quick actions - Compact */
.nala-quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
}

.nala-quick-action {
    background: rgba(255, 107, 53, 0.1);
    border: 1px solid rgba(255, 107, 53, 0.3);
    color: #FF6B35;
    padding: 5px 10px;
    border-radius: 14px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 28px;
    display: flex;
    align-items: center;
}

.nala-quick-action:hover {
    background: rgba(255, 107, 53, 0.2);
    border-color: rgba(255, 107, 53, 0.5);
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Tablet optimizations - Enhanced header design for 768px */
@media (min-width: 768px) and (max-width: 1024px) {
    .nala-chat-container {
        width: 380px;
        height: 480px;
    }

    .nala-chat-header {
        padding: 16px 20px;
    }

    .nala-chat-header-text h3 {
        font-size: 17px;
        font-weight: 700;
        letter-spacing: -0.025em;
    }

    .nala-chat-header-text p {
        font-size: 13px;
        font-weight: 500;
        margin-top: 2px;
    }

    .nala-chat-header-avatar {
        width: 40px;
        height: 40px;
    }

    .nala-chat-close {
        width: 32px;
        height: 32px;
    }

    .nala-chat-close svg {
        width: 18px;
        height: 18px;
    }

    .nala-chat-messages {
        padding: 20px;
    }

    .nala-message-content {
        font-size: 14px;
        padding: 12px 16px;
    }

    .nala-input {
        font-size: 14px;
        padding: 12px 16px;
    }

    .nala-quick-action {
        font-size: 12px;
        padding: 6px 12px;
        min-height: 32px;
    }
}

/* Mobile optimizations - Compact design */
@media (max-width: 480px) {
    .nala-chat-container {
        width: calc(100vw - 20px);
        height: calc(100vh - 120px);
        right: 10px;
        bottom: 80px;
    }

    .nala-chat-toggle {
        width: 52px;
        height: 52px;
        right: 15px;
        bottom: 15px;
    }

    .nala-avatar {
        width: 28px;
        height: 28px;
    }

    .nala-chat-messages {
        padding: 12px;
        gap: 10px;
    }

    .nala-message-content {
        max-width: 85%;
        font-size: 12px;
        padding: 8px 12px;
    }

    .nala-chat-input {
        padding: 10px 12px;
    }

    .nala-input {
        font-size: 12px;
        padding: 8px 12px;
        min-height: 36px;
    }

    .nala-send-button {
        width: 36px;
        height: 36px;
    }

    .nala-quick-action {
        font-size: 10px;
        padding: 4px 8px;
        min-height: 24px;
    }
}

/* Hidden state */
.nala-hidden {
    display: none !important;
}

/* Prevent body scroll when chat is open */
body.nala-chat-open {
    overflow: hidden;
}

@media (min-width: 769px) {
    body.nala-chat-open {
        overflow: auto;
    }
}

/* Typing animation */
@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>

<!-- Chat Toggle Button -->
<button id="nala-chat-toggle" class="nala-chat-toggle">
    <div class="nala-avatar">
        <img src="/images/nala.png" alt="Nala AI">
    </div>
</button>

<!-- Main Chat Container -->
<div id="nala-chat-container" class="nala-chat-container nala-hidden">
    <div id="nala-chat-window" class="nala-chat-window">
        <!-- Chat Header -->
        <div class="nala-chat-header">
            <div class="nala-chat-header-info">
                <div class="nala-chat-header-avatar">
                    <img src="/images/nala.png" alt="Nala AI">
                </div>
                <div class="nala-chat-header-text">
                    <h3>Nala</h3>
                    <p>Ngambiskuy Advance Learning Assistance</p>
                </div>
            </div>
            <div class="nala-chat-header-actions">
                <?php if(auth()->guard()->check()): ?>
                    <!-- Chat Management Buttons for Authenticated Users -->
                    <button id="nala-new-conversation" class="nala-header-btn" title="Percakapan Baru">
                        <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                    </button>
                    <button id="nala-clear-history" class="nala-header-btn" title="Hapus Riwayat">
                        <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                <?php endif; ?>
                <button id="nala-chat-close" class="nala-chat-close">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Messages Area -->
        <div id="nala-messages" class="nala-chat-messages">
            <!-- Welcome Message -->
            <div id="nala-welcome-message" class="nala-message ai">
                <div class="nala-message-avatar ai">
                    <img src="/images/nala.png" alt="Nala AI">
                </div>
                <div class="nala-message-content">
                    <?php if(auth()->guard()->check()): ?>
                        Halo <?php echo e(auth()->user()->name); ?>! 👋 Saya Nala, asisten AI Ngambiskuy. Saya siap membantu perjalanan belajar Anda. Ada yang bisa saya bantu hari ini?
                    <?php else: ?>
                        Hi there! 👋 I'm Nala, your AI learning companion. How can I help you today?
                    <?php endif; ?>
                </div>
            </div>

            <!-- Login Prompt for Unauthenticated Users -->
            <?php if(auth()->guard()->guest()): ?>
                <div id="nala-login-prompt" class="nala-message ai">
                    <div class="nala-message-avatar ai">
                        <img src="/images/nala.png" alt="Nala AI">
                    </div>
                    <div class="nala-message-content">
                        Untuk pengalaman terbaik, silakan login untuk menyimpan percakapan kita!
                        <div style="margin-top: 12px; display: flex; gap: 8px;">
                            <a href="<?php echo e(route('login')); ?>" style="background: rgba(255, 107, 53, 0.2); color: #FF6B35; padding: 6px 12px; border-radius: 12px; text-decoration: none; font-size: 12px;">Masuk</a>
                            <a href="<?php echo e(route('register')); ?>" style="background: rgba(255, 107, 53, 0.1); color: #FF6B35; padding: 6px 12px; border-radius: 12px; text-decoration: none; font-size: 12px;">Daftar</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Input Area -->
        <div class="nala-chat-input">
            <div class="nala-input-container">
                <div class="nala-input-wrapper">
                    <textarea id="nala-input"
                        class="nala-input"
                        placeholder="Ketik pesan singkat..."
                        maxlength="500"
                        rows="1"></textarea>
                </div>
                <button id="nala-send-message" class="nala-send-button" disabled>
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>

            <!-- Quick Actions -->
            <div id="nala-quick-actions" class="nala-quick-actions">
                <button class="nala-quick-action" data-action="course-recommendation">
                    📚 Mulai Belajar
                </button>
                <button class="nala-quick-action" data-action="career-path">
                    🚀 Kursus Populer
                </button>
                <button class="nala-quick-action" data-action="help">
                    ❓ Bantuan
                </button>
            </div>
        </div>
    </div>
</div>



<!-- Pass authentication status and user data to JavaScript -->
<script>
    window.nalaConfig = {
        isAuthenticated: <?php echo e(auth()->check() ? 'true' : 'false'); ?>,
        <?php if(auth()->guard()->check()): ?>
        user: {
            name: '<?php echo e(auth()->user()->name); ?>',
            email: '<?php echo e(auth()->user()->email); ?>',
            role: '<?php echo e(auth()->user()->getRole()); ?>'
        },
        <?php endif; ?>
        currentRoute: '<?php echo e(Route::currentRouteName()); ?>',
        currentUrl: '<?php echo e(url()->current()); ?>',
        csrfToken: '<?php echo e(csrf_token()); ?>'
    };
</script>

<!-- Simple JavaScript to make Nala work immediately -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatToggle = document.getElementById('nala-chat-toggle');
    const chatContainer = document.getElementById('nala-chat-container');
    const chatWindow = document.getElementById('nala-chat-window');
    const chatClose = document.getElementById('nala-chat-close');
    const sendButton = document.getElementById('nala-send-message');
    const inputField = document.getElementById('nala-input');

    let isOpen = false;

    // Toggle chat function with enhanced event handling
    function toggleChat(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        if (isOpen) {
            closeChat();
        } else {
            openChat();
        }
    }

    // Open chat
    function openChat() {
        isOpen = true;
        document.body.classList.add('nala-chat-open');
        chatContainer.classList.remove('nala-hidden');
        setTimeout(() => {
            chatWindow.classList.add('open');
        }, 10);
    }

    // Close chat
    function closeChat() {
        isOpen = false;
        document.body.classList.remove('nala-chat-open');
        chatWindow.classList.remove('open');
        setTimeout(() => {
            chatContainer.classList.add('nala-hidden');
        }, 300);
    }

    // Send message - Delegate to ai-chat.js implementation
    function sendMessage() {
        // Check if the NalaAIAssistant class is available (from ai-chat.js)
        if (window.nalaAIAssistant && typeof window.nalaAIAssistant.sendMessage === 'function') {
            window.nalaAIAssistant.sendMessage();
        } else {
            // Fallback for when ai-chat.js is not loaded
            console.warn('Nala AI not initialized. Please ensure ai-chat.js is loaded.');
        }
    }

    // Legacy function removed - now handled by ai-chat.js

    // Legacy function removed - now handled by ai-chat.js

    // Handle input changes
    function handleInputChange() {
        const length = inputField.value.length;
        sendButton.disabled = length === 0;
    }

    // Enhanced event listeners for better dev tools compatibility
    if (chatToggle) {
        chatToggle.addEventListener('click', toggleChat, { passive: false });
        chatToggle.addEventListener('touchstart', toggleChat, { passive: false });

        // Prevent event bubbling issues with dev tools
        chatToggle.addEventListener('mousedown', function(e) {
            e.stopPropagation();
        });

        chatToggle.addEventListener('mouseup', function(e) {
            e.stopPropagation();
        });
    }

    if (chatClose) {
        chatClose.addEventListener('click', closeChat);
    }

    if (sendButton) {
        sendButton.addEventListener('click', sendMessage);
    }

    if (inputField) {
        inputField.addEventListener('input', handleInputChange);
        inputField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }

    // Quick action buttons
    const quickActions = document.querySelectorAll('.nala-quick-action');
    quickActions.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.getAttribute('data-action') || this.textContent.trim();
            inputField.value = `Ceritakan tentang ${action}`;
            handleInputChange();
        });
    });

    // Close on outside click
    document.addEventListener('click', function(e) {
        // Check if click is on any Nala-related button
        const isNalaButton = e.target.closest('#nala-chat-toggle') ||
                            e.target.closest('.nala-course-chat-btn') ||
                            e.target.closest('.nala-quick-action');

        if (isOpen &&
            !chatContainer.contains(e.target) &&
            !isNalaButton) {
            closeChat();
        }
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isOpen) {
            closeChat();
        }
    });
});
</script>

<!-- Nala JavaScript is loaded via Vite -->
<?php $__env->startPush('scripts'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/ai-chat.js']); ?>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/components/ai-chat.blade.php ENDPATH**/ ?>