/* Tutor Dashboard Responsive Styles */
/* Mobile-first responsive design for tutor dashboard */

/* Tutor Dashboard Container */
.tutor-dashboard-container {
  max-width: 100%;
  overflow-x: hidden;
}

/* Tutor Stats Cards Responsive Enhancements */
.tutor-stats-card {
  min-height: 140px;
  transition: all 0.3s ease;
}

.tutor-stats-card:hover {
  transform: translateY(-2px);
}

/* Tutor Sidebar Enhancements */
.tutor-sidebar-link {
  min-height: 44px;
  display: flex;
  align-items: center;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.tutor-sidebar-link:hover {
  background-color: rgba(16, 185, 129, 0.1);
}

.tutor-sidebar-link.active {
  background: linear-gradient(135deg, #059669, #0d9488);
  color: white;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

/* Mobile optimizations (320px - 767px) */
@media (max-width: 767px) {
  /* Container adjustments */
  .tutor-dashboard-container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* Welcome header mobile optimizations */
  .tutor-welcome-header {
    padding: 1rem;
    text-align: center;
  }

  .tutor-welcome-header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .tutor-welcome-header p {
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  /* Header action buttons mobile */
  .tutor-header-actions {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .tutor-header-actions .btn {
    width: 100%;
    min-height: 44px;
    justify-content: center;
  }

  /* Quick stats mobile grid */
  .tutor-quick-stats-mobile {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .tutor-quick-stats-mobile > div {
    background-color: rgba(16, 185, 129, 0.05);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(16, 185, 129, 0.1);
  }

  /* Stats cards mobile layout */
  .tutor-stats-card {
    min-height: 120px;
    padding: 1rem;
  }

  .tutor-stats-card h3 {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .tutor-stats-card .stat-number {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  /* Course cards mobile layout */
  .tutor-course-card-mobile {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .tutor-course-card-mobile .course-thumbnail {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 0.75rem auto;
  }

  /* Activity items mobile */
  .tutor-activity-item-mobile {
    padding: 0.5rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* Quick actions mobile grid */
  .tutor-quick-actions-mobile {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .tutor-quick-action-item {
    min-height: 44px;
    padding: 0.75rem;
    justify-content: flex-start;
    text-align: left;
  }

  /* Mobile sidebar improvements */
  .tutor-mobile-sidebar {
    max-width: 280px;
  }

  .tutor-mobile-sidebar .tutor-sidebar-link {
    min-height: 48px;
    padding: 0.875rem;
    font-size: 0.9rem;
  }

  /* Mobile header improvements */
  .tutor-mobile-header {
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .tutor-mobile-header .hamburger-btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.5rem;
  }

  /* Course grid mobile */
  .tutor-course-grid-mobile {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Tabs mobile */
  .tutor-tabs-mobile {
    flex-direction: column;
    gap: 0;
  }

  .tutor-tabs-mobile button {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  /* Form elements mobile */
  .tutor-form-mobile input,
  .tutor-form-mobile select,
  .tutor-form-mobile textarea {
    min-height: 44px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Small mobile devices (320px - 480px) */
@media (max-width: 480px) {
  .tutor-dashboard-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Extra compact stats cards */
  .tutor-stats-card {
    min-height: 100px;
    padding: 0.75rem;
  }

  .tutor-stats-card .stat-number {
    font-size: 1.25rem;
  }

  /* Smaller touch targets for very small screens */
  .tutor-btn-mobile {
    min-height: 40px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  /* Compact course cards */
  .tutor-course-card-mobile .course-thumbnail {
    width: 2.5rem;
    height: 2.5rem;
  }

  /* Smaller sidebar */
  .tutor-mobile-sidebar {
    max-width: 260px;
  }

  /* Compact header */
  .tutor-mobile-header {
    padding: 0.5rem 0.75rem;
  }
}

/* Tablet optimizations (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Two-column layout optimizations */
  .tutor-tablet-two-col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  /* Stats cards tablet layout */
  .tutor-stats-card {
    min-height: 160px;
  }

  /* Course cards tablet layout */
  .tutor-course-card-tablet {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .tutor-course-card-tablet .course-thumbnail {
    width: 4rem;
    height: 4rem;
    flex-shrink: 0;
  }

  /* Activity section tablet */
  .tutor-activity-section-tablet {
    max-height: 400px;
    overflow-y: auto;
  }

  /* Quick actions tablet grid */
  .tutor-quick-actions-tablet {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  /* Sidebar tablet optimizations */
  .tutor-sidebar-tablet {
    width: 240px;
  }
}

/* Large tablet and small desktop (1025px - 1280px) */
@media (min-width: 1025px) and (max-width: 1280px) {
  /* Optimize for smaller desktop screens */
  .tutor-stats-card {
    min-height: 180px;
  }

  /* Compact course recommendations */
  .tutor-course-recommendations-compact {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
  /* Touch device optimizations */
  .tutor-touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }

  /* Larger tap targets */
  .tutor-stats-card a,
  .tutor-quick-action-item,
  .tutor-sidebar-link,
  .btn {
    min-height: 44px;
    padding: 0.75rem;
  }

  /* Remove hover effects on touch devices */
  .tutor-stats-card:hover {
    transform: none;
  }

  /* Enhanced focus states for accessibility */
  .tutor-stats-card:focus,
  .tutor-quick-action-item:focus,
  .tutor-sidebar-link:focus,
  .btn:focus {
    outline: 2px solid #059669;
    outline-offset: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tutor-stats-card {
    border-width: 2px;
  }

  .tutor-quick-action-item {
    border-width: 2px;
  }

  .tutor-sidebar-link {
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
  }

  .tutor-sidebar-link.active {
    border-color: #059669;
  }

  .btn {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tutor-stats-card,
  .tutor-quick-action-item,
  .tutor-sidebar-link,
  .btn {
    transition: none;
  }

  .tutor-stats-card:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .tutor-dashboard-container {
    background: white;
    color: black;
  }

  .tutor-stats-card,
  .tutor-quick-action-item {
    border: 1px solid #000;
    background: white;
  }

  .btn {
    display: none;
  }

  .tutor-mobile-sidebar,
  .tutor-mobile-header {
    display: none;
  }
}

/* Loading states */
.tutor-loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: tutor-loading 1.5s infinite;
}

@keyframes tutor-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility classes for responsive design */
.tutor-mobile-only {
  display: block;
}

.tutor-tablet-only,
.tutor-desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .tutor-mobile-only {
    display: none;
  }

  .tutor-tablet-only {
    display: block;
  }
}

@media (min-width: 1025px) {
  .tutor-tablet-only {
    display: none;
  }

  .tutor-desktop-only {
    display: block;
  }
}

/* Responsive spacing utilities */
.tutor-space-mobile {
  margin-bottom: 1rem;
}

.tutor-space-tablet {
  margin-bottom: 1.5rem;
}

.tutor-space-desktop {
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .tutor-space-mobile {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .tutor-space-mobile {
    margin-bottom: 2rem;
  }

  .tutor-space-tablet {
    margin-bottom: 2rem;
  }
}

/* Course management specific styles */
.tutor-course-management-mobile {
  padding: 0.75rem;
}

.tutor-course-management-mobile .course-actions {
  flex-direction: column;
  gap: 0.5rem;
}

.tutor-course-management-mobile .course-actions .btn {
  width: 100%;
  justify-content: center;
}

/* Analytics responsive styles */
.tutor-analytics-mobile {
  padding: 0.75rem;
}

.tutor-analytics-mobile .chart-container {
  height: 200px;
  margin-bottom: 1rem;
}

/* Earnings responsive styles */
.tutor-earnings-mobile {
  padding: 0.75rem;
}

.tutor-earnings-mobile .earnings-summary {
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

/* Students management responsive styles */
.tutor-students-mobile {
  padding: 0.75rem;
}

.tutor-students-mobile .student-card {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.tutor-students-mobile .student-actions {
  flex-direction: column;
  gap: 0.5rem;
}

/* Blog management responsive styles */
.tutor-blog-mobile {
  padding: 0.75rem;
}

.tutor-blog-mobile .blog-card {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.tutor-blog-mobile .blog-actions {
  flex-direction: column;
  gap: 0.5rem;
}

/* Exam management responsive styles */
.tutor-exam-mobile {
  padding: 0.75rem;
}

.tutor-exam-mobile .exam-card {
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.tutor-exam-mobile .exam-actions {
  flex-direction: column;
  gap: 0.5rem;
}
