<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;

class CourseEnrollmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all student users (users who only have the USER role)
        $students = User::whereDoesntHave('roles', function($query) {
            $query->whereIn('name', [Role::TUTOR, Role::ADMIN, Role::SUPERADMIN]);
        })->get();

        // Get all courses (both free and paid)
        $allCourses = Course::all();

        // Get specific tutors for targeted enrollments
        $sariDewi = User::where('email', '<EMAIL>')->first();
        $ahmadRahman = User::where('email', '<EMAIL>')->first();
        $superAdmin = User::where('email', '<EMAIL>')->first();

        if ($students->count() > 0 && $allCourses->count() > 0) {
            // Create enrollments for <PERSON><PERSON>'s courses (she should have students)
            $sariCourses = Course::where('tutor_id', $sariDewi->id)->get();
            foreach ($sariCourses as $course) {
                // Enroll multiple students in each of Sari's courses
                $studentsToEnroll = $students->random(min(5, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    $status = collect(['active', 'completed'])->random();
                    $enrolledAt = now()->subDays(rand(1, 180)); // Random enrollment dates in last 6 months
                    $completedAt = null;

                    // If status is completed, set completion date after enrollment
                    if ($status === 'completed') {
                        $completedAt = $enrolledAt->copy()->addDays(rand(1, 30));
                    }

                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => $status,
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => $enrolledAt,
                        'completed_at' => $completedAt,
                    ]);
                }
            }

            // Create enrollments for Ahmad's courses
            $ahmadCourses = Course::where('tutor_id', $ahmadRahman->id)->get();
            foreach ($ahmadCourses as $course) {
                $studentsToEnroll = $students->random(min(3, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    $status = collect(['active', 'completed'])->random();
                    $enrolledAt = now()->subDays(rand(1, 150));
                    $completedAt = null;

                    if ($status === 'completed') {
                        $completedAt = $enrolledAt->copy()->addDays(rand(1, 25));
                    }

                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => $status,
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => $enrolledAt,
                        'completed_at' => $completedAt,
                    ]);
                }
            }

            // Create enrollments for SuperAdmin's courses
            $superAdminCourses = Course::where('tutor_id', $superAdmin->id)->get();
            foreach ($superAdminCourses as $course) {
                $studentsToEnroll = $students->random(min(4, $students->count()));

                foreach ($studentsToEnroll as $student) {
                    $status = collect(['active', 'completed'])->random();
                    $enrolledAt = now()->subDays(rand(1, 120));
                    $completedAt = null;

                    if ($status === 'completed') {
                        $completedAt = $enrolledAt->copy()->addDays(rand(1, 20));
                    }

                    CourseEnrollment::create([
                        'user_id' => $student->id,
                        'course_id' => $course->id,
                        'status' => $status,
                        'amount_paid' => $course->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $student->id . '_' . $course->id,
                        'enrolled_at' => $enrolledAt,
                        'completed_at' => $completedAt,
                    ]);
                }
            }

            // Create some additional random enrollments for variety
            for ($i = 0; $i < 10; $i++) {
                $randomStudent = $students->random();
                $randomCourse = $allCourses->random();

                // Check if enrollment already exists
                $existingEnrollment = CourseEnrollment::where('user_id', $randomStudent->id)
                    ->where('course_id', $randomCourse->id)
                    ->first();

                if (!$existingEnrollment) {
                    $status = collect(['active', 'completed'])->random();
                    $enrolledAt = now()->subDays(rand(1, 180));
                    $completedAt = null;

                    if ($status === 'completed') {
                        $completedAt = $enrolledAt->copy()->addDays(rand(1, 35));
                    }

                    CourseEnrollment::create([
                        'user_id' => $randomStudent->id,
                        'course_id' => $randomCourse->id,
                        'status' => $status,
                        'amount_paid' => $randomCourse->price,
                        'payment_method' => collect(['credit_card', 'bank_transfer', 'e_wallet'])->random(),
                        'payment_reference' => 'PAY_' . time() . '_' . $randomStudent->id . '_' . $randomCourse->id,
                        'enrolled_at' => $enrolledAt,
                        'completed_at' => $completedAt,
                    ]);
                }
            }
        }
    }
}
