<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MembershipPlan;
use App\Models\UserMembership;

class DebugPricingPage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:pricing-page {email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug pricing page logic for a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found");
            return;
        }

        $this->info("=== Debugging Membership Issues for {$user->email} ===");

        // Check all user memberships
        $this->info("\n=== All User Memberships ===");
        $memberships = UserMembership::where('user_id', $user->id)
            ->with('membershipPlan')
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($memberships as $membership) {
            $this->line("ID: {$membership->id}");
            $this->line("Plan: {$membership->membershipPlan->name} ({$membership->membershipPlan->slug})");
            $this->line("Status: {$membership->status}");
            $this->line("Starts: {$membership->starts_at}");
            $this->line("Expires: {$membership->expires_at}");
            $this->line("Created: {$membership->created_at}");
            $this->line("Is Active: " . ($membership->isActive() ? 'YES' : 'NO'));
            $this->line("Prompts Allocated: {$membership->nala_prompts_allocated}");
            $this->line("---");
        }

        // Check active membership relationship
        $this->info("\n=== Active Membership Query ===");
        $currentMembership = $user->activeMembership;
        $currentPlanId = null;
        $userHasFreeMembership = false;

        if ($currentMembership) {
            $currentMembership->load('membershipPlan');
            $currentPlanId = $currentMembership->membership_plan_id;
            $userHasFreeMembership = $currentMembership->membershipPlan->is_free;

            $this->info("Active Membership Found:");
            $this->line("- Plan: {$currentMembership->membershipPlan->name}");
            $this->line("- Plan ID: {$currentMembership->membership_plan_id}");
            $this->line("- Status: {$currentMembership->status}");
            $this->line("- Is Free: " . ($userHasFreeMembership ? 'Yes' : 'No'));
            $this->line("- Prompts: {$currentMembership->nala_prompts_allocated}");
        } else {
            $this->warn("No active membership found");
        }

        // Check user's current_membership_id field
        $this->info("\n=== User Current Membership Field ===");
        $this->line("current_membership_id: " . ($user->current_membership_id ?? 'NULL'));

        // Get membership plans
        $membershipPlans = MembershipPlan::active()
            ->orderBy('type')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('type');

        $this->info("\n=== Plan Comparison Logic ===");

        foreach ($membershipPlans['individual'] ?? [] as $plan) {
            $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
            $isFreePlanForAuthenticatedUser = $plan->is_free && $userHasFreeMembership;

            $this->line("\n--- {$plan->name} Plan ---");
            $this->line("Plan ID: {$plan->id}");
            $this->line("Plan Slug: {$plan->slug}");
            $this->line("Is Current Plan: " . ($isCurrentPlan ? 'YES' : 'NO'));
            $this->line("Is Free Plan for Auth User: " . ($isFreePlanForAuthenticatedUser ? 'YES' : 'NO'));

            if ($isCurrentPlan || $isFreePlanForAuthenticatedUser) {
                $this->info("→ Should show: CURRENT PLAN button");
            } else {
                $this->line("→ Should show: Get {$plan->name} button");
            }
        }

        // Check Nala prompt tracking
        $this->info("\n=== Nala Prompt Tracking ===");
        $promptService = new \App\Services\NalaPromptTrackingService();
        $membershipLevel = $promptService->getUserMembershipLevel($user);
        $remainingPrompts = $promptService->getRemainingPrompts($user);

        $this->line("Membership Level: {$membershipLevel}");
        $this->line("Remaining Prompts: {$remainingPrompts}");
    }
}
