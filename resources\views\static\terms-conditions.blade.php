@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> & Ketentuan - Ngambiskuy')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-12">
                <div class="mx-auto h-16 w-16 bg-gradient-to-br from-primary to-red-500 rounded-lg flex items-center justify-center mb-6 shadow-lg">
                    <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                </div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Syarat & Ketentuan</h1>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Syarat dan ketentuan penggunaan platform pembelajaran Ngambiskuy
                </p>
                <p class="text-sm text-gray-500 mt-4">
                    Terakhir diperbarui: {{ date('d F Y') }}
                </p>
            </div>

            <!-- Content -->
            <div class="bg-white rounded-xl shadow-lg p-8 lg:p-12">
                <div class="prose prose-lg max-w-none">
                    <div class="bg-primary/10 border border-primary/20 rounded-lg p-6 mb-8">
                        <h3 class="text-primary font-semibold mb-2">Selamat Datang di Ngambiskuy</h3>
                        <p class="text-gray-700 text-sm mb-0">
                            Dengan menggunakan platform kami, Anda menyetujui syarat dan ketentuan berikut. Mohon baca dengan seksama sebelum menggunakan layanan Ngambiskuy.
                        </p>
                    </div>

                    <h2 class="text-primary border-b border-primary/20 pb-2">1. Penerimaan Syarat</h2>
                    <p>
                        Dengan mengakses dan menggunakan platform Ngambiskuy ("Platform"), Anda menyetujui untuk terikat oleh syarat dan ketentuan ini ("Syarat"). Jika Anda tidak menyetujui syarat ini, mohon untuk tidak menggunakan Platform kami.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">2. Tentang Ngambiskuy</h2>
                    <p>
                        Ngambiskuy adalah platform pembelajaran online bertenaga AI yang menyediakan kursus, ujian, blog edukasi, dan berbagai fitur pembelajaran untuk membantu pengembangan karir di bidang teknologi. Platform ini dioperasikan oleh PT Ngambiskuy Teknologi Indonesia dan dilengkapi dengan asisten AI bernama Nala (Ngambiskuy Advance Learning Assistance).
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">3. Akun Pengguna</h2>
                    <h3 class="text-gray-800">3.1 Pendaftaran</h3>
                    <ul class="space-y-2">
                        <li>Anda harus berusia minimal 17 tahun untuk mendaftar</li>
                        <li>Informasi yang Anda berikan harus akurat dan terkini</li>
                        <li>Anda bertanggung jawab menjaga keamanan akun dan password</li>
                        <li>Satu orang hanya diperbolehkan memiliki satu akun</li>
                        <li>Akun yang tidak aktif selama 2 tahun dapat dihapus</li>
                    </ul>

                    <h3 class="text-gray-800">3.2 Tanggung Jawab Pengguna</h3>
                    <ul class="space-y-2">
                        <li>Menggunakan Platform sesuai dengan tujuan pembelajaran</li>
                        <li>Tidak menyalahgunakan fitur atau konten Platform</li>
                        <li>Tidak membagikan akses akun kepada orang lain</li>
                        <li>Melaporkan aktivitas mencurigakan atau pelanggaran</li>
                        <li>Menjaga etika dalam interaksi dengan pengguna lain</li>
                    </ul>

                    <h2 class="text-primary border-b border-primary/20 pb-2">4. Layanan dan Konten</h2>
                    <h3 class="text-gray-800">4.1 Akses Konten</h3>
                    <p>
                        Platform menyediakan akses ke berbagai materi pembelajaran, termasuk video, artikel, ujian, blog edukasi, dan fitur AI Nala. Beberapa konten mungkin memerlukan pembayaran atau keanggotaan khusus (Basic, Standard, atau Pro).
                    </p>

                    <h3 class="text-gray-800">4.2 Hak Kekayaan Intelektual</h3>
                    <p>
                        Semua konten di Platform, termasuk teks, gambar, video, kode, dan materi pembelajaran, dilindungi oleh hak cipta dan merupakan milik Ngambiskuy atau pembuat konten yang bersangkutan. Dilarang menyalin, mendistribusikan, atau menggunakan konten untuk tujuan komersial tanpa izin tertulis.
                    </p>

                    <h3 class="text-gray-800">4.3 Konten Buatan Pengguna</h3>
                    <p>
                        Tutor dapat membuat dan mengunggah konten pembelajaran. Dengan mengunggah konten, tutor memberikan lisensi kepada Ngambiskuy untuk menggunakan, menampilkan, dan mendistribusikan konten tersebut di Platform.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">5. Keanggotaan dan Pembayaran</h2>
                    <h3 class="text-gray-800">5.1 Jenis Keanggotaan</h3>
                    <ul class="space-y-2">
                        <li><strong>Free:</strong> Akses terbatas ke konten dasar</li>
                        <li><strong>Basic:</strong> Akses ke fitur premium dengan batasan tertentu</li>
                        <li><strong>Standard:</strong> Akses penuh ke sebagian besar fitur</li>
                        <li><strong>Pro:</strong> Akses penuh ke semua fitur dan prioritas support</li>
                    </ul>

                    <h3 class="text-gray-800">5.2 Harga dan Pembayaran</h3>
                    <ul class="space-y-2">
                        <li>Harga layanan dapat berubah dengan pemberitahuan 30 hari sebelumnya</li>
                        <li>Pembayaran dilakukan melalui metode yang tersedia di Platform</li>
                        <li>Semua pembayaran dalam Rupiah Indonesia (IDR)</li>
                        <li>Pembayaran kursus memberikan akses seumur hidup</li>
                        <li>Keanggotaan berlaku sesuai periode yang dipilih</li>
                    </ul>

                    <h3 class="text-gray-800">5.3 Kebijakan Refund</h3>
                    <p>
                        Refund dapat diberikan dalam 7 hari pertama setelah pembelian jika konten belum diakses lebih dari 20%. Untuk keanggotaan, refund dapat diberikan dalam 3 hari pertama. Silakan hubungi tim support untuk informasi lebih lanjut.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">6. Penggunaan AI dan Data</h2>
                    <h3 class="text-gray-800">6.1 Fitur AI Nala</h3>
                    <p>
                        Platform menggunakan teknologi AI melalui asisten Nala untuk memberikan rekomendasi pembelajaran, analisis karir, dan bantuan dalam pembuatan konten. Penggunaan AI dibatasi berdasarkan jenis keanggotaan dengan kuota harian yang berbeda.
                    </p>

                    <h3 class="text-gray-800">6.2 Pemrosesan Data</h3>
                    <p>
                        Data yang Anda berikan akan diproses sesuai dengan Kebijakan Privasi kami. AI kami dapat menganalisis pola pembelajaran untuk memberikan rekomendasi yang lebih baik, namun data pribadi Anda tetap terlindungi.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">7. Tutor dan Pengajar</h2>
                    <h3 class="text-gray-800">7.1 Pendaftaran Tutor</h3>
                    <p>
                        Pengguna dapat mendaftar sebagai tutor untuk membuat dan menjual kursus. Tutor harus menyediakan informasi yang akurat dan konten berkualitas sesuai standar Platform.
                    </p>

                    <h3 class="text-gray-800">7.2 Pembagian Pendapatan</h3>
                    <p>
                        Tutor akan menerima persentase dari penjualan kursus setelah dikurangi biaya platform dan pajak yang berlaku. Detail pembagian akan dijelaskan dalam perjanjian tutor terpisah.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">8. Larangan Penggunaan</h2>
                    <p>Anda dilarang untuk:</p>
                    <ul class="space-y-2">
                        <li>Menggunakan Platform untuk tujuan ilegal atau melanggar hukum</li>
                        <li>Mengganggu atau merusak sistem Platform</li>
                        <li>Menyalin, mendistribusikan, atau memodifikasi konten tanpa izin</li>
                        <li>Menggunakan bot atau script otomatis untuk mengakses Platform</li>
                        <li>Melakukan spam atau aktivitas yang mengganggu pengguna lain</li>
                        <li>Menyalahgunakan fitur AI untuk tujuan yang tidak sesuai</li>
                        <li>Membuat akun palsu atau menyamar sebagai orang lain</li>
                        <li>Mengunggah konten yang mengandung virus atau malware</li>
                    </ul>

                    <h2 class="text-primary border-b border-primary/20 pb-2">9. Penghentian Layanan</h2>
                    <p>
                        Kami berhak menghentikan atau menangguhkan akses Anda ke Platform jika terjadi pelanggaran terhadap syarat dan ketentuan ini. Penghentian dapat bersifat sementara atau permanen tergantung tingkat pelanggaran.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">10. Batasan Tanggung Jawab</h2>
                    <p>
                        Ngambiskuy tidak bertanggung jawab atas kerugian langsung atau tidak langsung yang timbul dari penggunaan Platform, termasuk namun tidak terbatas pada kehilangan data, gangguan bisnis, atau kerugian finansial, kecuali yang diwajibkan oleh hukum yang berlaku di Indonesia.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">11. Ketersediaan Layanan</h2>
                    <p>
                        Kami berusaha menjaga Platform tersedia 24/7, namun tidak dapat menjamin ketersediaan tanpa gangguan. Pemeliharaan rutin dan perbaikan sistem dapat menyebabkan gangguan sementara yang akan diberitahukan sebelumnya jika memungkinkan.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">12. Perubahan Syarat</h2>
                    <p>
                        Kami dapat mengubah syarat dan ketentuan ini sewaktu-waktu. Perubahan material akan diberitahukan melalui Platform, email, atau notifikasi dalam aplikasi minimal 30 hari sebelum berlaku. Penggunaan berkelanjutan setelah perubahan dianggap sebagai persetujuan terhadap syarat yang baru.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">13. Hukum yang Berlaku</h2>
                    <p>
                        Syarat dan ketentuan ini diatur oleh hukum Republik Indonesia. Setiap sengketa akan diselesaikan melalui musyawarah terlebih dahulu, dan jika tidak tercapai kesepakatan, akan diselesaikan melalui pengadilan yang berwenang di Jakarta Pusat.
                    </p>

                    <h2 class="text-primary border-b border-primary/20 pb-2">14. Kontak</h2>
                    <p>
                        Jika Anda memiliki pertanyaan tentang syarat dan ketentuan ini, silakan hubungi kami di:
                    </p>
                    <ul class="space-y-2">
                        <li><strong>Email Legal:</strong> <EMAIL></li>
                        <li><strong>Customer Support:</strong> <EMAIL></li>
                        <li><strong>Alamat:</strong> Jakarta, Indonesia</li>
                        <li><strong>Jam Operasional:</strong> Senin - Jumat, 09:00 - 17:00 WIB</li>
                    </ul>

                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mt-8">
                        <h3 class="text-orange-900 font-semibold mb-2">Penting untuk Diingat</h3>
                        <p class="text-orange-800 text-sm">
                            Dengan menggunakan Ngambiskuy, Anda menyetujui semua syarat dan ketentuan di atas. Pastikan Anda memahami hak dan kewajiban Anda sebagai pengguna platform pembelajaran bertenaga AI ini.
                        </p>
                    </div>
                </div>

                <!-- Back Button -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <div class="flex justify-center space-x-4">
                        <button onclick="window.history.back()"
                            class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Kembali
                        </button>
                        <a href="{{ route('home') }}"
                            class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Beranda
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
