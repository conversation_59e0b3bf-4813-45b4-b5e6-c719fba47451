# Nala AI Chat History Testing Guide

## Overview
This guide provides comprehensive testing procedures for the Nala AI chat history persistence functionality.

## Prerequisites
1. Laravel application running with database migrations applied
2. Test users with different membership levels (free, basic, standard, pro)
3. Browser with developer tools for debugging

## Test Scenarios

### 1. Basic Chat History Functionality

#### Test 1.1: First-time User Experience
**Steps:**
1. Register a new user account
2. Open Nala AI chat
3. Send a message: "Hello Nala"
4. Verify AI responds appropriately
5. Close and reopen chat
6. Verify conversation history is loaded

**Expected Results:**
- Welcome message appears for new users
- First message creates a new conversation
- History loads correctly on chat reopen
- History indicator shows "Riwayat percakapan dimuat (X pesan)"

#### Test 1.2: Session Continuity
**Steps:**
1. <PERSON><PERSON> as existing user with chat history
2. Open Nala chat
3. Send message: "What did we discuss before?"
4. Verify AI acknowledges previous conversation context
5. Send follow-up questions referencing earlier topics

**Expected Results:**
- Previous chat history loads automatically
- AI responses show awareness of conversation context
- Follow-up questions receive contextually relevant answers

### 2. Chat Management Features

#### Test 2.1: New Conversation Button
**Steps:**
1. Login and open chat with existing history
2. Click "New Conversation" button (+ icon in header)
3. Verify chat UI clears but history remains in database
4. Send a new message
5. Verify new conversation is created

**Expected Results:**
- Chat UI clears when starting new conversation
- Previous history remains accessible
- New conversation gets unique ID
- AI treats it as fresh conversation

#### Test 2.2: Clear History Button
**Steps:**
1. Login with existing chat history
2. Click "Clear History" button (trash icon in header)
3. Confirm deletion in popup
4. Verify all history is cleared
5. Send new message to verify fresh start

**Expected Results:**
- Confirmation dialog appears
- All chat history is permanently deleted
- Success message appears
- New messages start fresh conversation

### 3. Security and Access Control

#### Test 3.1: User Isolation
**Steps:**
1. Create two test users (User A and User B)
2. Login as User A, create chat history
3. Login as User B, verify cannot see User A's history
4. Try to access User A's conversation ID via API
5. Verify access is denied

**Expected Results:**
- Users only see their own chat history
- API requests for other users' data return 404/403
- No data leakage between users

#### Test 3.2: Authentication Requirements
**Steps:**
1. Logout from application
2. Try to access chat history API endpoints directly
3. Verify authentication is required
4. Login and verify access is restored

**Expected Results:**
- Unauthenticated requests return 401
- Chat history requires valid authentication
- Guest users see limited functionality

### 4. Performance and Limits

#### Test 4.1: Message Limit Enforcement
**Steps:**
1. Create conversation with 35+ messages
2. Verify only recent 30 messages are kept
3. Check that oldest messages are soft-deleted
4. Verify conversation performance remains good

**Expected Results:**
- Automatic cleanup maintains 30 message limit
- Older messages marked as deleted, not shown in UI
- Chat performance remains responsive

#### Test 4.2: Rate Limiting
**Steps:**
1. Make rapid API requests to chat history endpoints
2. Verify rate limiting kicks in after threshold
3. Wait for rate limit reset
4. Verify normal access is restored

**Expected Results:**
- Rate limiting prevents abuse (10 requests/minute for history)
- 429 status returned when limit exceeded
- Access restored after cooldown period

### 5. Cross-Platform Testing

#### Test 5.1: Mobile Responsiveness
**Steps:**
1. Test chat history on mobile devices
2. Verify buttons are accessible and properly sized
3. Test scrolling through long chat history
4. Verify touch interactions work correctly

**Expected Results:**
- Chat management buttons are touch-friendly
- History scrolling works smoothly on mobile
- UI adapts properly to different screen sizes

#### Test 5.2: Browser Compatibility
**Steps:**
1. Test on Chrome, Firefox, Safari, Edge
2. Verify chat history loads consistently
3. Test JavaScript functionality across browsers
4. Verify CSS styling is consistent

**Expected Results:**
- Consistent behavior across modern browsers
- No JavaScript errors in console
- UI appears correctly in all browsers

### 6. Error Handling

#### Test 6.1: Network Failures
**Steps:**
1. Disconnect internet during chat history loading
2. Verify graceful error handling
3. Reconnect and verify recovery
4. Test partial loading scenarios

**Expected Results:**
- Appropriate error messages shown
- Fallback to localStorage for guests
- Graceful recovery when connection restored

#### Test 6.2: Database Issues
**Steps:**
1. Simulate database connection issues
2. Verify error handling in API responses
3. Check logging of errors
4. Verify user-friendly error messages

**Expected Results:**
- 500 errors handled gracefully
- Errors logged for debugging
- User sees helpful error messages

## Automated Testing

### Running Tests
```bash
# Run all Nala chat history tests
php artisan test tests/Feature/NalaChatHistoryTest.php

# Run with coverage
php artisan test --coverage tests/Feature/NalaChatHistoryTest.php

# Run cleanup command test
php artisan nala:cleanup-chat-history --dry-run
```

### Database Migrations
```bash
# Apply new indexes
php artisan migrate

# Rollback if needed
php artisan migrate:rollback --step=1
```

## Performance Monitoring

### Key Metrics to Monitor
1. Chat history loading time (should be < 500ms)
2. Message sending response time (should be < 2s)
3. Database query count per request
4. Memory usage during chat operations

### Database Queries to Monitor
```sql
-- Check conversation counts per user
SELECT user_id, COUNT(*) as conversation_count 
FROM nala_chat_conversations 
WHERE status = 'active' 
GROUP BY user_id 
ORDER BY conversation_count DESC;

-- Check message counts per conversation
SELECT conversation_id, COUNT(*) as message_count 
FROM nala_chat_messages 
WHERE status != 'deleted' 
GROUP BY conversation_id 
HAVING message_count > 30;

-- Check for orphaned messages
SELECT COUNT(*) as orphaned_messages 
FROM nala_chat_messages 
WHERE conversation_id NOT IN (
    SELECT id FROM nala_chat_conversations WHERE status != 'deleted'
);
```

## Troubleshooting Common Issues

### Issue: Chat history not loading
**Solutions:**
1. Check browser console for JavaScript errors
2. Verify API endpoints are accessible
3. Check user authentication status
4. Verify database connectivity

### Issue: Messages not persisting
**Solutions:**
1. Check conversation creation logic
2. Verify message storage in database
3. Check for validation errors
4. Verify user permissions

### Issue: Performance degradation
**Solutions:**
1. Run database cleanup command
2. Check for missing indexes
3. Monitor query performance
4. Consider conversation archiving

## Success Criteria

The chat history implementation is considered successful when:

✅ Users can seamlessly continue conversations across sessions
✅ Chat history loads quickly (< 500ms)
✅ Message limits are automatically maintained
✅ User data is properly isolated and secure
✅ Rate limiting prevents abuse
✅ Error handling is graceful and user-friendly
✅ Mobile experience is smooth and responsive
✅ All automated tests pass
✅ Performance metrics meet targets
✅ No data loss or corruption occurs
