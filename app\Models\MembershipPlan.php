<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class MembershipPlan extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'type',
        'price',
        'price_per_user',
        'duration_months',
        'is_free',
        'nala_prompts',
        'has_unlimited_nala',
        'has_ice_full',
        'has_ai_teaching_assistants_courses',
        'has_ai_teaching_assistants_tryout',
        'has_free_certifications',
        'has_blog_access',
        'career_path_predictor',
        'has_priority_support',
        'is_team_plan',
        'minimum_users',
        'maximum_users',
        'is_active',
        'is_featured',
        'sort_order',
        'color',
        'features_list',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'price_per_user' => 'decimal:2',
        'duration_months' => 'integer',
        'is_free' => 'boolean',
        'nala_prompts' => 'integer',
        'has_unlimited_nala' => 'boolean',
        'has_ice_full' => 'boolean',
        'has_ai_teaching_assistants_courses' => 'boolean',
        'has_ai_teaching_assistants_tryout' => 'boolean',
        'has_free_certifications' => 'boolean',
        'has_blog_access' => 'boolean',
        'has_priority_support' => 'boolean',
        'is_team_plan' => 'boolean',
        'minimum_users' => 'integer',
        'maximum_users' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'features_list' => 'array',
    ];

    /**
     * Get the user memberships for this plan.
     */
    public function userMemberships(): HasMany
    {
        return $this->hasMany(UserMembership::class);
    }

    /**
     * Get the payments for this plan.
     */
    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    /**
     * Scope a query to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured plans.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include individual plans.
     */
    public function scopeIndividual($query)
    {
        return $query->where('type', 'individual');
    }

    /**
     * Scope a query to only include team plans.
     */
    public function scopeTeam($query)
    {
        return $query->where('type', 'team');
    }

    /**
     * Get the formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free) {
            return 'Gratis';
        }

        return 'IDR ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get the formatted price per user for team plans.
     */
    public function getFormattedPricePerUserAttribute(): string
    {
        if (!$this->is_team_plan || !$this->price_per_user) {
            return '';
        }

        return 'IDR ' . number_format($this->price_per_user, 0, ',', '.') . '/user';
    }

    /**
     * Check if this is the free plan.
     */
    public function isFree(): bool
    {
        return $this->is_free || $this->price == 0;
    }

    /**
     * Get the career path predictor feature name.
     */
    public function getCareerPathPredictorNameAttribute(): string
    {
        return match($this->career_path_predictor) {
            'basic' => 'Basic Career Path Predictor',
            'enhanced' => 'Enhanced Career Path Predictor',
            'enhanced_with_job_board' => 'Enhanced with Job Board Integration',
            default => 'No Career Path Predictor'
        };
    }

    /**
     * Get all features as an array for display.
     */
    public function getAllFeaturesAttribute(): array
    {
        $features = [];

        if ($this->nala_prompts) {
            $features[] = $this->nala_prompts . ' NALA Prompts per day';
        } elseif ($this->has_unlimited_nala) {
            $features[] = 'Unlimited NALA Prompts';
        } else {
            $features[] = 'Limited NALA Messages (Throttled)';
        }

        if ($this->has_ice_full) {
            $features[] = 'Full Intelligent Course Engine (ICE)';
        } else {
            $features[] = 'Limited Intelligent Course Engine (ICE)';
        }

        if ($this->has_ai_teaching_assistants_courses) {
            $features[] = 'Full AI Teaching Assistants (Courses)';
        } else {
            $features[] = 'Limited AI Teaching Assistants (Courses)';
        }

        if ($this->has_ai_teaching_assistants_tryout) {
            $features[] = 'AI Teaching Assistants (Tryout)';
        }

        if ($this->has_free_certifications) {
            $features[] = 'Free Certifications for Free Courses';
        }

        if ($this->has_blog_access) {
            $features[] = 'Blog Access & Creation';
        }

        if ($this->career_path_predictor !== 'none') {
            $features[] = $this->career_path_predictor_name;
        }

        if ($this->has_priority_support) {
            $features[] = 'Priority Support';
        }

        // Add custom features from features_list
        if ($this->features_list) {
            $features = array_merge($features, $this->features_list);
        }

        return $features;
    }
}
