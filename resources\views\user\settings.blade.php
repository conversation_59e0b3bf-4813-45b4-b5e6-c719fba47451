@extends('layouts.user')

@section('title', 'Pengaturan - Ngambiskuy')

@section('content')
<div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Pengaturan</h1>
                <p class="text-gray-600 mt-1">Kelola preferensi akun dan pengaturan aplikasi Anda</p>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            {{ session('success') }}
        </div>
    @endif

    <form action="{{ route('user.settings.update') }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')

        <!-- Account Settings -->
        {{-- <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Pengaturan Akun</h2>

            <div class="space-y-6">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h3 class="font-medium text-gray-900">Informasi Profil</h3>
                        <p class="text-sm text-gray-600">Ubah nama, email, dan informasi profil lainnya</p>
                    </div>
                    <a href="{{ route('user.profile') }}" class="btn btn-outline">
                        Edit Profil
                    </a>
                </div>

                @if($hasTermsAgreed)
                 <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                     <div>
                         <h3 class="font-medium text-gray-900">Persetujuan Syarat & Ketentuan</h3>
                         <p class="text-sm text-gray-600">Status persetujuan syarat dan ketentuan</p>
                     </div>
                     <label class="relative inline-flex items-center cursor-pointer">
                         <input type="checkbox" name="terms_agreed" value="1" class="sr-only peer" {{ $user->terms_agreed ? 'checked' : '' }}>
                         <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                     </label>
                 </div>
                 @endif

                @if($hasPrivacyAgreed)
                 <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                     <div>
                         <h3 class="font-medium text-gray-900">Persetujuan Kebijakan Privasi</h3>
                         <p class="text-sm text-gray-600">Status persetujuan kebijakan privasi</p>
                     </div>
                     <label class="relative inline-flex items-center cursor-pointer">
                         <input type="checkbox" name="privacy_agreed" value="1" class="sr-only peer" {{ $user->privacy_agreed ? 'checked' : '' }}>
                         <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                     </label>
                 </div>
                 @endif
            </div>
        </div> --}}

        @if($hasLearningPreferences || $hasMinatBelajar)
        <!-- Learning Preferences -->
        <div class="bg-white rounded-xl shadow-sm p-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Preferensi Belajar</h2>

            <div class="space-y-6">
                @if($hasLearningPreferences)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Gaya Belajar</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        @php
                            $learningStyles = ['visual', 'auditory', 'kinesthetic', 'reading'];
                            $userLearningPrefs = $user->learning_preferences ?? [];
                        @endphp
                        @foreach($learningStyles as $style)
                        <label class="flex items-center">
                            <input type="checkbox" name="learning_preferences[]" value="{{ $style }}" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   {{ in_array($style, $userLearningPrefs) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-gray-700">{{ ucfirst($style) }}</span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endif

                @if($hasMinatBelajar)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Minat Pembelajaran</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        @php
                            $interests = ['programming', 'design', 'ai', 'data_science', 'mobile_dev', 'business', 'marketing', 'finance'];
                            $userInterests = $user->minat_belajar ?? [];
                        @endphp
                        @foreach($interests as $interest)
                        <label class="flex items-center">
                            <input type="checkbox" name="minat_belajar[]" value="{{ $interest }}" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   {{ in_array($interest, $userInterests) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-gray-700">
                                @switch($interest)
                                    @case('programming') Programming @break
                                    @case('design') Design @break
                                    @case('ai') AI/ML @break
                                    @case('data_science') Data Science @break
                                    @case('mobile_dev') Mobile Dev @break
                                    @case('business') Business @break
                                    @case('marketing') Marketing @break
                                    @case('finance') Finance @break
                                    @default {{ ucfirst($interest) }}
                                @endswitch
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        @if($hasWorkPreferences || $hasIndustryInterests)
        <!-- Career Preferences -->
        <div class="bg-white rounded-xl shadow-sm p-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Preferensi Karir</h2>

            <div class="space-y-6">
                @if($hasWorkPreferences)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Preferensi Kerja</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        @php
                            $workPrefs = ['remote', 'hybrid', 'onsite', 'freelance', 'full_time', 'part_time'];
                            $userWorkPrefs = $user->work_preferences ?? [];
                        @endphp
                        @foreach($workPrefs as $pref)
                        <label class="flex items-center">
                            <input type="checkbox" name="work_preferences[]" value="{{ $pref }}" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   {{ in_array($pref, $userWorkPrefs) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-gray-700">
                                @switch($pref)
                                    @case('remote') Remote @break
                                    @case('hybrid') Hybrid @break
                                    @case('onsite') Onsite @break
                                    @case('freelance') Freelance @break
                                    @case('full_time') Full Time @break
                                    @case('part_time') Part Time @break
                                    @default {{ ucfirst($pref) }}
                                @endswitch
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endif

                @if($hasIndustryInterests)
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Minat Industri</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        @php
                            $industries = ['technology', 'finance', 'healthcare', 'education', 'ecommerce', 'gaming', 'startup', 'consulting'];
                            $userIndustries = $user->industry_interests ?? [];
                        @endphp
                        @foreach($industries as $industry)
                        <label class="flex items-center">
                            <input type="checkbox" name="industry_interests[]" value="{{ $industry }}" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   {{ in_array($industry, $userIndustries) ? 'checked' : '' }}>
                            <span class="ml-2 text-sm text-gray-700">
                                @switch($industry)
                                    @case('technology') Technology @break
                                    @case('finance') Finance @break
                                    @case('healthcare') Healthcare @break
                                    @case('education') Education @break
                                    @case('ecommerce') E-commerce @break
                                    @case('gaming') Gaming @break
                                    @case('startup') Startup @break
                                    @case('consulting') Consulting @break
                                    @default {{ ucfirst($industry) }}
                                @endswitch
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Simpan Pengaturan
            </button>
        </div>
    </form>
</div>

<script>
// Auto-save functionality and real-time updates
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[type="checkbox"]');
    
    // Add change listeners to all inputs for real-time feedback
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual feedback when settings change
            const saveButton = document.querySelector('button[type="submit"]');
            saveButton.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
            saveButton.classList.remove('btn-primary');
            saveButton.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>Simpan Perubahan';
        });
    });
});
</script>
@endsection
