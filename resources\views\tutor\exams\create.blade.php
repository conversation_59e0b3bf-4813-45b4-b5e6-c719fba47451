@extends('layouts.tutor')

@section('title', '<PERSON>uat <PERSON>')

@section('content')
<div class="tutor-dashboard-container tutor-exam-create-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex items-center space-x-2 md:space-x-3 mb-2">
                    <a href="{{ route('tutor.exams') }}" class="text-gray-500 hover:text-gray-700 tutor-touch-friendly p-1">
                        <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900">Buat Ujian Baru</h1>
                    <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 md:py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Ujian Mandiri
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">Buat ujian mandiri yang dapat dijual terpisah dari kursus</p>
            </div>
            <div class="tutor-header-actions flex items-center">
                <a href="{{ route('tutor.exams.download-template') }}" class="btn bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all tutor-touch-friendly w-full md:w-auto text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download Template CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form action="{{ route('tutor.exams.store') }}" method="POST" id="examForm">
        @csrf

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-4 md:space-y-6">
                <!-- Basic Information -->
                <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                    <h2 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Informasi Dasar</h2>

                    <div class="tutor-form-mobile space-y-3 md:space-y-4">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Judul Ujian *</label>
                            <input type="text" name="title" id="title" value="{{ old('title') }}" required
                                   class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Masukkan judul ujian">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi *</label>
                            <textarea name="description" id="description" rows="3" required
                                      class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                      placeholder="Jelaskan tentang ujian ini">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Kategori *</label>
                                <select name="category_id" id="category_id" required
                                        class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    <option value="">Pilih Kategori</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">Tingkat Kesulitan *</label>
                                <select name="difficulty_level" id="difficulty_level" required
                                        class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                                    <option value="">Pilih Tingkat</option>
                                    <option value="beginner" {{ old('difficulty_level') == 'beginner' ? 'selected' : '' }}>Pemula</option>
                                    <option value="intermediate" {{ old('difficulty_level') == 'intermediate' ? 'selected' : '' }}>Menengah</option>
                                    <option value="advanced" {{ old('difficulty_level') == 'advanced' ? 'selected' : '' }}>Lanjutan</option>
                                </select>
                                @error('difficulty_level')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Ujian *</label>
                            <div class="flex flex-col md:flex-row gap-3 md:gap-4">
                                <label class="flex items-center tutor-touch-friendly">
                                    <input type="radio" name="exam_type" value="free"
                                           {{ old('exam_type', 'free') == 'free' ? 'checked' : '' }}
                                           onchange="toggleExamPriceField()"
                                           class="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300 focus:ring-emerald-500">
                                    <span class="ml-2 text-sm text-gray-700">Gratis</span>
                                </label>
                                <label class="flex items-center tutor-touch-friendly">
                                    <input type="radio" name="exam_type" value="paid"
                                           {{ old('exam_type') == 'paid' ? 'checked' : '' }}
                                           onchange="toggleExamPriceField()"
                                           class="w-4 h-4 text-emerald-600 bg-gray-100 border-gray-300 focus:ring-emerald-500">
                                    <span class="ml-2 text-sm text-gray-700">Berbayar</span>
                                </label>
                            </div>
                            @error('exam_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div id="exam-price-field" class="{{ old('exam_type', 'free') == 'free' ? 'hidden' : '' }}">
                            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Harga (Rp) *</label>
                            <input type="number" name="price" id="price" value="{{ old('price', 0) }}"
                                   {{ old('exam_type', 'free') == 'paid' ? 'min="15000" step="1000" required' : 'min="0" step="1"' }}
                                   class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="15000">
                            <p class="mt-1 text-xs text-gray-500">
                                Harga minimum: IDR 15.000. Tutor mendapat 80%, platform 20% (fixed rate, no referral).
                            </p>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="instructions" class="block text-sm font-medium text-gray-700 mb-2">Instruksi Ujian</label>
                            <textarea name="instructions" id="instructions" rows="3"
                                      class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                      placeholder="Instruksi khusus untuk peserta ujian (opsional)">{{ old('instructions') }}</textarea>
                            @error('instructions')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Quiz Settings -->
                <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                    <h2 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Pengaturan Ujian</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                        <div>
                            <label for="time_limit" class="block text-sm font-medium text-gray-700 mb-2">Batas Waktu (menit) *</label>
                            <input type="number" name="time_limit" id="time_limit" value="{{ old('time_limit', 60) }}" min="1" max="300" required
                                   class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('time_limit')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-2">Maksimal Percobaan *</label>
                            <input type="number" name="max_attempts" id="max_attempts" value="{{ old('max_attempts', 3) }}" min="1" max="10" required
                                   class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('max_attempts')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="passing_score" class="block text-sm font-medium text-gray-700 mb-2">Nilai Lulus (%) *</label>
                            <input type="number" name="passing_score" id="passing_score" value="{{ old('passing_score', 70) }}" min="0" max="100" required
                                   class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            @error('passing_score')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex flex-col space-y-3">
                            <div class="flex items-center tutor-touch-friendly">
                                <input type="checkbox" name="shuffle_questions" id="shuffle_questions" value="1" {{ old('shuffle_questions') ? 'checked' : '' }}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                                <label for="shuffle_questions" class="ml-2 text-sm text-gray-700">Acak urutan soal</label>
                            </div>

                            <div class="flex items-center tutor-touch-friendly">
                                <input type="checkbox" name="show_results_immediately" id="show_results_immediately" value="1" {{ old('show_results_immediately', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded">
                                <label for="show_results_immediately" class="ml-2 text-sm text-gray-700">Tampilkan hasil langsung</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Questions Section -->
                <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-3 md:mb-4 gap-3">
                        <h2 class="text-base md:text-lg font-semibold text-gray-900">Soal Ujian</h2>
                        <div class="flex flex-col md:flex-row gap-2 md:gap-2">
                            <button type="button" onclick="addQuestion()"
                                    class="btn btn-sm bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly order-1">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Tambah Soal
                            </button>
                            <button type="button" onclick="showImportModal()"
                                    class="btn btn-sm btn-outline tutor-touch-friendly order-2">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                </svg>
                                Import CSV
                            </button>
                        </div>
                    </div>

                    <div id="questions_container" class="space-y-3 md:space-y-4">
                        <!-- Questions will be added here -->
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-4 md:space-y-6">
                <!-- Actions -->
                <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Aksi</h3>
                    <!-- Mobile: Vertical Button Layout (primary action first) -->
                    <div class="flex flex-col gap-3">
                        <button type="submit" class="w-full btn bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly order-1">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Simpan Ujian
                        </button>
                        <a href="{{ route('tutor.exams') }}" class="w-full btn btn-outline tutor-touch-friendly order-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Batal
                        </a>
                    </div>
                </div>

                <!-- Help -->
                <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg shadow-sm p-4 md:p-6 border border-emerald-200">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Tips Membuat Ujian</h3>
                    <ul class="space-y-2 text-xs md:text-sm text-gray-600">
                        <li class="flex items-start">
                            <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Buat minimal 5 soal untuk ujian yang berkualitas
                        </li>
                        <li class="flex items-start">
                            <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Gunakan template CSV untuk import soal dalam jumlah banyak
                        </li>
                        <li class="flex items-start">
                            <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Berikan penjelasan untuk setiap jawaban yang benar
                        </li>
                        <li class="flex items-start">
                            <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Atur waktu yang sesuai dengan jumlah soal
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Import CSV Modal -->
<div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-3 md:p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-4 md:p-6">
                <div class="flex items-center justify-between mb-3 md:mb-4">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900">Import Soal dari CSV</h3>
                    <button type="button" onclick="hideImportModal()" class="text-gray-400 hover:text-gray-600 tutor-touch-friendly p-1">
                        <svg class="w-5 h-5 md:w-6 md:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Download Template -->
                <div class="mb-4 md:mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2 md:mb-3">1. Download Template CSV</label>
                    <a href="{{ route('tutor.exams.download-template') }}"
                       class="inline-flex items-center px-3 md:px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors tutor-touch-friendly">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Template
                    </a>
                </div>

                <!-- Upload CSV -->
                <div class="mb-4 md:mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2 md:mb-3">2. Upload File CSV</label>
                    <input type="file" id="csvFile" accept=".csv" onchange="handleCSVUpload(event)"
                           class="tutor-form-mobile w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                </div>

                <!-- Preview -->
                <div id="csvPreview" class="hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2 md:mb-3">3. Preview Soal</label>
                    <div id="csvPreviewContent" class="max-h-48 md:max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-3 md:p-4 mb-3 md:mb-4">
                        <!-- Preview content will be inserted here -->
                    </div>
                    <!-- Mobile: Vertical Button Layout (primary action first) -->
                    <div class="flex flex-col md:flex-row md:justify-end gap-3 md:gap-3 md:space-x-0">
                        <button type="button" onclick="importQuestions()" class="btn bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly order-1">Import Soal</button>
                        <button type="button" onclick="hideImportModal()" class="btn btn-outline tutor-touch-friendly order-2">Batal</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let questionCounter = 0;
let csvData = [];

// Add first question by default
document.addEventListener('DOMContentLoaded', function() {
    addQuestion();
    setupRealTimeValidation();
    // Initialize price field attributes based on current exam type
    toggleExamPriceField();
});

// Real-time validation setup
function setupRealTimeValidation() {
    // Title validation
    const titleInput = document.getElementById('title');
    titleInput.addEventListener('input', function() {
        validateField(this, 'Judul ujian harus diisi');
    });

    // Description validation
    const descriptionInput = document.getElementById('description');
    descriptionInput.addEventListener('input', function() {
        validateField(this, 'Deskripsi harus diisi');
    });

    // Category validation
    const categorySelect = document.getElementById('category_id');
    categorySelect.addEventListener('change', function() {
        validateField(this, 'Kategori harus dipilih');
    });

    // Difficulty level validation
    const difficultySelect = document.getElementById('difficulty_level');
    difficultySelect.addEventListener('change', function() {
        validateField(this, 'Tingkat kesulitan harus dipilih');
    });

    // Price validation for paid exams
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        const examType = document.querySelector('input[name="exam_type"]:checked').value;
        if (examType === 'paid') {
            validatePriceField(this);
        }
    });

    // Time limit validation
    const timeLimitInput = document.getElementById('time_limit');
    timeLimitInput.addEventListener('input', function() {
        validateNumberField(this, 1, 300, 'Batas waktu harus antara 1-300 menit');
    });

    // Max attempts validation
    const maxAttemptsInput = document.getElementById('max_attempts');
    maxAttemptsInput.addEventListener('input', function() {
        validateNumberField(this, 1, 10, 'Maksimal percobaan harus antara 1-10');
    });

    // Passing score validation
    const passingScoreInput = document.getElementById('passing_score');
    passingScoreInput.addEventListener('input', function() {
        validateNumberField(this, 0, 100, 'Nilai lulus harus antara 0-100%');
    });

    // Form submission validation
    const examForm = document.getElementById('examForm');
    examForm.addEventListener('submit', function(e) {
        if (!validateFormSubmission()) {
            e.preventDefault();
            return false;
        }
    });
}

function validateField(field, message) {
    const value = field.value.trim();
    const isValid = value !== '';

    showValidationFeedback(field, isValid, message);
    return isValid;
}

function validatePriceField(field) {
    const value = parseInt(field.value);
    const isValid = !isNaN(value) && value >= 15000 && value % 1000 === 0;
    const message = 'Harga minimum IDR 15.000 dan harus kelipatan 1000';

    showValidationFeedback(field, isValid, message);
    return isValid;
}

function validateNumberField(field, min, max, message) {
    const value = parseInt(field.value);
    const isValid = !isNaN(value) && value >= min && value <= max;

    showValidationFeedback(field, isValid, message);
    return isValid;
}

function showValidationFeedback(field, isValid, message) {
    // Remove existing validation elements
    const existingError = field.parentNode.querySelector('.validation-error');
    if (existingError) existingError.remove();

    // Update field styling
    field.classList.remove('border-red-500', 'border-green-500', 'focus:ring-red-500', 'focus:ring-green-500');

    if (field.value.trim() === '') {
        // No styling for empty fields (neutral state)
        field.classList.add('border-gray-300', 'focus:ring-emerald-500');
        return;
    }

    if (isValid) {
        field.classList.add('border-green-500', 'focus:ring-green-500');
    } else {
        field.classList.add('border-red-500', 'focus:ring-red-500');
        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'validation-error mt-1 text-sm text-red-600 flex items-center';
        errorDiv.innerHTML = `<svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>${message}`;
        field.parentNode.appendChild(errorDiv);
    }
}

// Setup validation for individual question
function setupQuestionValidation(questionId) {
    // Question text validation
    const questionTextarea = document.querySelector(`textarea[name="questions[${questionId}][question]"]`);
    if (questionTextarea) {
        questionTextarea.addEventListener('input', function() {
            validateField(this, 'Pertanyaan harus diisi');
        });
    }

    // Points validation
    const pointsInput = document.querySelector(`input[name="questions[${questionId}][points]"]`);
    if (pointsInput) {
        pointsInput.addEventListener('input', function() {
            validateNumberField(this, 1, 100, 'Poin harus antara 1-100');
        });
    }

    // Options validation (for multiple choice and true/false)
    const optionInputs = document.querySelectorAll(`input[name="questions[${questionId}][options][]"]`);
    optionInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            const typeSelect = document.querySelector(`select[name="questions[${questionId}][type]"]`);
            if (typeSelect.value === 'multiple_choice' || typeSelect.value === 'true_false') {
                const optionLabel = String.fromCharCode(65 + index); // A, B, C, D
                validateField(this, `Pilihan ${optionLabel} harus diisi`);
            }
        });
    });
}

// Form submission validation
function validateFormSubmission() {
    let isValid = true;
    let firstInvalidField = null;

    // Validate basic fields
    const requiredFields = [
        { field: document.getElementById('title'), message: 'Judul ujian harus diisi' },
        { field: document.getElementById('description'), message: 'Deskripsi harus diisi' },
        { field: document.getElementById('category_id'), message: 'Kategori harus dipilih' },
        { field: document.getElementById('difficulty_level'), message: 'Tingkat kesulitan harus dipilih' },
        { field: document.getElementById('time_limit'), message: 'Batas waktu harus diisi' },
        { field: document.getElementById('max_attempts'), message: 'Maksimal percobaan harus diisi' },
        { field: document.getElementById('passing_score'), message: 'Nilai lulus harus diisi' }
    ];

    // Check price for paid exams
    const examType = document.querySelector('input[name="exam_type"]:checked').value;
    if (examType === 'paid') {
        requiredFields.push({ field: document.getElementById('price'), message: 'Harga harus diisi untuk ujian berbayar' });
    }

    requiredFields.forEach(({ field, message }) => {
        if (!field.value.trim()) {
            showValidationFeedback(field, false, message);
            isValid = false;
            if (!firstInvalidField) firstInvalidField = field;
        }
    });

    // Validate questions
    const questionContainers = document.querySelectorAll('[id^="question_"]');
    if (questionContainers.length === 0) {
        alert('Minimal harus ada 1 soal untuk ujian');
        isValid = false;
    } else {
        questionContainers.forEach((container, index) => {
            const questionId = container.id.split('_')[1];
            const questionTextarea = container.querySelector(`textarea[name="questions[${questionId}][question]"]`);

            if (!questionTextarea.value.trim()) {
                showValidationFeedback(questionTextarea, false, 'Pertanyaan harus diisi');
                isValid = false;
                if (!firstInvalidField) firstInvalidField = questionTextarea;
            }

            // Validate options for multiple choice and true/false
            const typeSelect = container.querySelector(`select[name="questions[${questionId}][type]"]`);
            if (typeSelect.value === 'multiple_choice' || typeSelect.value === 'true_false') {
                const optionInputs = container.querySelectorAll(`input[name="questions[${questionId}][options][]"]`);
                const requiredOptions = typeSelect.value === 'multiple_choice' ? 4 : 2;

                // Validate option inputs
                for (let i = 0; i < requiredOptions; i++) {
                    if (optionInputs[i] && !optionInputs[i].value.trim()) {
                        const optionLabel = String.fromCharCode(65 + i);
                        showValidationFeedback(optionInputs[i], false, `Pilihan ${optionLabel} harus diisi`);
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = optionInputs[i];
                    }
                }

                // Validate correct answer selection
                const correctAnswerRadio = container.querySelector(`input[name="questions[${questionId}][correct_answer]"]:checked`);
                if (!correctAnswerRadio) {
                    const firstRadio = container.querySelector(`input[name="questions[${questionId}][correct_answer]"]`);
                    if (firstRadio) {
                        showValidationFeedback(firstRadio, false, 'Pilih jawaban yang benar');
                        isValid = false;
                        if (!firstInvalidField) firstInvalidField = firstRadio;
                    }
                }
            }
        });
    }

    // Scroll to first invalid field
    if (!isValid && firstInvalidField) {
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstInvalidField.focus();
    }

    return isValid;
}

function addQuestion() {
    questionCounter++;
    const container = document.getElementById('questions_container');
    const questionDiv = document.createElement('div');
    questionDiv.className = 'border border-gray-200 rounded-lg p-4';
    questionDiv.id = `question_${questionCounter}`;

    questionDiv.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h4 class="font-medium text-gray-900">Soal ${questionCounter}</h4>
            <button type="button" onclick="removeQuestion(${questionCounter})" class="text-red-600 hover:text-red-800 transition-colors">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan <span class="text-red-500">*</span></label>
                <textarea name="questions[${questionCounter}][question]" rows="3" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                          placeholder="Masukkan pertanyaan ujian..."></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                    <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200">
                        <option value="multiple_choice">Pilihan Ganda</option>
                        <option value="true_false">Benar/Salah</option>
                        <option value="short_answer">Jawaban Singkat</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                    <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                           placeholder="10">
                </div>
            </div>

            <div id="options_${questionCounter}">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Pilihan Jawaban <span class="text-red-500">*</span>
                    <span class="text-xs text-gray-500 font-normal">(Pilih radio button untuk menandai jawaban yang benar)</span>
                </label>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input type="radio" name="questions[${questionCounter}][correct_answer]" value="0"
                               class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                        <span class="w-6 h-6 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center text-xs font-medium">A</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Pilihan A" required>
                    </div>
                    <div class="flex items-center space-x-3 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input type="radio" name="questions[${questionCounter}][correct_answer]" value="1"
                               class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                        <span class="w-6 h-6 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center text-xs font-medium">B</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Pilihan B" required>
                    </div>
                    <div class="flex items-center space-x-3 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input type="radio" name="questions[${questionCounter}][correct_answer]" value="2"
                               class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                        <span class="w-6 h-6 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center text-xs font-medium">C</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Pilihan C" required>
                    </div>
                    <div class="flex items-center space-x-3 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <input type="radio" name="questions[${questionCounter}][correct_answer]" value="3"
                               class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                        <span class="w-6 h-6 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center text-xs font-medium">D</span>
                        <input type="text" name="questions[${questionCounter}][options][]"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Pilihan D" required>
                    </div>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                <textarea name="questions[${questionCounter}][explanation]" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                          placeholder="Penjelasan jawaban yang benar (akan ditampilkan setelah ujian selesai)"></textarea>
            </div>
        </div>
    `;

    container.appendChild(questionDiv);

    // Add validation for the new question fields
    setupQuestionValidation(questionCounter);
}

function removeQuestion(questionId) {
    const questionDiv = document.getElementById(`question_${questionId}`);
    if (questionDiv) {
        questionDiv.remove();
    }

    // Renumber remaining questions
    const container = document.getElementById('questions_container');
    const questions = container.children;
    for (let i = 0; i < questions.length; i++) {
        const questionDiv = questions[i];
        const header = questionDiv.querySelector('h4');
        if (header) {
            header.textContent = `Soal ${i + 1}`;
        }
    }
}

function toggleQuestionOptions(questionId) {
    const typeSelect = document.querySelector(`select[name="questions[${questionId}][type]"]`);
    const optionsDiv = document.getElementById(`options_${questionId}`);

    if (typeSelect.value === 'multiple_choice') {
        optionsDiv.style.display = 'block';

        // Show all 4 options (A, B, C, D)
        const optionContainers = optionsDiv.querySelectorAll('.flex.items-center.space-x-3');
        optionContainers.forEach((container, index) => {
            container.style.display = 'flex';
            const textInput = container.querySelector('input[type="text"]');
            const radioInput = container.querySelector('input[type="radio"]');
            const labelSpan = container.querySelector('span');

            // Reset placeholders, radio values, and labels
            if (textInput) {
                textInput.placeholder = `Pilihan ${String.fromCharCode(65 + index)}`;
                textInput.required = true;
                textInput.value = ''; // Clear any previous values
            }
            if (radioInput) {
                radioInput.value = index;
            }
            if (labelSpan) {
                labelSpan.textContent = String.fromCharCode(65 + index);
            }
        });
    } else if (typeSelect.value === 'true_false') {
        optionsDiv.style.display = 'block';

        // Show only first 2 options and hide C, D
        const optionContainers = optionsDiv.querySelectorAll('.flex.items-center.space-x-3');
        optionContainers.forEach((container, index) => {
            if (index < 2) {
                container.style.display = 'flex';
                const textInput = container.querySelector('input[type="text"]');
                const radioInput = container.querySelector('input[type="radio"]');
                const labelSpan = container.querySelector('span');

                // Update placeholders and labels for true/false
                if (textInput) {
                    textInput.placeholder = index === 0 ? 'Benar' : 'Salah';
                    textInput.value = index === 0 ? 'Benar' : 'Salah';
                    textInput.required = true;
                }
                if (radioInput) {
                    radioInput.value = index === 0 ? 'true' : 'false';
                }
                if (labelSpan) {
                    labelSpan.textContent = index === 0 ? 'A' : 'B';
                }
            } else {
                container.style.display = 'none';
                const textInput = container.querySelector('input[type="text"]');
                if (textInput) {
                    textInput.required = false;
                }
            }
        });
    } else if (typeSelect.value === 'short_answer') {
        optionsDiv.style.display = 'none';

        // Remove required attribute from all option inputs and clear radio selections
        const textInputs = optionsDiv.querySelectorAll('input[type="text"]');
        const radioInputs = optionsDiv.querySelectorAll('input[type="radio"]');
        textInputs.forEach(input => {
            input.required = false;
        });
        radioInputs.forEach(input => {
            input.checked = false;
        });
    }
}

// CSV Import Functions
function showImportModal() {
    document.getElementById('importModal').classList.remove('hidden');
}

function hideImportModal() {
    document.getElementById('importModal').classList.add('hidden');
    document.getElementById('csvFile').value = '';
    document.getElementById('csvPreview').classList.add('hidden');
    csvData = [];
}

function handleCSVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        parseCSV(csv);
    };
    reader.readAsText(file);
}

function parseCSV(csv) {
    const lines = csv.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    if (lines.length < 2) {
        alert('File CSV tidak valid atau kosong!');
        return;
    }

    const headers = lines[0].split(',').map(h => h.trim());
    csvData = [];

    for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim());
        if (values.length >= 3) { // At least question, type, points
            const row = {};
            headers.forEach((header, index) => {
                row[header] = values[index] || '';
            });
            csvData.push(row);
        }
    }

    if (csvData.length > 0) {
        showCSVPreview();
    } else {
        alert('Tidak ada data valid yang ditemukan dalam file CSV!');
    }
}

function showCSVPreview() {
    const previewDiv = document.getElementById('csvPreviewContent');
    let html = '<div class="space-y-4">';

    csvData.forEach((row, index) => {
        html += `
            <div class="border border-gray-200 rounded-lg p-3">
                <div class="font-medium text-gray-900 mb-2">${index + 1}. ${row.question}</div>
                <div class="text-sm text-gray-600 mb-1">Tipe: ${getTypeLabel(row.type)} | Poin: ${row.points}</div>
        `;

        if (row.type === 'multiple_choice') {
            html += '<div class="text-sm text-gray-600">';
            ['A', 'B', 'C', 'D'].forEach(letter => {
                const option = row[`option_${letter.toLowerCase()}`];
                if (option) {
                    const isCorrect = row.correct_answer === letter;
                    html += `<div class="${isCorrect ? 'font-medium text-green-600' : ''}">${letter}. ${option}</div>`;
                }
            });
            html += '</div>';
        } else if (row.type === 'true_false') {
            html += '<div class="text-sm text-gray-600">';
            html += `<div class="${row.correct_answer === 'A' ? 'font-medium text-green-600' : ''}">A. ${row.option_a || 'Benar'}</div>`;
            html += `<div class="${row.correct_answer === 'B' ? 'font-medium text-green-600' : ''}">B. ${row.option_b || 'Salah'}</div>`;
            html += '</div>';
        }

        html += '</div>';
    });

    html += '</div>';
    html += `<div class="mt-4 p-3 bg-green-50 rounded-lg">
        <div class="text-sm text-green-800">
            <strong>Total: ${csvData.length} soal</strong> siap untuk diimport
        </div>
    </div>`;

    previewDiv.innerHTML = html;
    document.getElementById('csvPreview').classList.remove('hidden');
}

function getTypeLabel(type) {
    switch(type) {
        case 'multiple_choice': return 'Pilihan Ganda';
        case 'true_false': return 'Benar/Salah';
        case 'short_answer': return 'Jawaban Singkat';
        default: return type;
    }
}

function importQuestions() {
    if (csvData.length === 0) {
        alert('Tidak ada data untuk diimport!');
        return;
    }

    // Clear existing questions
    document.getElementById('questions_container').innerHTML = '';
    questionCounter = 0;

    // Import each question
    csvData.forEach(row => {
        questionCounter++;
        const container = document.getElementById('questions_container');
        const questionDiv = document.createElement('div');
        questionDiv.className = 'border border-gray-200 rounded-lg p-4';
        questionDiv.id = `question_${questionCounter}`;

        let optionsHtml = '';
        if (row.type === 'multiple_choice') {
            const options = [row.option_a, row.option_b, row.option_c, row.option_d].filter(opt => opt);
            const correctIndex = ['A', 'B', 'C', 'D'].indexOf(row.correct_answer.toUpperCase());

            optionsHtml = `
                <div id="options_${questionCounter}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                    <div class="space-y-2">
                        ${options.map((option, index) => `
                            <div class="flex items-center space-x-2">
                                <input type="radio" name="questions[${questionCounter}][correct_answer]" value="${index}"
                                       ${index === correctIndex ? 'checked' : ''}
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                                <input type="text" name="questions[${questionCounter}][options][]" value="${option}"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                       placeholder="Pilihan ${String.fromCharCode(65 + index)}" required>
                            </div>
                        `).join('')}
                        ${options.length < 4 ? Array.from({length: 4 - options.length}, (_, i) => {
                            const index = options.length + i;
                            return `
                            <div class="flex items-center space-x-2">
                                <input type="radio" name="questions[${questionCounter}][correct_answer]" value="${index}"
                                       class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                                <input type="text" name="questions[${questionCounter}][options][]"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                       placeholder="Pilihan ${String.fromCharCode(65 + index)}" required>
                            </div>
                            `;
                        }).join('') : ''}
                    </div>
                </div>
            `;
        } else if (row.type === 'true_false') {
            const isTrue = row.correct_answer.toUpperCase() === 'A' || row.correct_answer.toLowerCase() === 'benar';
            optionsHtml = `
                <div id="options_${questionCounter}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="true"
                                   ${isTrue ? 'checked' : ''}
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]" value="${row.option_a || 'Benar'}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Benar" required>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="false"
                                   ${!isTrue ? 'checked' : ''}
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]" value="${row.option_b || 'Salah'}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Salah" required>
                        </div>
                        <div class="flex items-center space-x-2" style="display: none;">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="2"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan C">
                        </div>
                        <div class="flex items-center space-x-2" style="display: none;">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="3"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan D">
                        </div>
                    </div>
                </div>
            `;
        } else {
            optionsHtml = `
                <div id="options_${questionCounter}" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="0"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan A">
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="1"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan B">
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="2"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan C">
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="radio" name="questions[${questionCounter}][correct_answer]" value="3"
                                   class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300">
                            <input type="text" name="questions[${questionCounter}][options][]"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                   placeholder="Pilihan D">
                        </div>
                    </div>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h4 class="font-medium text-gray-900">Soal ${questionCounter}</h4>
                <button type="button" onclick="removeQuestion(${questionCounter})" class="text-red-600 hover:text-red-800">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan *</label>
                    <textarea name="questions[${questionCounter}][question]" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">${row.question}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                        <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                            <option value="multiple_choice" ${row.type === 'multiple_choice' ? 'selected' : ''}>Pilihan Ganda</option>
                            <option value="true_false" ${row.type === 'true_false' ? 'selected' : ''}>Benar/Salah</option>
                            <option value="short_answer" ${row.type === 'short_answer' ? 'selected' : ''}>Jawaban Singkat</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                        <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="${row.points || 10}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                    </div>
                </div>

                ${optionsHtml}

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                    <textarea name="questions[${questionCounter}][explanation]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent">${row.explanation || ''}</textarea>
                </div>
            </div>
        `;

        container.appendChild(questionDiv);
    });

    hideImportModal();
    alert(`${csvData.length} soal berhasil diimport!`);
}

function toggleExamPriceField() {
    const examType = document.querySelector('input[name="exam_type"]:checked').value;
    const priceField = document.getElementById('exam-price-field');
    const priceInput = document.getElementById('price');

    if (examType === 'free') {
        priceField.classList.add('hidden');
        priceInput.value = 0;
        // Remove validation attributes for free exams to prevent validation errors
        priceInput.removeAttribute('required');
        priceInput.setAttribute('min', '0');
        priceInput.setAttribute('step', '1');
        // Clear any validation styling for price field when hidden
        priceInput.classList.remove('border-red-500', 'border-green-500', 'focus:ring-red-500', 'focus:ring-green-500');
        priceInput.classList.add('border-gray-300', 'focus:ring-emerald-500');
        const existingError = priceInput.parentNode.querySelector('.validation-error');
        if (existingError) existingError.remove();
    } else {
        priceField.classList.remove('hidden');
        // Add validation attributes back for paid exams
        priceInput.setAttribute('required', 'required');
        priceInput.setAttribute('min', '15000');
        priceInput.setAttribute('step', '1000');
        if (priceInput.value == 0) {
            priceInput.value = '';
        }
        // Trigger validation for price field when shown
        if (priceInput.value.trim() !== '') {
            validatePriceField(priceInput);
        }
    }
}
</script>
@endpush

@endsection
