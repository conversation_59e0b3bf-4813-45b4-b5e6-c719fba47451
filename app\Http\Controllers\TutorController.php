<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Models\Course;
use App\Models\UserMembership;
use App\Models\PayoutRequest;
use App\Models\TutorPaymentSettings;
use App\Models\Payment;

class TutorController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'is.tutor']);
    }

    /**
     * Show the tutor dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Get real data from database
        $totalCourses = \App\Models\Course::where('tutor_id', $user->id)->count();
        $publishedCourses = \App\Models\Course::where('tutor_id', $user->id)
            ->where('status', 'published')
            ->count();

        // Get active students (students enrolled in tutor's courses)
        $activeStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->distinct('user_id')->count('user_id');

        // Calculate dynamic tutor rating from all published courses
        $tutorRating = \App\Models\Course::where('tutor_id', $user->id)
            ->where('status', 'published')
            ->where('average_rating', '>', 0) // Only include courses with ratings
            ->avg('average_rating');

        // Format rating to 1 decimal place, fallback to 0 if no ratings
        $tutorRating = $tutorRating ? round($tutorRating, 1) : 0;

        // Calculate total revenue from course purchases
        $totalRevenue = \App\Models\CoursePurchase::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->where('status', 'active')->sum('amount_paid');

        // Calculate monthly earnings (current month)
        $monthlyEarnings = \App\Models\CoursePurchase::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->where('status', 'active')
        ->whereMonth('purchased_at', now()->month)
        ->whereYear('purchased_at', now()->year)
        ->sum('amount_paid');

        // Calculate course completion rate
        $totalEnrollments = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->count();

        $completedEnrollments = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->whereNotNull('completed_at')->count();

        $completionRate = $totalEnrollments > 0 ? round(($completedEnrollments / $totalEnrollments) * 100, 1) : 0;

        // Get total views (sum of total_students from all courses)
        $totalViews = \App\Models\Course::where('tutor_id', $user->id)
            ->where('status', 'published')
            ->sum('total_students');

        // Prepare stats array with dynamic data
        $stats = [
            'total_courses' => $totalCourses,
            'published_courses' => $publishedCourses,
            'active_students' => $activeStudents,
            'total_revenue' => $totalRevenue,
            'course_ratings' => $tutorRating,
            'monthly_earnings' => $monthlyEarnings,
            'completion_rate' => $completionRate,
            'total_views' => $totalViews
        ];

        // Get recent activities (recent course enrollments)
        $recentActivities = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get course performance data
        $coursePerformance = \App\Models\Course::with(['enrollments'])
            ->where('tutor_id', $user->id)
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        return view('tutor.dashboard', compact('user', 'stats', 'recentActivities', 'coursePerformance'));
    }

    /**
     * Show the tutor courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get real courses from database
        $publishedCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->get();

        $draftCourses = \App\Models\Course::with(['category', 'chapters', 'lessons'])
            ->where('tutor_id', $user->id)
            ->where('status', 'draft')
            ->orderBy('updated_at', 'desc')
            ->get();

        return view('tutor.courses', compact('user', 'publishedCourses', 'draftCourses'));
    }

    /**
     * Show the create course page.
     */
    public function createCourse()
    {
        $user = Auth::user();
        $categories = \App\Models\Category::active()->get();
        return view('tutor.create-course', compact('user', 'categories'));
    }

    /**
     * Store a new course.
     */
    public function storeCourse(Request $request)
    {
        $user = Auth::user();

        // Debug: Log incoming request data
        Log::info('Course creation attempt', [
            'user_id' => $user->id,
            'request_data' => $request->all(),
            'has_file' => $request->hasFile('thumbnail')
        ]);

        // Validate the request with custom error messages
        $validated = $request->validate([
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_duration' => 'required|integer|min:1|max:500',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|numeric|min:30000',
            'learning_outcomes' => 'nullable|array',
            'learning_outcomes.*' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'nullable|string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ], [
            'course_title.required' => 'Judul kursus harus diisi.',
            'course_title.min' => 'Judul kursus minimal 10 karakter.',
            'course_title.max' => 'Judul kursus maksimal 255 karakter.',
            'course_category.required' => 'Kategori kursus harus dipilih.',
            'course_category.exists' => 'Kategori yang dipilih tidak valid.',
            'course_description.required' => 'Deskripsi kursus harus diisi.',
            'course_description.min' => 'Deskripsi kursus minimal 50 karakter.',
            'course_description.max' => 'Deskripsi kursus maksimal 1000 karakter.',
            'course_level.required' => 'Level kursus harus dipilih.',
            'course_level.in' => 'Level kursus tidak valid.',
            'course_duration.required' => 'Estimasi durasi harus diisi.',
            'course_duration.integer' => 'Estimasi durasi harus berupa angka.',
            'course_duration.min' => 'Estimasi durasi minimal 1 jam.',
            'course_duration.max' => 'Estimasi durasi maksimal 500 jam.',
            'course_type.required' => 'Tipe kursus harus dipilih.',
            'course_type.in' => 'Tipe kursus tidak valid.',
            'course_price.required_if' => 'Harga kursus harus diisi untuk kursus berbayar.',
            'course_price.numeric' => 'Harga kursus harus berupa angka.',
            'course_price.min' => 'Harga minimum untuk kursus berbayar adalah IDR 30.000.',
            'learning_outcomes.*.max' => 'Tujuan pembelajaran maksimal 255 karakter.',
            'requirements.*.max' => 'Prasyarat maksimal 255 karakter.',
            'target_audience.*.max' => 'Target audience maksimal 255 karakter.',
            'thumbnail.image' => 'File thumbnail harus berupa gambar.',
            'thumbnail.mimes' => 'Format thumbnail harus JPG, PNG, atau GIF.',
            'thumbnail.max' => 'Ukuran thumbnail maksimal 10MB.',
        ]);

        // Additional validation for course price step (kelipatan 1000)
        if ($validated['course_type'] === 'paid' && isset($validated['course_price'])) {
            $price = (float) $validated['course_price'];
            if ($price % 1000 !== 0) {
                return back()->withInput()->withErrors(['course_price' => 'Harga kursus harus kelipatan 1000.']);
            }
        }

        // Filter out empty values from arrays
        $learningOutcomes = array_filter($request->input('learning_outcomes', []), function($value) {
            return !empty(trim($value));
        });

        $requirements = array_filter($request->input('requirements', []), function($value) {
            return !empty(trim($value));
        });

        $targetAudience = array_filter($request->input('target_audience', []), function($value) {
            return !empty(trim($value));
        });

        try {
            Log::info('Creating course with data', [
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience
            ]);

            // Determine if course is free and set price
            $isFree = $validated['course_type'] === 'free';
            $price = $isFree ? 0 : ($validated['course_price'] ?? 0);

            // Create the course first to get the course ID
            $course = \App\Models\Course::create([
                'tutor_id' => $user->id,
                'category_id' => $validated['course_category'],
                'title' => $validated['course_title'],
                'description' => $validated['course_description'],
                'level' => $validated['course_level'],
                'duration' => $validated['course_duration'],
                'price' => $price,
                'is_free' => $isFree,
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience,
                'status' => 'draft',
            ]);

            Log::info('Course created successfully', ['course_id' => $course->id]);

            // Handle thumbnail upload with custom path structure including thumbnail folder
            if ($request->hasFile('thumbnail')) {
                Log::info('Processing thumbnail upload');
                $thumbnailPath = $request->file('thumbnail')->store(
                    "user/{$user->id}/course/{$course->id}/thumbnail",
                    'public'
                );

                Log::info('Thumbnail uploaded', ['path' => $thumbnailPath]);

                // Update course with thumbnail path
                $course->update(['thumbnail' => $thumbnailPath]);

                Log::info('Course updated with thumbnail path');
            }

            Log::info('Redirecting to courses page');
            return redirect()->route('tutor.courses', ['tab' => 'drafts'])->with('success', 'Kursus berhasil dibuat! Anda dapat melanjutkan dengan menambahkan kurikulum.');

        } catch (\Illuminate\Database\QueryException $e) {
            Log::error('Database error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'sql' => $e->getSql() ?? 'N/A'
            ]);

            // Check for specific database errors
            if (str_contains($e->getMessage(), 'Duplicate entry')) {
                return back()->withInput()->with('error', 'Kursus dengan judul yang sama sudah ada. Silakan gunakan judul yang berbeda.');
            }

            return back()->withInput()->with('error', 'Terjadi kesalahan database saat membuat kursus. Silakan coba lagi.');

        } catch (\Illuminate\Contracts\Filesystem\FileNotFoundException $e) {
            Log::error('File upload error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withInput()->with('error', 'Terjadi kesalahan saat mengupload thumbnail. Silakan coba lagi.');

        } catch (\Exception $e) {
            Log::error('General error during course creation: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_id' => $user->id,
                'request_data' => $request->except(['thumbnail']) // Exclude file data from logs
            ]);

            // Provide more specific error messages based on error type
            if (str_contains($e->getMessage(), 'storage')) {
                return back()->withInput()->with('error', 'Terjadi kesalahan saat menyimpan file. Pastikan file tidak terlalu besar dan coba lagi.');
            }

            if (str_contains($e->getMessage(), 'validation')) {
                return back()->withInput()->with('error', 'Data yang dimasukkan tidak valid. Silakan periksa kembali form Anda.');
            }

            return back()->withInput()->with('error', 'Terjadi kesalahan tidak terduga saat membuat kursus. Tim teknis telah diberitahu. Silakan coba lagi dalam beberapa menit.');
        }
    }

    /**
     * Show the edit course page.
     */
    public function editCourse(\App\Models\Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        $categories = \App\Models\Category::active()->get();
        return view('tutor.edit-course', compact('user', 'course', 'categories'));
    }

    /**
     * Update the course.
     */
    public function updateCourse(Request $request, \App\Models\Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Validate the request with custom error messages
        $validated = $request->validate([
            'course_title' => 'required|string|min:10|max:255',
            'course_category' => 'required|exists:categories,id',
            'course_description' => 'required|string|min:50|max:1000',
            'course_level' => 'required|in:beginner,intermediate,advanced',
            'course_duration' => 'required|integer|min:1|max:500',
            'course_type' => 'required|in:free,paid',
            'course_price' => 'required_if:course_type,paid|numeric|min:30000',
            'learning_outcomes' => 'nullable|array',
            'learning_outcomes.*' => 'nullable|string|max:255',
            'requirements' => 'nullable|array',
            'requirements.*' => 'nullable|string|max:255',
            'target_audience' => 'nullable|array',
            'target_audience.*' => 'nullable|string|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ], [
            'course_title.required' => 'Judul kursus harus diisi.',
            'course_title.min' => 'Judul kursus minimal 10 karakter.',
            'course_title.max' => 'Judul kursus maksimal 255 karakter.',
            'course_category.required' => 'Kategori kursus harus dipilih.',
            'course_category.exists' => 'Kategori yang dipilih tidak valid.',
            'course_description.required' => 'Deskripsi kursus harus diisi.',
            'course_description.min' => 'Deskripsi kursus minimal 50 karakter.',
            'course_description.max' => 'Deskripsi kursus maksimal 1000 karakter.',
            'course_level.required' => 'Level kursus harus dipilih.',
            'course_level.in' => 'Level kursus tidak valid.',
            'course_duration.required' => 'Estimasi durasi harus diisi.',
            'course_duration.integer' => 'Estimasi durasi harus berupa angka.',
            'course_duration.min' => 'Estimasi durasi minimal 1 jam.',
            'course_duration.max' => 'Estimasi durasi maksimal 500 jam.',
            'course_type.required' => 'Tipe kursus harus dipilih.',
            'course_type.in' => 'Tipe kursus tidak valid.',
            'course_price.required_if' => 'Harga kursus harus diisi untuk kursus berbayar.',
            'course_price.numeric' => 'Harga kursus harus berupa angka.',
            'course_price.min' => 'Harga minimum untuk kursus berbayar adalah IDR 30.000.',
            'learning_outcomes.*.max' => 'Tujuan pembelajaran maksimal 255 karakter.',
            'requirements.*.max' => 'Prasyarat maksimal 255 karakter.',
            'target_audience.*.max' => 'Target audience maksimal 255 karakter.',
            'thumbnail.image' => 'File thumbnail harus berupa gambar.',
            'thumbnail.mimes' => 'Format thumbnail harus JPG, PNG, atau GIF.',
            'thumbnail.max' => 'Ukuran thumbnail maksimal 10MB.',
        ]);

        // Additional validation for course price step (kelipatan 1000)
        if ($validated['course_type'] === 'paid' && isset($validated['course_price'])) {
            $price = (float) $validated['course_price'];
            if ($price % 1000 !== 0) {
                return back()->withInput()->withErrors(['course_price' => 'Harga kursus harus kelipatan 1000.']);
            }
        }

        // Filter out empty values from arrays
        $learningOutcomes = array_filter($request->input('learning_outcomes', []), function($value) {
            return !empty(trim($value));
        });

        $requirements = array_filter($request->input('requirements', []), function($value) {
            return !empty(trim($value));
        });

        $targetAudience = array_filter($request->input('target_audience', []), function($value) {
            return !empty(trim($value));
        });

        try {
            // Determine if course is free and set price
            $isFree = $validated['course_type'] === 'free';
            $price = $isFree ? 0 : ($validated['course_price'] ?? 0);

            // Update the course
            $course->update([
                'category_id' => $validated['course_category'],
                'title' => $validated['course_title'],
                'description' => $validated['course_description'],
                'level' => $validated['course_level'],
                'duration' => $validated['course_duration'],
                'price' => $price,
                'is_free' => $isFree,
                'learning_outcomes' => $learningOutcomes,
                'requirements' => $requirements,
                'target_audience' => $targetAudience,
            ]);

            // Handle thumbnail upload
            if ($request->hasFile('thumbnail')) {
                // Delete old thumbnail if exists
                if ($course->thumbnail) {
                    Storage::disk('public')->delete($course->thumbnail);
                }

                $thumbnailPath = $request->file('thumbnail')->store(
                    "user/{$user->id}/course/{$course->id}/thumbnail",
                    'public'
                );

                $course->update(['thumbnail' => $thumbnailPath]);
            }

            return redirect()->route('tutor.courses', ['tab' => 'drafts'])->with('success', 'Kursus berhasil diperbarui!');

        } catch (\Exception $e) {
            return back()->withInput()->with('error', 'Terjadi kesalahan saat memperbarui kursus. Silakan coba lagi.');
        }
    }

    /**
     * Show the tutor students page.
     */
    public function students()
    {
        $user = Auth::user();

        // Get real enrolled students data from database
        $enrolledStudents = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) {
                return [
                    'id' => $enrollment->user->id,
                    'name' => $enrollment->user->name,
                    'email' => $enrollment->user->email,
                    'course_title' => $enrollment->course->title,
                    'course_id' => $enrollment->course->id,
                    'enrollment_date' => $enrollment->enrolled_at->format('d M Y'),
                    'last_active' => $enrollment->updated_at->diffForHumans(),
                    'status' => $enrollment->status,
                    'progress' => 0, // TODO: Implement progress tracking
                ];
            });

        // Get tutor's courses for the filter dropdown
        $tutorCourses = \App\Models\Course::where('tutor_id', $user->id)
            ->select('id', 'title')
            ->orderBy('title')
            ->get();

        return view('tutor.students', compact('user', 'enrolledStudents', 'tutorCourses'));
    }

    /**
     * Show the tutor analytics page.
     */
    public function analytics(Request $request)
    {
        $user = Auth::user();

        // Get filter period (default to 6 months)
        $months = $request->get('months', 6);
        $months = in_array($months, [3, 6, 12]) ? $months : 6;

        // Get real analytics data from database
        $tutorCourses = \App\Models\Course::where('tutor_id', $user->id)->get();
        $courseIds = $tutorCourses->pluck('id');

        // Course performance metrics - use actual enrollment data for revenue
        $coursePerformance = $tutorCourses->map(function($course) {
            // Calculate actual revenue from enrollments
            $actualRevenue = \App\Models\CourseEnrollment::where('course_id', $course->id)
                ->where('status', 'active')
                ->sum('amount_paid');

            // Get actual student count from enrollments
            $actualStudents = \App\Models\CourseEnrollment::where('course_id', $course->id)
                ->where('status', 'active')
                ->count();

            return [
                'course_id' => $course->id,
                'title' => $course->title,
                'total_students' => $actualStudents,
                'average_rating' => $course->average_rating ?: 0,
                'total_reviews' => $course->total_reviews,
                'revenue' => $actualRevenue,
                'status' => $course->status
            ];
        });

        // Monthly enrollment trends (dynamic months)
        $enrollmentTrends = [];
        for ($i = $months - 1; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $enrollments = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->count();

            $enrollmentTrends[] = [
                'month' => $month->format('M Y'),
                'enrollments' => $enrollments
            ];
        }

        // Revenue analytics (dynamic months)
        $revenueAnalytics = [];
        for ($i = $months - 1; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->sum('amount_paid');

            $revenueAnalytics[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue ?: 0
            ];
        }

        // Student engagement metrics
        $totalStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->distinct('user_id')->count('user_id');

        $activeStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->where('status', 'active')
        ->distinct('user_id')
        ->count('user_id');

        $completedStudents = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->where('status', 'completed')
        ->distinct('user_id')
        ->count('user_id');

        // Calculate quiz success rate from actual quiz attempts
        $totalQuizAttempts = \App\Models\QuizAttempt::whereHas('quiz.lesson.course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->count();

        $successfulQuizAttempts = \App\Models\QuizAttempt::whereHas('quiz.lesson.course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->where('score_percentage', '>=', 70)->count(); // Assuming 70% is passing

        $quizSuccessRate = $totalQuizAttempts > 0 ? round(($successfulQuizAttempts / $totalQuizAttempts) * 100, 1) : 0;

        // Calculate average study time from lesson progress (convert seconds to minutes)
        $avgStudyTimeSeconds = \App\Models\LessonProgress::whereHas('lesson.course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->whereNotNull('completed_at')->avg('time_spent_seconds') ?: 0;
        $avgStudyTimeMinutes = $avgStudyTimeSeconds / 60;

        // Count active discussions (using Nala chat as proxy for discussions)
        $activeDiscussions = \App\Models\NalaChatConversation::where('user_id', $user->id)
            ->where('status', 'active')
            ->whereMonth('last_message_at', now()->month)
            ->count();

        $analyticsData = [
            'course_performance' => $coursePerformance,
            'enrollment_trends' => $enrollmentTrends,
            'revenue_analytics' => $revenueAnalytics,
            'student_engagement' => [
                'total_students' => $totalStudents,
                'active_students' => $activeStudents,
                'completed_students' => $completedStudents,
                'completion_rate' => $totalStudents > 0 ? round(($completedStudents / $totalStudents) * 100, 1) : 0,
                'quiz_success_rate' => $quizSuccessRate,
                'avg_study_time_minutes' => round($avgStudyTimeMinutes),
                'active_discussions' => $activeDiscussions
            ],
            'summary_stats' => [
                'total_courses' => $tutorCourses->count(),
                'published_courses' => $tutorCourses->where('status', 'published')->count(),
                'total_enrollments' => \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->count(),
                'total_revenue' => \App\Models\CoursePurchase::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->where('status', 'active')->sum('amount_paid'),
                'average_rating' => $tutorCourses->where('status', 'published')->avg('average_rating') ?: 0
            ]
        ];

        // If this is an AJAX request, return JSON
        if ($request->ajax()) {
            return response()->json([
                'enrollment_trends' => $enrollmentTrends,
                'revenue_analytics' => $revenueAnalytics
            ]);
        }

        return view('tutor.analytics', compact('user', 'analyticsData', 'months'));
    }

    /**
     * Show the tutor earnings page.
     */
    public function earnings()
    {
        $user = Auth::user();

        // Get real earnings data from database
        $totalEarnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })->sum('amount_paid');

        // Calculate monthly earnings (current month)
        $monthlyEarnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
            $query->where('tutor_id', $user->id);
        })
        ->whereYear('enrolled_at', now()->year)
        ->whereMonth('enrolled_at', now()->month)
        ->sum('amount_paid');

        // Get transaction history (recent enrollments)
        $transactionHistory = \App\Models\CourseEnrollment::with(['user', 'course'])
            ->whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->orderBy('enrolled_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function($enrollment) {
                return [
                    'id' => $enrollment->id,
                    'student_name' => $enrollment->user->name,
                    'course_title' => $enrollment->course->title,
                    'amount' => $enrollment->amount_paid,
                    'date' => $enrollment->enrolled_at->format('d M Y'),
                    'status' => $enrollment->status,
                    'payment_method' => $enrollment->payment_method ?: 'N/A'
                ];
            });

        // Calculate earnings by course
        $earningsByCourse = \App\Models\Course::where('tutor_id', $user->id)
            ->with(['enrollments'])
            ->get()
            ->map(function($course) {
                $totalRevenue = $course->enrollments->sum('amount_paid');
                $totalEnrollments = $course->enrollments->count();

                return [
                    'course_id' => $course->id,
                    'title' => $course->title,
                    'price' => $course->price,
                    'total_enrollments' => $totalEnrollments,
                    'total_revenue' => $totalRevenue,
                    'status' => $course->status
                ];
            })
            ->sortByDesc('total_revenue');

        // Monthly earnings trend (last 6 months)
        $monthlyTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $earnings = \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                $query->where('tutor_id', $user->id);
            })
            ->whereYear('enrolled_at', $month->year)
            ->whereMonth('enrolled_at', $month->month)
            ->sum('amount_paid');

            $monthlyTrend[] = [
                'month' => $month->format('M Y'),
                'earnings' => $earnings ?: 0
            ];
        }

        // Calculate average monthly earnings from the trend data
        $avgMonthlyEarnings = count($monthlyTrend) > 0 ?
            round(collect($monthlyTrend)->avg('earnings')) : 0;

        // Calculate platform fee based on actual course settings
        $platformFeeTotal = 0;
        $tutorEarningsTotal = 0;

        foreach ($earningsByCourse as $courseEarning) {
            $course = \App\Models\Course::find($courseEarning['course_id']);
            if ($course) {
                $platformFee = ($courseEarning['total_revenue'] * $course->platform_fee_percentage) / 100;
                $platformFeeTotal += $platformFee;
                $tutorEarningsTotal += $courseEarning['total_revenue'] - $platformFee;
            }
        }

        $earningsData = [
            'total_earnings' => $totalEarnings,
            'monthly_earnings' => $monthlyEarnings,
            'avg_monthly_earnings' => $avgMonthlyEarnings,
            'pending_payouts' => $tutorEarningsTotal, // Actual tutor earnings after platform fee
            'transaction_history' => $transactionHistory,
            'earnings_by_course' => $earningsByCourse,
            'monthly_trend' => $monthlyTrend,
            'summary_stats' => [
                'total_students' => \App\Models\CourseEnrollment::whereHas('course', function($query) use ($user) {
                    $query->where('tutor_id', $user->id);
                })->distinct('user_id')->count('user_id'),
                'average_course_price' => \App\Models\Course::where('tutor_id', $user->id)->avg('price') ?: 0,
                'platform_fee' => $platformFeeTotal, // Actual platform fee based on course settings
                'tutor_earnings' => $tutorEarningsTotal // Net tutor earnings
            ]
        ];

        return view('tutor.earnings', compact('user', 'earningsData'));
    }

    /**
     * Request a payout.
     */
    public function requestPayout(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated.'
                ], 401);
            }

            // Get payment settings
            $paymentSettings = $user->tutorPaymentSettings;
            if (!$paymentSettings) {
                return response()->json([
                    'success' => false,
                    'message' => 'Silakan lengkapi pengaturan pembayaran terlebih dahulu.'
                ], 400);
            }

            // Check if payment settings are complete
            if (!$paymentSettings->isCompleteForPayouts()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pengaturan pembayaran belum lengkap. Silakan lengkapi informasi bank dan pajak.'
                ], 400);
            }

            // Calculate available earnings from course payments only
            $availableEarnings = Payment::where('payable_type', Course::class)
                ->whereIn('payable_id', function($query) use ($user) {
                    $query->select('id')
                          ->from('courses')
                          ->where('tutor_id', $user->id);
                })
                ->where('status', 'completed')
                ->sum('tutor_earnings') ?? 0;

            // Subtract already requested payouts
            $requestedPayouts = PayoutRequest::where('tutor_id', $user->id)
                ->whereIn('status', ['pending', 'processing', 'approved'])
                ->sum('amount') ?? 0;

            $availableAmount = max(0, $availableEarnings - $requestedPayouts);

            // Check if user has sufficient balance
            if ($availableAmount < $paymentSettings->minimum_payout) {
                return response()->json([
                    'success' => false,
                    'message' => 'Saldo tersedia tidak mencukupi untuk minimum payout. Saldo tersedia: IDR ' . number_format($availableAmount, 0, ',', '.') . ', Minimum payout: IDR ' . number_format($paymentSettings->minimum_payout, 0, ',', '.')
                ], 400);
            }

            // Validate request with custom error messages
            $validator = Validator::make($request->all(), [
                'amount' => [
                    'required',
                    'numeric',
                    'min:' . $paymentSettings->minimum_payout,
                    'max:' . $availableAmount
                ],
                'notes' => 'nullable|string|max:500'
            ], [
                'amount.required' => 'Jumlah payout harus diisi.',
                'amount.numeric' => 'Jumlah payout harus berupa angka.',
                'amount.min' => 'Jumlah payout minimal IDR ' . number_format($paymentSettings->minimum_payout, 0, ',', '.'),
                'amount.max' => 'Jumlah payout tidak boleh melebihi saldo tersedia (IDR ' . number_format($availableAmount, 0, ',', '.') . ')',
                'notes.max' => 'Catatan tidak boleh lebih dari 500 karakter.'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }

            $requestedAmount = $request->amount;
            $platformFee = PayoutRequest::calculatePlatformFee($requestedAmount);
            $netAmount = $requestedAmount - $platformFee;

            DB::beginTransaction();

            // Create payout request
            $payoutRequest = PayoutRequest::create([
                'tutor_id' => $user->id,
                'request_id' => PayoutRequest::generateRequestId(),
                'amount' => $requestedAmount,
                'platform_fee' => $platformFee,
                'net_amount' => $netAmount,
                'currency' => 'IDR',
                'status' => 'pending',
                'tutor_notes' => $request->notes,
                'payment_method' => $paymentSettings->preferred_method,
                'payment_details' => $paymentSettings->getPreferredPaymentDetails(),
                'requested_at' => now(),
            ]);

            DB::commit();

            // Log successful payout request
            Log::info('Payout request created successfully', [
                'user_id' => $user->id,
                'request_id' => $payoutRequest->request_id,
                'amount' => $requestedAmount,
                'net_amount' => $netAmount
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Permintaan payout berhasil diajukan. Tim kami akan memproses dalam 1-3 hari kerja.',
                'payout_request' => [
                    'id' => $payoutRequest->id,
                    'request_id' => $payoutRequest->request_id,
                    'amount' => $payoutRequest->formatted_amount,
                    'net_amount' => $payoutRequest->formatted_net_amount,
                    'status' => $payoutRequest->status_name,
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->validator->errors()->first()
            ], 422);
        } catch (\Exception $e) {
            DB::rollback();

            Log::error('Error processing payout request: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memproses permintaan payout. Silakan coba lagi atau hubungi support.'
            ], 500);
        }
    }

    /**
     * Get payout request data for modal.
     */
    public function getPayoutData()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Calculate available earnings from course payments only
            $availableEarnings = Payment::where('payable_type', Course::class)
                ->whereIn('payable_id', function($query) use ($user) {
                    $query->select('id')
                          ->from('courses')
                          ->where('tutor_id', $user->id);
                })
                ->where('status', 'completed')
                ->sum('tutor_earnings') ?? 0;

            // Subtract already requested payouts
            $requestedPayouts = PayoutRequest::where('tutor_id', $user->id)
                ->whereIn('status', ['pending', 'processing', 'approved'])
                ->sum('amount') ?? 0;

            $availableAmount = max(0, $availableEarnings - $requestedPayouts);

            // Get payment settings
            $paymentSettings = $user->tutorPaymentSettings;
            $minimumPayout = $paymentSettings ? $paymentSettings->minimum_payout : 100000;

            // Check if payment settings are complete
            $hasPaymentSettings = $paymentSettings !== null;
            $paymentSettingsComplete = $hasPaymentSettings ? $paymentSettings->isCompleteForPayouts() : false;

            // Get recent payout requests
            $recentPayouts = PayoutRequest::where('tutor_id', $user->id)
                ->orderBy('requested_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function($payout) {
                    return [
                        'request_id' => $payout->request_id,
                        'amount' => $payout->formatted_amount,
                        'status' => $payout->status_name,
                        'requested_at' => $payout->requested_at->format('d M Y'),
                    ];
                });

            return response()->json([
                'success' => true,
                'available_amount' => $availableAmount,
                'formatted_available_amount' => 'IDR ' . number_format($availableAmount, 0, ',', '.'),
                'minimum_payout' => $minimumPayout,
                'formatted_minimum_payout' => 'IDR ' . number_format($minimumPayout, 0, ',', '.'),
                'can_request_payout' => $availableAmount >= $minimumPayout && $paymentSettingsComplete,
                'has_payment_settings' => $hasPaymentSettings,
                'payment_settings_complete' => $paymentSettingsComplete,
                'preferred_method' => $paymentSettings ? $paymentSettings->preferred_method_name : 'Belum diatur',
                'recent_payouts' => $recentPayouts,
                'message' => $this->getPayoutStatusMessage($availableAmount, $minimumPayout, $hasPaymentSettings, $paymentSettingsComplete),
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading payout data: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memuat data payout. Silakan coba lagi atau hubungi support.'
            ], 500);
        }
    }

    /**
     * Get appropriate message for payout status.
     */
    private function getPayoutStatusMessage($availableAmount, $minimumPayout, $hasPaymentSettings, $paymentSettingsComplete)
    {
        if (!$hasPaymentSettings) {
            return 'Silakan lengkapi pengaturan pembayaran terlebih dahulu untuk dapat mengajukan payout.';
        }

        if (!$paymentSettingsComplete) {
            return 'Pengaturan pembayaran belum lengkap. Silakan lengkapi informasi bank dan pajak.';
        }

        if ($availableAmount < $minimumPayout) {
            return 'Saldo tersedia belum mencapai minimum payout. Terus berkarya untuk mencapai minimum payout!';
        }

        return 'Anda dapat mengajukan payout sekarang.';
    }

    /**
     * Update payment settings.
     */
    public function updatePaymentSettings(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'payment_method' => 'required|in:bank_transfer,gopay,ovo,dana,shopeepay',
            'bank_name' => 'required_if:payment_method,bank_transfer|string|max:100',
            'bank_account_number' => 'required_if:payment_method,bank_transfer|string|max:50',
            'bank_account_holder' => 'required_if:payment_method,bank_transfer|string|max:100',
            'bank_branch' => 'nullable|string|max:100',
            'gopay_number' => 'required_if:payment_method,gopay|string|max:20',
            'ovo_number' => 'required_if:payment_method,ovo|string|max:20',
            'dana_number' => 'required_if:payment_method,dana|string|max:20',
            'shopeepay_number' => 'required_if:payment_method,shopeepay|string|max:20',
            'npwp_number' => 'required|string|max:20',
            'npwp_name' => 'required|string|max:100',
            'npwp_address' => 'nullable|string|max:255',
            'tax_status' => 'required|in:non_pkp,pkp',
            'minimum_payout' => 'required|numeric|min:100000|max:********',
            'payout_frequency' => 'required|in:manual,weekly,monthly',
            'auto_payout_enabled' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            // Get or create payment settings
            $paymentSettings = $user->tutorPaymentSettings ?? new TutorPaymentSettings();
            $paymentSettings->tutor_id = $user->id;
            $paymentSettings->preferred_method = $request->payment_method;
            $paymentSettings->minimum_payout = $request->minimum_payout;
            $paymentSettings->payout_frequency = $request->payout_frequency;
            $paymentSettings->auto_payout_enabled = $request->boolean('auto_payout_enabled');

            // Bank details
            if ($request->payment_method === 'bank_transfer') {
                $paymentSettings->bank_name = $request->bank_name;
                $paymentSettings->bank_account_number = $request->bank_account_number;
                $paymentSettings->bank_account_holder = $request->bank_account_holder;
                $paymentSettings->bank_branch = $request->bank_branch;
            }

            // E-wallet details
            if ($request->payment_method === 'gopay') {
                $paymentSettings->gopay_number = $request->gopay_number;
            } elseif ($request->payment_method === 'ovo') {
                $paymentSettings->ovo_number = $request->ovo_number;
            } elseif ($request->payment_method === 'dana') {
                $paymentSettings->dana_number = $request->dana_number;
            } elseif ($request->payment_method === 'shopeepay') {
                $paymentSettings->shopeepay_number = $request->shopeepay_number;
            }

            // Tax information
            $paymentSettings->npwp_number = $request->npwp_number;
            $paymentSettings->npwp_name = $request->npwp_name;
            $paymentSettings->npwp_address = $request->npwp_address;
            $paymentSettings->tax_status = $request->tax_status;

            $paymentSettings->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan pembayaran berhasil disimpan.',
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan pengaturan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment settings data.
     */
    public function getPaymentSettings()
    {
        $user = Auth::user();
        $paymentSettings = $user->tutorPaymentSettings;

        if (!$paymentSettings) {
            return response()->json([
                'payment_settings' => null,
                'has_settings' => false,
            ]);
        }

        return response()->json([
            'payment_settings' => [
                'preferred_method' => $paymentSettings->preferred_method,
                'bank_name' => $paymentSettings->bank_name,
                'bank_account_number' => $paymentSettings->bank_account_number,
                'bank_account_holder' => $paymentSettings->bank_account_holder,
                'bank_branch' => $paymentSettings->bank_branch,
                'gopay_number' => $paymentSettings->gopay_number,
                'ovo_number' => $paymentSettings->ovo_number,
                'dana_number' => $paymentSettings->dana_number,
                'shopeepay_number' => $paymentSettings->shopeepay_number,
                'npwp_number' => $paymentSettings->npwp_number,
                'npwp_name' => $paymentSettings->npwp_name,
                'npwp_address' => $paymentSettings->npwp_address,
                'tax_status' => $paymentSettings->tax_status,
                'minimum_payout' => $paymentSettings->minimum_payout,
                'payout_frequency' => $paymentSettings->payout_frequency,
                'auto_payout_enabled' => $paymentSettings->auto_payout_enabled,
                'bank_verified' => $paymentSettings->bank_verified,
                'tax_verified' => $paymentSettings->tax_verified,
            ],
            'has_settings' => true,
            'is_complete' => $paymentSettings->isCompleteForPayouts(),
        ]);
    }

    /**
     * Get payout history for the tutor.
     */
    public function getPayoutHistory()
    {
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }

        $payoutRequests = PayoutRequest::where('tutor_id', $user->id)
            ->orderBy('requested_at', 'desc')
            ->get()
            ->map(function($payout) {
                return [
                    'id' => $payout->id,
                    'request_id' => $payout->request_id,
                    'amount' => $payout->formatted_amount,
                    'net_amount' => $payout->formatted_net_amount,
                    'platform_fee' => 'IDR ' . number_format($payout->platform_fee, 0, ',', '.'),
                    'status' => $payout->status,
                    'status_name' => $payout->status_name,
                    'payment_method' => $payout->payment_method_name,
                    'requested_at' => $payout->requested_at->format('d M Y H:i'),
                    'processed_at' => $payout->processed_at ? $payout->processed_at->format('d M Y H:i') : null,
                    'paid_at' => $payout->paid_at ? $payout->paid_at->format('d M Y H:i') : null,
                    'notes' => $payout->notes,
                    'tutor_notes' => $payout->tutor_notes,
                ];
            });

        return response()->json([
            'payout_requests' => $payoutRequests,
            'total_requests' => $payoutRequests->count(),
        ]);
    }

    /**
     * Show the tutor profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('tutor.profile', compact('user'));
    }

    /**
     * Show the tutor settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        return view('tutor.settings', compact('user'));
    }

    /**
     * Update tutor profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        // Handle delete profile picture request
        if ($request->has('delete_profile_picture')) {
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
                $user->update(['profile_picture' => null]);
            }
            return back()->with('profile_success', 'Foto profil berhasil dihapus!');
        }

        // Handle profile picture upload only
        if ($request->hasFile('profile_picture')) {
            $request->validate([
                'profile_picture' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            ], [
                'profile_picture.image' => 'File harus berupa gambar.',
                'profile_picture.max' => 'Ukuran file maksimal 2MB.',
                'profile_picture.mimes' => 'Format file harus JPG, PNG, atau JPEG.',
            ]);

            // Delete old profile picture if exists
            if ($user->profile_picture) {
                \App\Services\FileStorageService::deletePublicFile($user->profile_picture);
            }

            $profilePicturePath = \App\Services\FileStorageService::storePublicUserFile(
                $request->file('profile_picture'),
                $user->id,
                'profile'
            );

            $user->update(['profile_picture' => $profilePicturePath]);
            return back()->with('profile_success', 'Foto profil berhasil diperbarui!');
        }

        // Handle other profile data
        $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'long_description' => 'nullable|string|max:10000',
        ]);

        $user->update([
            'name' => $request->display_name,
        ]);

        // Update tutor profile if exists
        if ($user->tutorProfile) {
            $user->tutorProfile->update([
                'description' => $request->description,
                'long_description' => $request->long_description,
            ]);
        }

        return back()->with('success', 'Profil tutor berhasil diperbarui!');
    }

    /**
     * Publish a course.
     */
    public function publishCourse(Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Comprehensive validation before publishing
        $validationErrors = [];

        // Check for chapters
        $hasChapters = $course->chapters()->count() > 0;
        if (!$hasChapters) {
            $validationErrors[] = 'Kursus harus memiliki minimal 1 bab.';
        }

        // Check for lessons
        $hasLessons = $course->lessons()->count() > 0;
        if (!$hasLessons) {
            $validationErrors[] = 'Kursus harus memiliki minimal 1 materi pembelajaran.';
        }

        // Check for published content
        $hasPublishedContent = $course->lessons()->where('is_published', true)->count() > 0;
        if (!$hasPublishedContent) {
            $validationErrors[] = 'Kursus harus memiliki minimal 1 materi yang dipublikasikan.';
        }

        // Check for basic course information
        if (empty($course->thumbnail)) {
            $validationErrors[] = 'Kursus harus memiliki thumbnail.';
        }

        if (empty($course->description)) {
            $validationErrors[] = 'Kursus harus memiliki deskripsi.';
        }

        if (empty($course->learning_outcomes)) {
            $validationErrors[] = 'Kursus harus memiliki tujuan pembelajaran.';
        }

        // If there are validation errors, return with error messages
        if (!empty($validationErrors)) {
            return back()->with('error', 'Tidak dapat menerbitkan kursus:<br>' . implode('<br>', $validationErrors));
        }

        // Update course status to published
        $course->update([
            'status' => 'published',
            'published_at' => now(),
        ]);

        // Automatically publish all chapters and lessons when course is published
        $course->chapters()->update(['is_published' => true]);
        $course->lessons()->update(['is_published' => true]);

        // Update course statistics
        $course->updateStatistics();

        // Redirect to course preview with success message
        return redirect()->route('tutor.courses.preview', $course)
            ->with('success', 'Kursus berhasil diterbitkan! Sekarang siswa dapat mendaftar di kursus Anda.');
    }

    /**
     * Save course as draft.
     */
    public function saveDraft(Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Update course status to draft
        $course->update([
            'status' => 'draft',
            'published_at' => null,
        ]);

        // Automatically unpublish all chapters and lessons when course is saved as draft
        $course->chapters()->update(['is_published' => false]);
        $course->lessons()->update(['is_published' => false]);

        // Update course statistics (will be 0 since nothing is published)
        $course->updateStatistics();

        return back()->with('success', 'Kursus berhasil disimpan sebagai draft.');
    }

    /**
     * Show the course publish confirmation page.
     */
    public function showPublishPage(Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Load course data with relationships
        $course->load([
            'chapters' => function ($query) {
                $query->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->orderBy('sort_order');
            },
            'category',
            'tutor'
        ]);

        // Calculate course statistics
        $totalChapters = $course->chapters()->count();
        $totalLessons = $course->lessons()->count();
        $publishedLessons = $course->lessons()->where('is_published', true)->count();
        $totalDuration = $course->lessons()->sum('duration_minutes');

        // Validation checks
        $validationChecks = [
            'has_chapters' => $totalChapters > 0,
            'has_lessons' => $totalLessons > 0,
            'has_published_content' => $publishedLessons > 0,
            'has_thumbnail' => !empty($course->thumbnail),
            'has_description' => !empty($course->description),
            'has_learning_outcomes' => !empty($course->learning_outcomes),
        ];

        $canPublish = collect($validationChecks)->every(fn($check) => $check);

        return view('tutor.publish-course', compact(
            'course',
            'totalChapters',
            'totalLessons',
            'publishedLessons',
            'totalDuration',
            'validationChecks',
            'canPublish'
        ));
    }

    /**
     * Show course preview as students would see it.
     */
    public function previewCourse(Course $course)
    {
        $user = Auth::user();

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== $user->id) {
            abort(403, 'Unauthorized access to course.');
        }

        // Load course data similar to how students see it
        $course->load([
            'tutor',
            'category',
            'chapters' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons.quiz',
            'chapters.lessons.assignment'
        ]);

        // Calculate course statistics
        $totalLessons = $course->chapters->sum(function ($chapter) {
            return $chapter->lessons->count();
        });

        $totalDuration = $course->chapters->sum(function ($chapter) {
            return $chapter->lessons->sum('duration_minutes');
        });

        // Get similar courses for recommendations
        $similarCourses = \App\Models\Course::published()
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->limit(4)
            ->get();

        return view('tutor.course-preview', compact(
            'course',
            'totalLessons',
            'totalDuration',
            'similarCourses'
        ));
    }

    /**
     * Show tutor membership page.
     */
    public function membership()
    {
        $user = Auth::user();
        $activeMembership = $user->activeMembership;

        // Load the membership plan relationship if membership exists
        if ($activeMembership) {
            $activeMembership->load('membershipPlan');
        }

        $membershipHistory = UserMembership::where('user_id', $user->id)
            ->with('membershipPlan', 'payment')
            ->latest()
            ->get();

        return view('tutor.membership.index', compact('activeMembership', 'membershipHistory'));
    }
}
