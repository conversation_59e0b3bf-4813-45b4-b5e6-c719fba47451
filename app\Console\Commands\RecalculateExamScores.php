<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExamAttempt;
use Illuminate\Support\Facades\DB;

class RecalculateExamScores extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'exam:recalculate-scores';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate score percentages for all exam attempts using correct answers vs total questions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to recalculate exam scores...');
        
        $attempts = ExamAttempt::whereNotNull('total_questions')
            ->whereNotNull('correct_answers')
            ->get();
            
        $this->info("Found {$attempts->count()} exam attempts to recalculate.");
        
        $updated = 0;
        
        foreach ($attempts as $attempt) {
            try {
                // Calculate new score percentage based on correct answers vs total questions
                $newScorePercentage = $attempt->total_questions > 0 
                    ? ($attempt->correct_answers / $attempt->total_questions) * 100 
                    : 0;
                
                // Check if the score needs updating (avoid unnecessary updates)
                if (abs($attempt->score_percentage - $newScorePercentage) > 0.01) {
                    // Update the attempt with new score percentage and pass status
                    $attempt->update([
                        'score_percentage' => $newScorePercentage,
                        'is_passed' => $newScorePercentage >= $attempt->exam->passing_score,
                    ]);
                    
                    $updated++;
                    
                    $this->line("Updated attempt {$attempt->id}: {$attempt->score_percentage}% -> {$newScorePercentage}%");
                }
            } catch (\Exception $e) {
                $this->error("Failed to update attempt {$attempt->id}: {$e->getMessage()}");
            }
        }
        
        $this->info("Recalculation completed. Updated {$updated} exam attempts.");
        
        return 0;
    }
}