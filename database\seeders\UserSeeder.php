<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use App\Models\MembershipPlan;
use App\Models\UserMembership;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin user
        $superAdmin = User::create([
            'name' => 'Super Admin Ngambiskuy',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'bio' => 'Founder dan lead educator di Ngambiskuy Platform. Dosen dan peneliti dengan 15+ tahun pengalaman di bidang Computer Science.',
            'location' => 'Jakarta, Indonesia',
            'job_title' => 'Founder & CEO',
            'company' => 'Ngambiskuy Platform',
            'website' => 'https://ngambiskuy.com',
            'linkedin_url' => 'https://linkedin.com/in/budisantoso',
            'skills' => ['AI', 'Machine Learning', 'Software Engineering', 'Research', 'Education'],
            // Education & Learning
            'pendidikan' => 's3',
            'institusi_pendidikan' => 'Institut Teknologi Bandung',
            'jurusan' => 'Teknik Informatika',
            'tahun_lulus' => 2008,
            'minat_belajar' => ['AI/ML', 'Programming', 'Business'],
            // AI Career Assistant Data
            'experience_years' => 15,
            'career_goals' => [
                'short_term' => 'Expand Ngambiskuy platform to 100k users',
                'long_term' => 'Become leading edtech platform in Southeast Asia'
            ],
            'industry_interests' => ['Education Technology', 'Artificial Intelligence', 'Software Development'],
            'work_preferences' => ['hybrid', 'leadership', 'mentoring'],
        ]);

        // Assign all roles to super admin
        $superAdmin->syncRoles([Role::USER, Role::TUTOR, Role::ADMIN, Role::SUPERADMIN]);

        // Assign free membership to super admin
        $this->assignFreeMembership($superAdmin);

        // Create admin user
        $admin = User::create([
            'name' => 'Admin Ngambiskuy',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Assign admin and tutor roles
        $admin->syncRoles([Role::USER, Role::TUTOR, Role::ADMIN]);

        // Assign free membership to admin
        $this->assignFreeMembership($admin);

        // Create tutor user with membership
        $tutor1 = User::create([
            'name' => 'Sari Dewi',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'tutor_status' => 'approved',
            'bio' => 'Passionate Data Scientist dengan 8+ tahun pengalaman di industri teknologi. Suka berbagi ilmu dan membantu orang lain berkembang di bidang data science.',
            'location' => 'Jakarta, Indonesia',
            'job_title' => 'Senior Data Scientist',
            'company' => 'TechCorp Indonesia',
            'website' => 'https://saridewi.dev',
            'linkedin_url' => 'https://linkedin.com/in/saridewi',
            'github_url' => 'https://github.com/saridewi',
            'twitter_url' => 'https://twitter.com/saridewi_ds',
            'skills' => ['Python', 'Machine Learning', 'TensorFlow', 'Data Analysis', 'SQL', 'Statistics'],
            // Education & Learning
            'pendidikan' => 's2',
            'institusi_pendidikan' => 'Universitas Indonesia',
            'jurusan' => 'Statistika',
            'tahun_lulus' => 2016,
            'minat_belajar' => ['Data Science', 'AI/ML', 'Programming'],
            // AI Career Assistant Data
            'experience_years' => 8,
            'career_goals' => [
                'short_term' => 'Lead data science team',
                'long_term' => 'Become Chief Data Officer'
            ],
            'industry_interests' => ['Technology', 'Finance', 'Healthcare'],
            'work_preferences' => ['remote', 'flexible_hours', 'team_collaboration'],
        ]);

        // Assign tutor role
        $tutor1->syncRoles([Role::USER, Role::TUTOR]);

        // Assign Standard membership plan to Sari for testing
        $standardPlan = MembershipPlan::where('slug', 'standard')->first();
        if ($standardPlan) {
            // Remove any existing memberships for this user first
            $tutor1->memberships()->delete();

            $startsAt = now();
            $expiresAt = $startsAt->copy()->addMonths($standardPlan->duration_months);

            UserMembership::create([
                'user_id' => $tutor1->id,
                'membership_plan_id' => $standardPlan->id,
                'status' => 'active',
                'starts_at' => $startsAt,
                'expires_at' => $expiresAt,
                'nala_prompts_allocated' => $standardPlan->nala_prompts ?? 0,
                'nala_prompts_remaining' => $standardPlan->nala_prompts ?? 0,
                'has_unlimited_nala' => $standardPlan->has_unlimited_nala,
                'has_ice_full' => $standardPlan->has_ice_full,
                'has_ai_teaching_assistants_courses' => $standardPlan->has_ai_teaching_assistants_courses,
                'has_ai_teaching_assistants_tryout' => $standardPlan->has_ai_teaching_assistants_tryout,
                'has_free_certifications' => $standardPlan->has_free_certifications,
                'has_blog_access' => $standardPlan->has_blog_access,
                'career_path_predictor' => $standardPlan->career_path_predictor,
                'has_priority_support' => $standardPlan->has_priority_support,
            ]);
        }

        // Create another tutor
        $tutor2 = User::create([
            'name' => 'Ahmad Rahman',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'tutor_status' => 'approved',
            'bio' => 'Full-Stack Developer yang passionate dalam web development dan cloud computing. Mentor berpengalaman dengan 1500+ siswa.',
            'location' => 'Bandung, Indonesia',
            'job_title' => 'Lead Full-Stack Developer',
            'company' => 'StartupTech',
            'website' => 'https://ahmadrahman.dev',
            'linkedin_url' => 'https://linkedin.com/in/ahmadrahman',
            'github_url' => 'https://github.com/ahmadrahman',
            'youtube_url' => 'https://youtube.com/c/ahmadrahmandev',
            'skills' => ['React', 'Node.js', 'TypeScript', 'AWS', 'Docker', 'MongoDB'],
        ]);

        // Assign tutor role
        $tutor2->syncRoles([Role::USER, Role::TUTOR]);

        // Create test student user
        $testUser = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Assign user role
        $testUser->syncRoles([Role::USER]);

        // Assign free membership to test user
        $this->assignFreeMembership($testUser);

        // Create demo student
        $demoStudent = User::create([
            'name' => 'Budi Santoso',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'bio' => 'Mahasiswa Teknik Informatika yang passionate dalam web development dan mobile app development.',
            'location' => 'Yogyakarta, Indonesia',
            'job_title' => 'Mahasiswa',
            'company' => 'Universitas Gadjah Mada',
            // Education & Learning
            'pendidikan' => 's1',
            'institusi_pendidikan' => 'Universitas Gadjah Mada',
            'jurusan' => 'Teknik Informatika',
            'tahun_lulus' => 2025,
            'minat_belajar' => ['Programming', 'Mobile Dev', 'Design'],
            // AI Career Assistant Data
            'experience_years' => 0,
            'career_goals' => [
                'short_term' => 'Complete internship at tech company',
                'long_term' => 'Become full-stack developer'
            ],
            'industry_interests' => ['Technology', 'Startups', 'Gaming'],
            'work_preferences' => ['remote', 'flexible_hours', 'learning_opportunities'],
        ]);

        // Assign user role
        $demoStudent->syncRoles([Role::USER]);

        // Assign free membership to demo student
        $this->assignFreeMembership($demoStudent);

        // Create additional student users
        $student1 = User::create([
            'name' => 'Rina Sari',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'bio' => 'Fresh graduate yang ingin berkarir di bidang data science dan business intelligence.',
            'location' => 'Surabaya, Indonesia',
            'job_title' => 'Data Analyst',
            'company' => 'PT. Analytics Indonesia',
            // Education & Learning
            'pendidikan' => 's1',
            'institusi_pendidikan' => 'Institut Teknologi Sepuluh Nopember',
            'jurusan' => 'Sistem Informasi',
            'tahun_lulus' => 2023,
            'minat_belajar' => ['Data Science', 'Business', 'AI/ML'],
            // AI Career Assistant Data
            'experience_years' => 1,
            'career_goals' => [
                'short_term' => 'Master data visualization tools',
                'long_term' => 'Become senior data scientist'
            ],
            'industry_interests' => ['Technology', 'Finance', 'E-commerce'],
            'work_preferences' => ['hybrid', 'mentorship', 'growth_opportunities'],
        ]);

        // Assign user role
        $student1->syncRoles([Role::USER]);

        // Assign free membership to student1
        $this->assignFreeMembership($student1);

        $student2 = User::create([
            'name' => 'Doni Pratama',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
            'bio' => 'UI/UX Designer yang ingin memperdalam skill di bidang product design dan user research.',
            'location' => 'Bandung, Indonesia',
            'job_title' => 'UI/UX Designer',
            'company' => 'Creative Studio Bandung',
            // Education & Learning
            'pendidikan' => 'diploma',
            'institusi_pendidikan' => 'Politeknik Negeri Bandung',
            'jurusan' => 'Desain Komunikasi Visual',
            'tahun_lulus' => 2022,
            'minat_belajar' => ['Design', 'UI/UX', 'Marketing'],
            // AI Career Assistant Data
            'experience_years' => 2,
            'career_goals' => [
                'short_term' => 'Lead design team',
                'long_term' => 'Start own design agency'
            ],
            'industry_interests' => ['Design', 'Technology', 'Creative Industry'],
            'work_preferences' => ['creative_freedom', 'collaborative', 'portfolio_building'],
        ]);

        // Assign user role
        $student2->syncRoles([Role::USER]);

        // Assign free membership to student2
        $this->assignFreeMembership($student2);
    }

    /**
     * Assign free membership to a user.
     */
    private function assignFreeMembership(User $user): void
    {
        $freePlan = MembershipPlan::where('slug', 'free')->first();
        if ($freePlan) {
            UserMembership::create([
                'user_id' => $user->id,
                'membership_plan_id' => $freePlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'expires_at' => now()->addMonths($freePlan->duration_months),
                'nala_prompts_allocated' => $freePlan->nala_prompts ?? 0,
                'nala_prompts_remaining' => $freePlan->nala_prompts ?? 0,
                'has_unlimited_nala' => $freePlan->has_unlimited_nala,
                'has_ice_full' => $freePlan->has_ice_full,
                'has_ai_teaching_assistants_courses' => $freePlan->has_ai_teaching_assistants_courses,
                'has_ai_teaching_assistants_tryout' => $freePlan->has_ai_teaching_assistants_tryout,
                'has_free_certifications' => $freePlan->has_free_certifications,
                'has_blog_access' => $freePlan->has_blog_access,
                'career_path_predictor' => $freePlan->career_path_predictor,
                'has_priority_support' => $freePlan->has_priority_support,
            ]);
        }
    }
}
