<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MembershipPlan;

class DebugPlanIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:plan-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug plan ID matching';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::where('email', '<EMAIL>')->first();
        $currentMembership = $user->activeMembership;
        $currentMembership->load('membershipPlan');
        $currentPlanId = $currentMembership->membership_plan_id;

        $this->info("Current Plan ID: {$currentPlanId}");
        $this->info("Current Plan ID Type: " . gettype($currentPlanId));

        $membershipPlans = MembershipPlan::active()
            ->orderBy('type')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('type');

        foreach ($membershipPlans['individual'] ?? [] as $plan) {
            $this->line("\n--- {$plan->name} Plan ---");
            $this->line("Plan ID: {$plan->id}");
            $this->line("Plan ID Type: " . gettype($plan->id));
            $this->line("IDs Match (==): " . ($currentPlanId == $plan->id ? 'YES' : 'NO'));
            $this->line("IDs Match (===): " . ($currentPlanId === $plan->id ? 'YES' : 'NO'));

            if ($plan->slug === 'standard') {
                $this->info("This is the Standard plan that should match!");
            }
        }
    }
}
