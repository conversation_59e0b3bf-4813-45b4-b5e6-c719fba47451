<?php

namespace Tests\Feature;

use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChapterPublishingTest extends TestCase
{
    use RefreshDatabase;

    private User $tutor;
    private Course $course;
    private CourseChapter $chapter;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed roles first
        $this->seed(\Database\Seeders\RoleSeeder::class);

        // Create a tutor
        $this->tutor = User::factory()->create([
            'tutor_status' => 'approved',
            'referral_code' => 'TEST123',
        ]);

        // Assign tutor role
        $this->tutor->syncRoles([Role::USER, Role::TUTOR]);

        // Create a course
        $this->course = Course::factory()->create([
            'tutor_id' => $this->tutor->id,
            'status' => 'draft',
        ]);

        // Create a chapter
        $this->chapter = CourseChapter::factory()->create([
            'course_id' => $this->course->id,
            'title' => 'Test Chapter',
            'slug' => 'test-chapter',
            'is_published' => false,
        ]);
    }

    /** @test */
    public function tutor_can_publish_chapter()
    {
        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.curriculum.toggle-publish-chapter', [$this->course, $this->chapter]));

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Bab berhasil dipublikasikan!');

        $this->chapter->refresh();
        $this->assertTrue($this->chapter->is_published);
    }

    /** @test */
    public function tutor_can_unpublish_chapter()
    {
        // First publish the chapter
        $this->chapter->update(['is_published' => true]);

        $response = $this->actingAs($this->tutor)
            ->post(route('tutor.curriculum.toggle-publish-chapter', [$this->course, $this->chapter]));

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Bab berhasil disembunyikan dari publik.');

        $this->chapter->refresh();
        $this->assertFalse($this->chapter->is_published);
    }

    /** @test */
    public function tutor_cannot_publish_other_tutors_chapter()
    {
        $otherTutor = User::factory()->create([
            'tutor_status' => 'approved',
            'referral_code' => 'OTHER123',
        ]);

        // Assign tutor role
        $otherTutor->syncRoles([Role::USER, Role::TUTOR]);

        $response = $this->actingAs($otherTutor)
            ->post(route('tutor.curriculum.toggle-publish-chapter', [$this->course, $this->chapter]));

        $response->assertStatus(403);
    }

    /** @test */
    public function non_tutor_cannot_access_chapter_publishing()
    {
        $user = User::factory()->create();

        // Assign only user role (no tutor role)
        $user->syncRoles([Role::USER]);

        $response = $this->actingAs($user)
            ->post(route('tutor.curriculum.toggle-publish-chapter', [$this->course, $this->chapter]));

        // Non-tutor users are redirected to tutor registration, not given 403
        $response->assertRedirect(route('tutor.register.terms'));
        $response->assertSessionHas('info', 'Untuk mengakses area tutor, Anda perlu mendaftar sebagai tutor terlebih dahulu.');
    }
}
