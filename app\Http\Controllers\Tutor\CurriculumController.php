<?php

namespace App\Http\Controllers\Tutor;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use App\Models\LessonQuiz;
use App\Models\QuizQuestion;
use App\Models\QuizQuestionOption;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CurriculumController extends Controller
{
    /**
     * Show the curriculum management page for a course.
     */
    public function index(Course $course)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Load chapters with lessons
        $course->load(['chapters.lessons' => function ($query) {
            $query->orderBy('sort_order');
        }]);

        return view('tutor.curriculum.index', compact('course'));
    }

    /**
     * Store a new chapter.
     */
    public function storeChapter(Request $request, Course $course)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_free' => 'boolean',
        ]);

        // Get the next sort order
        $nextSortOrder = $course->chapters()->max('sort_order') + 1;

        $chapter = CourseChapter::create([
            'course_id' => $course->id,
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'sort_order' => $nextSortOrder,
            'is_free' => $validated['is_free'] ?? false,
            'is_published' => false, // Default to draft
        ]);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil ditambahkan!');
    }

    /**
     * Show the form for creating a new material.
     */
    public function createMaterial(Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        return view('tutor.curriculum.create-material', compact('course', 'chapter'));
    }

    /**
     * Show the form for editing a material.
     */
    public function editMaterial(Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Load quiz and assignment relationships with their related data
        $lesson->load([
            'quiz.questions.options' => function ($query) {
                $query->orderBy('sort_order');
            },
            'assignment'
        ]);

        return view('tutor.curriculum.edit-material', compact('course', 'chapter', 'lesson'));
    }

    /**
     * Store a new lesson.
     */
    public function storeLesson(Request $request, Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'uploaded_video_path' => 'nullable|string', // Path from separate upload
            'video_source' => 'nullable|in:url,upload',
            'duration_minutes' => 'required|integer|min:1|max:300',
            'type' => 'required|in:video,text,quiz,assignment',
            'is_preview' => 'boolean',
        ];

        // Add assignment-specific validation rules if type is assignment
        if ($request->input('type') === 'assignment') {
            $rules['assignment_deadline'] = 'required|date|after:now';
            $rules['assignment_instructions'] = 'required|string|max:2000';
            $rules['assignment_max_points'] = 'nullable|integer|min:1|max:1000';
            $rules['assignment_max_files'] = 'nullable|integer|min:1|max:10';
            $rules['assignment_max_file_size'] = 'nullable|integer|min:1|max:100';
            $rules['assignment_allow_late'] = 'boolean';
            $rules['assignment_late_penalty'] = 'nullable|integer|min:0|max:100';
            $rules['assignment_requirements'] = 'nullable|string|max:1000';
            $rules['assignment_file_types'] = 'nullable|array';
            $rules['assignment_file_types.*'] = 'string|in:pdf,doc,docx,txt,zip,jpg,png,mp4';
        }

        // Add quiz-specific validation rules if type is quiz
        if ($request->input('type') === 'quiz') {
            $rules['quiz_time_limit'] = 'nullable|integer|min:1|max:180';
            $rules['quiz_max_attempts'] = 'nullable|integer|min:1|max:10';
            $rules['quiz_passing_score'] = 'nullable|integer|min:0|max:100';
            $rules['quiz_shuffle_questions'] = 'boolean';
            $rules['quiz_instructions'] = 'nullable|string|max:1000';
            $rules['quiz_questions'] = 'required|array|min:1';
            $rules['quiz_questions.*.question'] = 'required|string|max:500';
            $rules['quiz_questions.*.type'] = 'required|in:multiple_choice,true_false,essay';
            $rules['quiz_questions.*.points'] = 'nullable|integer|min:1|max:100';
            $rules['quiz_questions.*.options'] = 'required_if:quiz_questions.*.type,multiple_choice|array|min:2';
            $rules['quiz_questions.*.options.*'] = 'required|string|max:200';
            $rules['quiz_questions.*.correct_answer'] = 'required';
            $rules['quiz_questions.*.explanation'] = 'nullable|string|max:500';
        }

        $validated = $request->validate($rules, [
            'duration_minutes.required' => 'Estimasi durasi harus diisi.',
            'duration_minutes.min' => 'Estimasi durasi minimal 1 menit.',
            'duration_minutes.max' => 'Estimasi durasi maksimal 300 menit.',
            'duration_minutes.integer' => 'Estimasi durasi harus berupa angka.',
            'assignment_deadline.required' => 'Tanggal deadline tugas harus diisi.',
            'assignment_deadline.after' => 'Tanggal deadline harus setelah waktu sekarang.',
            'assignment_instructions.required' => 'Instruksi tugas harus diisi.',
            'assignment_instructions.max' => 'Instruksi tugas maksimal 2000 karakter.',
            'quiz_questions.required' => 'Kuis harus memiliki minimal 1 soal.',
            'quiz_questions.min' => 'Kuis harus memiliki minimal 1 soal.',
            'quiz_questions.*.question.required' => 'Pertanyaan harus diisi.',
            'quiz_questions.*.question.max' => 'Pertanyaan maksimal 500 karakter.',
            'quiz_questions.*.type.required' => 'Tipe soal harus dipilih.',
            'quiz_questions.*.options.required_if' => 'Pilihan jawaban harus diisi untuk soal pilihan ganda.',
            'quiz_questions.*.options.min' => 'Soal pilihan ganda harus memiliki minimal 2 pilihan.',
            'quiz_questions.*.correct_answer.required' => 'Jawaban yang benar harus dipilih.',
            'quiz_time_limit.min' => 'Batas waktu minimal 1 menit.',
            'quiz_time_limit.max' => 'Batas waktu maksimal 180 menit.',
            'quiz_max_attempts.min' => 'Maksimal percobaan minimal 1.',
            'quiz_max_attempts.max' => 'Maksimal percobaan maksimal 10.',
            'quiz_passing_score.min' => 'Nilai lulus minimal 0%.',
            'quiz_passing_score.max' => 'Nilai lulus maksimal 100%.',
        ]);

        // Use uploaded video path if available
        $videoFilePath = $validated['uploaded_video_path'] ?? null;

        // Get the next sort order within the chapter
        $nextSortOrder = $chapter->lessons()->max('sort_order') + 1;

        // Create the lesson
        $lesson = CourseLesson::create([
            'course_id' => $course->id,
            'chapter_id' => $chapter->id,
            'title' => $validated['title'],
            'description' => $validated['description'] ?? null,
            'content' => $validated['content'] ?? null,
            'video_url' => $request->video_source === 'url' ? $validated['video_url'] : null,
            'video_file' => $videoFilePath,
            'duration_minutes' => $validated['duration_minutes'],
            'type' => $validated['type'],
            'sort_order' => $nextSortOrder,
            'is_free' => $course->is_free, // Inherit from course
            'is_preview' => $validated['is_preview'] ?? false,
            'is_published' => false, // Default to draft
        ]);

        // Create assignment data if type is assignment
        if ($validated['type'] === 'assignment') {
            \App\Models\LessonAssignment::create([
                'lesson_id' => $lesson->id,
                'title' => 'Tugas: ' . $validated['title'],
                'description' => $validated['assignment_instructions'],
                'requirements' => $validated['assignment_requirements'] ?? null,
                'allowed_file_types' => $validated['assignment_file_types'] ?? ['pdf', 'doc', 'docx'],
                'max_file_size' => $validated['assignment_max_file_size'] ?? 10,
                'max_files' => $validated['assignment_max_files'] ?? 3,
                'due_date' => $validated['assignment_deadline'],
                'max_points' => $validated['assignment_max_points'] ?? 100,
                'allow_late_submission' => $validated['assignment_allow_late'] ?? false,
                'late_penalty_percent' => $validated['assignment_late_penalty'] ?? 0,
                'is_published' => false, // Default to draft
            ]);
        }

        // Create quiz data if type is quiz
        if ($validated['type'] === 'quiz') {
            $quiz = LessonQuiz::create([
                'lesson_id' => $lesson->id,
                'title' => 'Kuis: ' . $validated['title'],
                'description' => $validated['quiz_instructions'] ?? null,
                'time_limit' => $validated['quiz_time_limit'] ?? null,
                'max_attempts' => $validated['quiz_max_attempts'] ?? 3,
                'passing_score' => $validated['quiz_passing_score'] ?? 70,
                'shuffle_questions' => $validated['quiz_shuffle_questions'] ?? false,
                'show_results_immediately' => true,
                'is_published' => false, // Default to draft
            ]);

            // Create quiz questions
            if (isset($validated['quiz_questions']) && is_array($validated['quiz_questions'])) {
                foreach ($validated['quiz_questions'] as $index => $questionData) {
                    $question = QuizQuestion::create([
                        'quiz_id' => $quiz->id,
                        'question' => $questionData['question'],
                        'type' => $questionData['type'],
                        'points' => $questionData['points'] ?? 10,
                        'sort_order' => $index + 1,
                        'explanation' => $questionData['explanation'] ?? null,
                    ]);

                    // Create answer options for multiple choice questions
                    if ($questionData['type'] === 'multiple_choice' && isset($questionData['options'])) {
                        foreach ($questionData['options'] as $optionIndex => $optionText) {
                            $isCorrect = ($questionData['correct_answer'] == $optionIndex);
                            QuizQuestionOption::create([
                                'question_id' => $question->id,
                                'option_text' => $optionText,
                                'is_correct' => $isCorrect,
                                'sort_order' => $optionIndex + 1,
                            ]);
                        }
                    } elseif ($questionData['type'] === 'true_false') {
                        // Create true/false options
                        QuizQuestionOption::create([
                            'question_id' => $question->id,
                            'option_text' => 'Benar',
                            'is_correct' => $questionData['correct_answer'] === 'true',
                            'sort_order' => 1,
                        ]);
                        QuizQuestionOption::create([
                            'question_id' => $question->id,
                            'option_text' => 'Salah',
                            'is_correct' => $questionData['correct_answer'] === 'false',
                            'sort_order' => 2,
                        ]);
                    }
                }
            }
        }

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil ditambahkan!');
    }

    /**
     * Handle video upload with progress tracking.
     */
    public function uploadVideo(Request $request, Course $course)
    {
        // Set PHP runtime limits for large file uploads
        ini_set('upload_max_filesize', '150M');
        ini_set('post_max_size', '150M');
        ini_set('max_execution_time', '600');
        ini_set('max_input_time', '600');
        ini_set('memory_limit', '512M');

        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to course.'
            ], 403);
        }

        // Check if file was uploaded
        if (!$request->hasFile('video_file')) {
            return response()->json([
                'success' => false,
                'message' => 'No file uploaded.'
            ], 422);
        }

        // Check for upload errors
        $uploadError = $request->file('video_file')->getError();
        if ($uploadError !== UPLOAD_ERR_OK) {
            $errorMessage = $this->getUploadErrorMessage($uploadError);
            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ], 422);
        }

        try {
            Log::info('Starting video upload validation', [
                'course_id' => $course->id,
                'file_present' => $request->hasFile('video_file'),
                'file_size' => $request->hasFile('video_file') ? $request->file('video_file')->getSize() : 'N/A'
            ]);

            $validated = $request->validate([
                'video_file' => 'required|file|mimes:mp4,mov,avi,quicktime|max:102400', // 100MB max
            ]);

            $videoFile = $request->file('video_file');
            Log::info('Video file validation passed', [
                'original_name' => $videoFile->getClientOriginalName(),
                'size' => $videoFile->getSize(),
                'mime_type' => $videoFile->getMimeType()
            ]);

            // Additional file size check
            $maxSize = 100 * 1024 * 1024; // 100MB in bytes
            if ($videoFile->getSize() > $maxSize) {
                return response()->json([
                    'success' => false,
                    'message' => 'File terlalu besar. Maksimal 100MB.'
                ], 422);
            }

            // Create folder structure based on course type
            $folderPath = "user/{$course->tutor_id}/course/{$course->id}/videos";

            // Generate unique filename
            $fileName = time() . '_' . \Illuminate\Support\Str::random(10) . '.' . $videoFile->getClientOriginalExtension();

            // Store in private storage for paid courses, public for free courses
            $disk = $course->is_free ? 'public' : 'local';

            Log::info('Attempting to store video file', [
                'folder_path' => $folderPath,
                'file_name' => $fileName,
                'disk' => $disk,
                'course_is_free' => $course->is_free
            ]);

            $videoFilePath = $videoFile->storeAs($folderPath, $fileName, $disk);

            Log::info('Video file stored successfully', [
                'stored_path' => $videoFilePath
            ]);

            return response()->json([
                'success' => true,
                'path' => $videoFilePath,
                'message' => 'Video berhasil diupload.',
                'file_size' => $videoFile->getSize(),
                'file_name' => $videoFile->getClientOriginalName()
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'File tidak valid: ' . implode(', ', $e->validator->errors()->all()),
                'errors' => $e->validator->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Video upload failed: ' . $e->getMessage(), [
                'course_id' => $course->id,
                'tutor_id' => $course->tutor_id,
                'file_size' => $request->hasFile('video_file') ? $request->file('video_file')->getSize() : 'unknown',
                'file_name' => $request->hasFile('video_file') ? $request->file('video_file')->getClientOriginalName() : 'unknown',
                'stack_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Upload gagal: ' . $e->getMessage() . ' (Error Code: ' . $e->getCode() . ')'
            ], 500);
        }
    }

    /**
     * Get human readable upload error message.
     */
    private function getUploadErrorMessage($errorCode)
    {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File terlalu besar (melebihi upload_max_filesize di php.ini).';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File terlalu besar (melebihi MAX_FILE_SIZE di form).';
            case UPLOAD_ERR_PARTIAL:
                return 'File hanya terupload sebagian. Silakan coba lagi.';
            case UPLOAD_ERR_NO_FILE:
                return 'Tidak ada file yang diupload.';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Folder temporary tidak ditemukan.';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Gagal menulis file ke disk.';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload dihentikan oleh ekstensi PHP.';
            default:
                return 'Terjadi kesalahan upload yang tidak diketahui.';
        }
    }

    /**
     * Update chapter.
     */
    public function updateChapter(Request $request, Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_published' => 'boolean',
            'is_free' => 'boolean',
        ]);

        $chapter->update($validated);

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil diperbarui!');
    }

    /**
     * Toggle the publish status of a chapter.
     */
    public function togglePublishChapter(Course $course, $chapterSlug)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id()) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Find the chapter by slug within the course scope
        $chapter = CourseChapter::where('slug', $chapterSlug)
            ->where('course_id', $course->id)
            ->firstOrFail();

        // Toggle the publish status
        $chapter->update(['is_published' => !$chapter->is_published]);

        $message = $chapter->is_published
            ? 'Bab berhasil dipublikasikan!'
            : 'Bab berhasil disembunyikan dari publik.';

        return back()->with('success', $message);
    }

    /**
     * Update lesson.
     */
    public function updateLesson(Request $request, Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        // Base validation rules
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url',
            'duration_minutes' => 'required|integer|min:1|max:300',
            'type' => 'required|in:video,text,quiz,assignment',
            'is_published' => 'boolean',
            'is_free' => 'boolean',
            'is_preview' => 'boolean',
        ];

        // Add quiz-specific validation rules
        if ($request->type === 'quiz') {
            $rules = array_merge($rules, [
                'quiz_instructions' => 'nullable|string|max:1000',
                'quiz_time_limit' => 'nullable|integer|min:1|max:300',
                'quiz_max_attempts' => 'nullable|integer|min:1|max:10',
                'quiz_passing_score' => 'nullable|integer|min:0|max:100',
                'quiz_shuffle_questions' => 'boolean',
                'quiz_questions' => 'nullable|array',
                'quiz_questions.*.question' => 'required_with:quiz_questions|string|max:1000',
                'quiz_questions.*.type' => 'required_with:quiz_questions|in:multiple_choice,true_false,short_answer',
                'quiz_questions.*.points' => 'required_with:quiz_questions|integer|min:1|max:100',
                'quiz_questions.*.explanation' => 'nullable|string|max:500',
                'quiz_questions.*.options' => 'required_if:quiz_questions.*.type,multiple_choice|array|min:2',
                'quiz_questions.*.correct_answer' => 'required_with:quiz_questions',
            ]);
        }

        // Add assignment-specific validation rules
        if ($request->type === 'assignment') {
            $rules = array_merge($rules, [
                'assignment_instructions' => 'required|string|max:2000',
                'assignment_requirements' => 'nullable|string|max:1000',
                'assignment_file_types' => 'nullable|array',
                'assignment_max_file_size' => 'nullable|integer|min:1|max:100',
                'assignment_max_files' => 'nullable|integer|min:1|max:10',
                'assignment_deadline' => 'nullable|date|after:now',
                'assignment_max_points' => 'nullable|integer|min:1|max:1000',
                'assignment_allow_late' => 'boolean',
                'assignment_late_penalty' => 'nullable|integer|min:0|max:100',
            ]);
        }

        $validated = $request->validate($rules, [
            'duration_minutes.required' => 'Estimasi durasi harus diisi.',
            'duration_minutes.min' => 'Estimasi durasi minimal 1 menit.',
            'duration_minutes.max' => 'Estimasi durasi maksimal 300 menit.',
            'duration_minutes.integer' => 'Estimasi durasi harus berupa angka.',
        ]);

        // Update the lesson
        $lesson->update($validated);

        // Handle quiz data update
        if ($validated['type'] === 'quiz') {
            // Delete existing quiz and its questions if type changed from non-quiz
            if ($lesson->getOriginal('type') !== 'quiz') {
                $lesson->quiz()?->delete();
            }

            // Update or create quiz
            $quiz = $lesson->quiz ?: new \App\Models\LessonQuiz(['lesson_id' => $lesson->id]);
            $quiz->fill([
                'title' => 'Kuis: ' . $validated['title'],
                'description' => $validated['quiz_instructions'] ?? null,
                'time_limit' => $validated['quiz_time_limit'] ?? null,
                'max_attempts' => $validated['quiz_max_attempts'] ?? 3,
                'passing_score' => $validated['quiz_passing_score'] ?? 70,
                'shuffle_questions' => $validated['quiz_shuffle_questions'] ?? false,
                'show_results_immediately' => true,
                'is_published' => $validated['is_published'] ?? false,
            ]);
            $quiz->save();

            // Delete existing questions
            $quiz->questions()->delete();

            // Create new questions
            if (isset($validated['quiz_questions']) && is_array($validated['quiz_questions'])) {
                foreach ($validated['quiz_questions'] as $index => $questionData) {
                    $question = \App\Models\QuizQuestion::create([
                        'quiz_id' => $quiz->id,
                        'question' => $questionData['question'],
                        'type' => $questionData['type'],
                        'points' => $questionData['points'] ?? 10,
                        'sort_order' => $index + 1,
                        'explanation' => $questionData['explanation'] ?? null,
                    ]);

                    // Create answer options for multiple choice questions
                    if ($questionData['type'] === 'multiple_choice' && isset($questionData['options'])) {
                        foreach ($questionData['options'] as $optionIndex => $optionText) {
                            $isCorrect = ($questionData['correct_answer'] == $optionIndex);
                            \App\Models\QuizQuestionOption::create([
                                'question_id' => $question->id,
                                'option_text' => $optionText,
                                'is_correct' => $isCorrect,
                                'sort_order' => $optionIndex + 1,
                            ]);
                        }
                    } elseif ($questionData['type'] === 'true_false') {
                        // Create true/false options
                        \App\Models\QuizQuestionOption::create([
                            'question_id' => $question->id,
                            'option_text' => 'Benar',
                            'is_correct' => $questionData['correct_answer'] === 'true',
                            'sort_order' => 1,
                        ]);
                        \App\Models\QuizQuestionOption::create([
                            'question_id' => $question->id,
                            'option_text' => 'Salah',
                            'is_correct' => $questionData['correct_answer'] === 'false',
                            'sort_order' => 2,
                        ]);
                    }
                    // For short_answer, no options needed
                }
            }
        } else {
            // If type changed from quiz to something else, delete the quiz
            if ($lesson->getOriginal('type') === 'quiz') {
                $lesson->quiz()?->delete();
            }
        }

        // Handle assignment data update
        if ($validated['type'] === 'assignment') {
            // Delete existing assignment if type changed from non-assignment
            if ($lesson->getOriginal('type') !== 'assignment') {
                $lesson->assignment()?->delete();
            }

            // Update or create assignment
            $assignment = $lesson->assignment ?: new \App\Models\LessonAssignment(['lesson_id' => $lesson->id]);
            $assignment->fill([
                'title' => 'Tugas: ' . $validated['title'],
                'description' => $validated['assignment_instructions'],
                'requirements' => $validated['assignment_requirements'] ?? null,
                'allowed_file_types' => $validated['assignment_file_types'] ?? ['pdf', 'doc', 'docx'],
                'max_file_size' => $validated['assignment_max_file_size'] ?? 10,
                'max_files' => $validated['assignment_max_files'] ?? 3,
                'due_date' => $validated['assignment_deadline'],
                'max_points' => $validated['assignment_max_points'] ?? 100,
                'allow_late_submission' => $validated['assignment_allow_late'] ?? false,
                'late_penalty_percent' => $validated['assignment_late_penalty'] ?? 0,
                'is_published' => $validated['is_published'] ?? false,
            ]);
            $assignment->save();
        } else {
            // If type changed from assignment to something else, delete the assignment
            if ($lesson->getOriginal('type') === 'assignment') {
                $lesson->assignment()?->delete();
            }
        }

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil diperbarui!');
    }

    /**
     * Delete chapter.
     */
    public function deleteChapter(Course $course, CourseChapter $chapter)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() || $chapter->course_id !== $course->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $chapter->delete();

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Bab berhasil dihapus!');
    }

    /**
     * Delete lesson.
     */
    public function deleteLesson(Course $course, CourseChapter $chapter, CourseLesson $lesson)
    {
        // Ensure the course belongs to the authenticated tutor
        if ($course->tutor_id !== Auth::id() ||
            $chapter->course_id !== $course->id ||
            $lesson->chapter_id !== $chapter->id) {
            abort(403, 'Unauthorized access to course curriculum.');
        }

        $lesson->delete();

        return redirect()->route('tutor.curriculum.index', $course)
            ->with('success', 'Materi berhasil dihapus!');
    }

    /**
     * Download CSV template for quiz questions
     */
    public function downloadQuizTemplate()
    {
        $csvContent = [
            '# TEMPLATE SOAL KUIS - PANDUAN PENGGUNAAN',
            '# Kolom yang WAJIB diisi: question, type, points',
            '# Tipe soal yang didukung:',
            '#   - multiple_choice: Pilihan ganda (isi option_a sampai option_d, correct_answer: A/B/C/D)',
            '#   - true_false: Benar/Salah (kosongkan option_c dan option_d, correct_answer: A untuk Benar, B untuk Salah)',
            '#   - short_answer: Jawaban singkat (kosongkan semua option, correct_answer boleh kosong)',
            '# Points: angka 1-100',
            '# Explanation: opsional, penjelasan jawaban yang benar',
            '# Sort_order: urutan soal (opsional, akan diurutkan otomatis jika kosong)',
            '',
            'question,type,option_a,option_b,option_c,option_d,correct_answer,points,explanation,sort_order',
            'Siapa presiden pertama Indonesia?,multiple_choice,Soekarno,Soeharto,Habibie,Megawati,A,10,Soekarno adalah presiden pertama Republik Indonesia yang memproklamirkan kemerdekaan,1',
            'Apakah Indonesia adalah negara kepulauan?,true_false,Benar,Salah,,,A,5,Indonesia memiliki lebih dari 17000 pulau sehingga disebut negara kepulauan,2',
            'Sebutkan ibu kota provinsi Jawa Barat,short_answer,,,,,,10,Bandung adalah ibu kota provinsi Jawa Barat,3'
        ];

        $filename = 'template_soal_kuis_' . date('Y-m-d') . '.csv';

        return response()->streamDownload(function () use ($csvContent) {
            echo implode("\n", $csvContent);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
