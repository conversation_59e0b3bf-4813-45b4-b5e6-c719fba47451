@extends('layouts.tutor')

@section('title', $blogPost->title . ' - Tutor Dashboard')

@section('content')
<div class="tutor-dashboard-container tutor-blog-mobile min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-3 md:px-4 sm:px-6 lg:px-8 py-4 md:py-6">
            <div class="tutor-welcome-header flex flex-col gap-4">
                <div class="flex-1">
                    <h1 class="text-lg md:text-2xl font-bold text-gray-900 leading-tight">{{ $blogPost->title }}</h1>
                    <div class="flex flex-wrap items-center gap-2 md:gap-4 mt-2">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $blogPost->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ $blogPost->status === 'published' ? 'Dipublikasikan' : 'Draft' }}
                        </span>
                        @if($blogPost->category)
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ $blogPost->category->name }}
                            </span>
                        @endif
                        <span class="text-xs md:text-sm text-gray-500">{{ $blogPost->views_count }} views</span>
                        <span class="text-xs md:text-sm text-gray-500">{{ $blogPost->read_time }} menit baca</span>
                    </div>
                </div>

                <!-- Mobile: Vertical Button Layout -->
                <div class="tutor-header-actions flex flex-col md:flex-row gap-2 md:gap-3">
                    <a href="{{ route('tutor.blogs') }}" class="btn bg-gray-600 hover:bg-gray-700 text-white tutor-touch-friendly text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                    <a href="{{ route('tutor.blogs.edit', $blogPost) }}" class="btn bg-blue-600 hover:bg-blue-700 text-white tutor-touch-friendly text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit
                    </a>
                    @if($blogPost->status === 'published')
                        <a href="{{ route('blog.show', $blogPost) }}" target="_blank" class="btn bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly text-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            Lihat Publik
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-4xl mx-auto px-3 md:px-4 sm:px-6 lg:px-8 py-6 md:py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 order-2 lg:order-1">
                <div class="bg-white rounded-lg shadow-sm border p-4 md:p-8">
                    <!-- Featured Image -->
                    @if($blogPost->featured_image)
                        <div class="mb-6 md:mb-8">
                            <img src="{{ asset('storage/' . $blogPost->featured_image) }}"
                                 alt="{{ $blogPost->title }}"
                                 class="w-full h-48 md:h-64 object-cover rounded-lg">
                        </div>
                    @endif

                    <!-- Content -->
                    <div class="prose prose-gray max-w-none text-sm md:text-base">
                        <div class="whitespace-pre-line">{{ $blogPost->content }}</div>
                    </div>

                    <!-- Tags -->
                    @if($blogPost->tags && count($blogPost->tags) > 0)
                        <div class="mt-6 md:mt-8 pt-6 md:pt-8 border-t border-gray-200">
                            <h3 class="text-sm font-medium text-gray-900 mb-3">Tags:</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($blogPost->tags as $tag)
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                                        {{ $tag }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 order-1 lg:order-2">
                <div class="space-y-4 md:space-y-6">
                    <!-- Blog Info -->
                    <div class="bg-white rounded-lg shadow-sm border p-4 md:p-6">
                        <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Informasi Blog</h3>
                        <dl class="space-y-2 md:space-y-3">
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {{ $blogPost->status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $blogPost->status === 'published' ? 'Dipublikasikan' : 'Draft' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Dibuat</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900">{{ $blogPost->created_at->format('d M Y, H:i') }}</dd>
                            </div>
                            @if($blogPost->published_at)
                                <div>
                                    <dt class="text-xs md:text-sm font-medium text-gray-500">Dipublikasikan</dt>
                                    <dd class="mt-1 text-xs md:text-sm text-gray-900">{{ $blogPost->published_at->format('d M Y, H:i') }}</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Diperbarui</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900">{{ $blogPost->updated_at->format('d M Y, H:i') }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Views</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900">{{ number_format($blogPost->views_count) }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Waktu Baca</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900">{{ $blogPost->read_time }} menit</dd>
                            </div>
                        </dl>
                    </div>

                    <!-- SEO Info -->
                    <div class="bg-white rounded-lg shadow-sm border p-4 md:p-6">
                        <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">SEO</h3>
                        <dl class="space-y-2 md:space-y-3">
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Meta Title</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900 break-words">{{ $blogPost->meta_title ?: $blogPost->title }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Meta Description</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900 break-words">{{ $blogPost->meta_description ?: $blogPost->excerpt }}</dd>
                            </div>
                            <div>
                                <dt class="text-xs md:text-sm font-medium text-gray-500">Slug</dt>
                                <dd class="mt-1 text-xs md:text-sm text-gray-900 font-mono break-all">{{ $blogPost->slug }}</dd>
                            </div>
                        </dl>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-lg shadow-sm border p-4 md:p-6">
                        <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Aksi</h3>
                        <div class="space-y-3">
                            <form action="{{ route('tutor.blogs.toggle-publish', $blogPost) }}" method="POST">
                                @csrf
                                <button type="submit" class="w-full btn {{ $blogPost->status === 'published' ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-emerald-600 hover:bg-emerald-700' }} text-white tutor-touch-friendly text-sm">
                                    @if($blogPost->status === 'published')
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                        </svg>
                                        Sembunyikan
                                    @else
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Publikasikan
                                    @endif
                                </button>
                            </form>

                            <form action="{{ route('tutor.blogs.destroy', $blogPost) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus blog ini?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full btn bg-red-600 hover:bg-red-700 text-white tutor-touch-friendly text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Hapus Blog
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
