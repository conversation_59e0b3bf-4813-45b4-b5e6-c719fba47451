<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Exam;
use App\Models\User;
use App\Models\Category;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Exam>
 */
class ExamFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Exam::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->randomElement([
            'Ujian Dasar PHP',
            'Test JavaScript Fundamentals',
            'Python Programming Quiz',
            'Web Development Assessment',
            'Database Design Test',
            'UI/UX Design Evaluation',
            'Digital Marketing Quiz',
            'Data Science Fundamentals',
            'Mobile App Development Test',
            'Cybersecurity Basics Quiz'
        ]);

        return [
            'tutor_id' => User::factory(),
            'category_id' => Category::factory(),
            'title' => $title,
            'description' => $this->faker->paragraph(2),
            'price' => $this->faker->randomElement([0, 25000, 50000, 75000, 100000]),
            'time_limit' => $this->faker->numberBetween(30, 180), // 30 minutes to 3 hours
            'max_attempts' => $this->faker->numberBetween(1, 5),
            'passing_score' => $this->faker->numberBetween(60, 80),
            'shuffle_questions' => $this->faker->boolean(70), // 70% chance of true
            'show_results_immediately' => $this->faker->boolean(80), // 80% chance of true
            'is_published' => $this->faker->boolean(60), // 60% chance of published
            'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'instructions' => $this->faker->paragraph(3),
            'certificate_enabled' => $this->faker->boolean(40), // 40% chance of certificate enabled
        ];
    }

    /**
     * Indicate that the exam is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the exam is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0,
        ]);
    }

    /**
     * Indicate that the exam is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $this->faker->numberBetween(25000, 150000),
        ]);
    }

    /**
     * Indicate that the exam has certificate enabled.
     */
    public function withCertificate(): static
    {
        return $this->state(fn (array $attributes) => [
            'certificate_enabled' => true,
        ]);
    }
}
