@extends('layouts.user')

@section('title', 'Kursus Say<PERSON> - <PERSON>')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/dashboard-responsive.css') }}">
@endpush

@section('content')
<div class="dashboard-container p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
                <div class="flex-1 text-center sm:text-left">
                    <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">Kursus Saya</h1>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base"><PERSON><PERSON><PERSON> dan lanjutkan perjalanan belajar <PERSON></p>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 lg:flex-shrink-0">
                    <a href="{{ route('courses.index') }}" class="btn btn-outline min-h-[44px] justify-center text-sm sm:text-base">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span>Jelajahi Kursus</span>
                    </a>
                    @if(!auth()->user()->hasActiveMembership())
                        <a href="{{ route('user.membership') }}" class="btn btn-primary min-h-[44px] justify-center text-sm sm:text-base">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                            <span>Upgrade Membership</span>
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow-sm mb-6 lg:mb-8 border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex flex-col sm:flex-row sm:space-x-4 lg:space-x-8 px-4 sm:px-5 lg:px-6" aria-label="Tabs">
                <button class="border-primary text-primary py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center justify-center sm:justify-start" id="enrolled-tab">
                    <span class="mr-2">Progress</span>
                    <span class="bg-primary text-white py-0.5 px-2 sm:px-2.5 rounded-full text-xs">{{ count($inProgressCourses) }}</span>
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm min-h-[44px] flex items-center justify-center sm:justify-start" id="completed-tab">
                    <span class="mr-2">Finish</span>
                    <span class="bg-gray-100 text-gray-900 py-0.5 px-2 sm:px-2.5 rounded-full text-xs">{{ count($completedCourses) }}</span>
                </button>
            </nav>
        </div>
    </div>

    <!-- Enrolled Courses Tab -->
    <div id="enrolled-content" class="tab-content">
        @if(count($inProgressCourses) > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
                @foreach($inProgressCourses as $enrollmentData)
                    @php
                        $course = $enrollmentData['course'];
                        $enrollment = $enrollmentData['enrollment'];
                        $progress = $enrollmentData['progress'];
                    @endphp
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                        <div class="h-32 sm:h-36 lg:h-40 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-2 sm:bottom-3 lg:bottom-4 left-2 sm:left-3 lg:left-4 text-white pr-2">
                                <h3 class="text-sm sm:text-base lg:text-lg font-bold line-clamp-2">{{ $course->title }}</h3>
                                <p class="text-xs sm:text-sm opacity-90 truncate">{{ $course->tutor->name }}</p>
                            </div>
                            <div class="absolute top-2 sm:top-3 lg:top-4 right-2 sm:right-3 lg:right-4">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                    {{ $progress }}% Selesai
                                </span>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5 lg:p-6">
                            <div class="mb-4">
                                <div class="flex justify-between text-xs sm:text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-primary h-2 rounded-full transition-all duration-300" style="width: {{ $progress }}%"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between text-xs sm:text-sm text-gray-600 mb-4">
                                <span>{{ $course->lessons->count() }} Pelajaran</span>
                                <span>{{ $course->level_indonesian }}</span>
                            </div>
                            <div class="space-y-2 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-3">
                                <a href="{{ route('course.learn', $course) }}" class="btn btn-primary min-h-[44px] text-xs sm:text-sm w-full justify-center">
                                    Lanjutkan Belajar
                                </a>
                                @if(auth()->user()->hasActiveMembership())
                                    <button class="nala-course-chat-btn btn btn-outline min-h-[44px] text-xs sm:text-sm w-full justify-center" data-course-id="{{ $course->id }}" data-course-title="{{ $course->title }}">
                                        Chat NALA
                                    </button>
                                @else
                                    <a href="{{ route('user.membership') }}" class="btn btn-outline min-h-[44px] text-xs sm:text-sm w-full justify-center">
                                        Tanya NALA
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm p-8 sm:p-12 text-center border border-gray-200">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-bold text-gray-900 mb-2">Belum Ada Kursus</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto text-sm sm:text-base px-4">
                    Anda belum mendaftar ke kursus apapun. Mulai perjalanan belajar Anda dengan memilih kursus yang menarik.
                </p>
                <div class="space-y-3">
                    <a href="{{ route('courses.index') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span>Jelajahi Kursus</span>
                    </a>
                    @if(!auth()->user()->isTutor() && !auth()->user()->hasTutorProfile())
                        <div class="text-sm text-gray-500">atau</div>
                        <a href="{{ route('tutor.register.terms') }}" class="btn btn-outline min-h-[44px] w-full sm:w-auto">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span>Jadi Pengajar</span>
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>



    <!-- Completed Courses Tab -->
    <div id="completed-content" class="tab-content hidden">
        @if(count($completedCourses) > 0)
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
                @foreach($completedCourses as $enrollmentData)
                    @php
                        $course = $enrollmentData['course'];
                        $enrollment = $enrollmentData['enrollment'];
                        $progress = $enrollmentData['progress'];
                    @endphp
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border border-gray-200">
                        <div class="h-32 sm:h-36 lg:h-40 bg-gradient-to-br from-green-500 to-emerald-600 relative">
                            @if($course->thumbnail)
                                <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                            @endif
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-2 sm:bottom-3 lg:bottom-4 left-2 sm:left-3 lg:left-4 text-white pr-2">
                                <h3 class="text-sm sm:text-base lg:text-lg font-bold line-clamp-2">{{ $course->title }}</h3>
                                <p class="text-xs sm:text-sm opacity-90 truncate">{{ $course->tutor->name }}</p>
                            </div>
                            <div class="absolute top-2 sm:top-3 lg:top-4 right-2 sm:right-3 lg:right-4">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                                    <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span>Selesai</span>
                                </span>
                            </div>
                        </div>
                        <div class="p-4 sm:p-5 lg:p-6">
                            <div class="mb-4">
                                <div class="flex justify-between text-xs sm:text-sm text-gray-600 mb-1">
                                    <span>Progress</span>
                                    <span>{{ $progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between text-xs sm:text-sm text-gray-600 mb-4">
                                <span>{{ $course->lessons->count() }} Pelajaran</span>
                                <span>{{ $course->level_indonesian }}</span>
                            </div>
                            <div class="space-y-2 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-3">
                                <a href="{{ route('course.learn', $course) }}" class="btn btn-primary min-h-[44px] text-xs sm:text-sm w-full justify-center">
                                    <svg class="w-3 h-3 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span>Review</span>
                                </a>
                                @if(auth()->user()->hasActiveMembership())
                                    <button class="nala-course-chat-btn btn btn-outline min-h-[44px] text-xs sm:text-sm w-full justify-center" data-course-id="{{ $course->id }}" data-course-title="{{ $course->title }}">
                                        Tanya NALA
                                    </button>
                                @else
                                    <a href="{{ route('user.membership') }}" class="btn btn-outline min-h-[44px] text-xs sm:text-sm w-full justify-center">
                                        Tanya NALA
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm p-8 sm:p-12 text-center border border-gray-200">
                <div class="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                    <svg class="w-6 h-6 sm:w-8 sm:h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <h3 class="text-base sm:text-lg font-bold text-gray-900 mb-2">Belum Ada Kursus Selesai</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto text-sm sm:text-base px-4">
                    Anda belum menyelesaikan kursus apapun. Lanjutkan belajar untuk mendapatkan sertifikat pertama Anda!
                </p>
                <a href="{{ route('user.dashboard') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto">
                    Lanjutkan Belajar
                </a>
            </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('[id$="-tab"]');
    const contents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-primary', 'text-primary');
                t.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active classes to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-primary', 'text-primary');

            // Hide all content
            contents.forEach(content => content.classList.add('hidden'));

            // Show corresponding content
            const contentId = this.id.replace('-tab', '-content');
            document.getElementById(contentId).classList.remove('hidden');
        });
    });
});

function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('[id$="-tab"]').forEach(btn => {
        btn.classList.remove('border-primary', 'text-primary');
        btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    const targetContent = document.getElementById(tabName + '-content');
    if (targetContent) {
        targetContent.classList.remove('hidden');
    }
    
    // Add active class to selected tab
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.classList.add('border-primary', 'text-primary');
        targetTab.classList.remove('border-transparent', 'text-gray-500');
    }
}

// Handle course-specific Nala chat buttons
document.addEventListener('DOMContentLoaded', function() {
    // Handle course-specific chat buttons
    const courseChatButtons = document.querySelectorAll('.nala-course-chat-btn');

    courseChatButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const courseId = this.getAttribute('data-course-id');
            const courseTitle = this.getAttribute('data-course-title');

            // Open Nala chat with course context
            if (window.nalaAIAssistant) {
                // Use the Nala AI Assistant instance
                window.nalaAIAssistant.openChat();

                // Set course context message after a short delay
                setTimeout(() => {
                    const chatInput = document.getElementById('nala-input');
                    if (chatInput) {
                        chatInput.value = `Saya ingin bertanya tentang kursus "${courseTitle}". Bisakah Anda membantu saya?`;

                        // Focus input only on desktop to prevent mobile keyboard issues
                        if (window.innerWidth > 768) {
                            chatInput.focus();
                        }

                        // Trigger input event to enable send button
                        const event = new Event('input');
                        chatInput.dispatchEvent(event);
                    }
                }, 200);
            } else {
                // Fallback: trigger the main toggle button click event
                const mainToggle = document.getElementById('nala-chat-toggle');
                if (mainToggle) {
                    mainToggle.click();

                    // Set course context message after opening
                    setTimeout(() => {
                        const chatInput = document.getElementById('nala-input');
                        if (chatInput) {
                            chatInput.value = `Saya ingin bertanya tentang kursus "${courseTitle}". Bisakah Anda membantu saya?`;

                            // Focus input only on desktop to prevent mobile keyboard issues
                            if (window.innerWidth > 768) {
                                chatInput.focus();
                            }

                            // Trigger input event to enable send button
                            const event = new Event('input');
                            chatInput.dispatchEvent(event);
                        }
                    }, 250);
                } else {
                    console.warn('Nala AI Assistant not available');
                }
            }
        });
    });
});
</script>
@endsection
