<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\CourseLesson;
use App\Models\LessonProgress;
use App\Traits\CourseProgressTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CourseLearningController extends Controller
{
    use CourseProgressTrait;
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the course learning dashboard.
     */
    public function index(Course $course)
    {
        $user = Auth::user();

        // Check if course is published
        if ($course->status !== 'published') {
            abort(404, 'Kursus tidak ditemukan.');
        }

        // Check if user has access to this course
        if (!$this->hasAccessToCourse($user, $course)) {
            return redirect()->route('course.show', $course)
                ->with('error', 'Anda perlu mendaftar terlebih dahulu untuk mengakses kursus ini.');
        }

        // Load course data with additional relationships
        $course->load([
            'chapters' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons.quiz',
            'chapters.lessons.assignment'
        ]);

        // Use the trait method for consistent progress calculation
        $progressData = $this->calculateCourseProgress($course, $user->id);
        
        $userProgress = $progressData['lesson_progress'];
        $totalLessons = $progressData['total_lessons'];
        $completedLessons = $progressData['completed_lessons'];
        $progressPercentage = $progressData['progress_percentage'];
        $enrollment = $progressData['enrollment'];

        // Find next lesson to continue
        $nextLesson = $this->getNextLesson($course, $userProgress);

        return view('course.learn.index', compact(
            'course', 
            'userProgress', 
            'progressPercentage', 
            'enrollment',
            'nextLesson',
            'totalLessons',
            'completedLessons'
        ));
    }

    /**
     * Show a specific lesson.
     */
    public function lesson(Course $course, CourseLesson $lesson)
    {
        $user = Auth::user();

        // Check if lesson belongs to course
        if ($lesson->course_id !== $course->id) {
            abort(404, 'Materi tidak ditemukan.');
        }

        // Check if user has access to this course
        if (!$this->hasAccessToCourse($user, $course)) {
            return redirect()->route('course.show', $course)
                ->with('error', 'Anda perlu mendaftar terlebih dahulu untuk mengakses materi ini.');
        }

        // Check if lesson is published
        if (!$lesson->is_published) {
            abort(404, 'Materi tidak tersedia.');
        }

        // Load lesson relationships
        $lesson->load(['chapter', 'quiz.questions', 'assignment']);

        // Load course data with progress (same as index method)
        $course->load([
            'chapters' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons.progress' => function ($query) use ($user) {
                $query->where('user_id', $user->id);
            }
        ]);

        // Get or create lesson progress
        $progress = LessonProgress::firstOrCreate([
            'user_id' => $user->id,
            'lesson_id' => $lesson->id,
        ], [
            'status' => 'in_progress',
            'progress_percentage' => 0,
            'started_at' => now(),
            'last_accessed_at' => now(),
            'visit_count' => 1,
        ]);

        // Update last accessed time and visit count
        $progress->update([
            'last_accessed_at' => now(),
            'visit_count' => $progress->visit_count + 1,
        ]);

        // Get navigation info (previous/next lessons)
        $navigation = $this->getLessonNavigation($course, $lesson);

        return view('course.learn.lesson', compact(
            'course',
            'lesson',
            'progress',
            'navigation'
        ));
    }

    /**
     * Update lesson progress.
     */
    public function updateProgress(Request $request, Course $course, CourseLesson $lesson)
    {
        $user = Auth::user();

        // Validate request
        $validated = $request->validate([
            'status' => 'required|in:in_progress,completed',
            'progress_percentage' => 'required|integer|min:0|max:100',
            'time_spent_seconds' => 'nullable|integer|min:0',
        ]);

        // Check access
        if (!$this->hasAccessToCourse($user, $course) || $lesson->course_id !== $course->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $progress = LessonProgress::where('user_id', $user->id)
                ->where('lesson_id', $lesson->id)
                ->first();

            if (!$progress) {
                return response()->json(['error' => 'Progress not found'], 404);
            }

            $updateData = [
                'status' => $validated['status'],
                'progress_percentage' => $validated['progress_percentage'],
                'last_accessed_at' => now(),
            ];

            if ($validated['status'] === 'completed' && !$progress->completed_at) {
                $updateData['completed_at'] = now();
            }

            if (isset($validated['time_spent_seconds'])) {
                $updateData['time_spent_seconds'] = $progress->time_spent_seconds + $validated['time_spent_seconds'];
            }

            $progress->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Progress berhasil diperbarui',
                'progress' => $progress
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to update progress'], 500);
        }
    }

    /**
     * Check if user has access to the course.
     */
    private function hasAccessToCourse($user, $course)
    {
        // Free courses are accessible to everyone
        if ($course->is_free || $course->price == 0) {
            return true;
        }

        // Course tutor has access
        if ($course->tutor_id === $user->id) {
            return true;
        }

        // Check enrollment
        return CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->where('status', 'active')
            ->exists();
    }

    /**
     * Get next lesson for user to continue.
     */
    private function getNextLesson($course, $userProgress)
    {
        foreach ($course->chapters as $chapter) {
            foreach ($chapter->lessons as $lesson) {
                $progress = $userProgress->get($lesson->id);
                if (!$progress || $progress->status !== 'completed') {
                    return $lesson;
                }
            }
        }
        return null; // All lessons completed
    }

    /**
     * Get lesson navigation (previous/next).
     */
    private function getLessonNavigation($course, $currentLesson)
    {
        $allLessons = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons;
        });

        $currentIndex = $allLessons->search(function ($lesson) use ($currentLesson) {
            return $lesson->id === $currentLesson->id;
        });

        return [
            'previous' => $currentIndex > 0 ? $allLessons[$currentIndex - 1] : null,
            'next' => $currentIndex < $allLessons->count() - 1 ? $allLessons[$currentIndex + 1] : null,
        ];
    }
}
