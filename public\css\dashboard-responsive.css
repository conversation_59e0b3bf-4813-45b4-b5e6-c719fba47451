/* Dashboard Responsive Styles */
/* Mobile-first responsive design for user dashboard */

/* Dashboard Container */
.dashboard-container {
  max-width: 100%;
  overflow-x: hidden;
}

/* Stats Cards Responsive Enhancements */
.stats-card {
  min-height: 140px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

/* Mobile optimizations (320px - 767px) */
@media (max-width: 767px) {
  /* Container adjustments */
  .dashboard-container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* Welcome header mobile optimizations */
  .welcome-header {
    padding: 1rem;
  }

  /* Quick stats mobile grid */
  .quick-stats-mobile {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quick-stats-mobile > div {
    background-color: rgba(59, 130, 246, 0.05);
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid rgba(59, 130, 246, 0.1);
  }

  /* Stats cards mobile layout */
  .stats-card {
    min-height: 120px;
    padding: 1rem;
  }

  .stats-card h3 {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
  }

  .stats-card .stat-number {
    font-size: 1.5rem;
    line-height: 1.2;
  }

  /* Course cards mobile layout */
  .course-card-mobile {
    padding: 0.75rem;
    border-radius: 0.5rem;
  }

  .course-card-mobile .course-thumbnail {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 0.75rem auto;
  }

  /* Activity items mobile */
  .activity-item-mobile {
    padding: 0.5rem;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  /* Quick links mobile grid */
  .quick-links-mobile {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .quick-link-item {
    min-height: 44px;
    padding: 0.75rem;
    justify-content: flex-start;
  }

  /* Account info mobile */
  .account-info-mobile {
    padding: 1rem;
  }

  .account-info-mobile .account-details {
    font-size: 0.75rem;
  }

  /* Button optimizations */
  .btn-mobile {
    min-height: 44px;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    width: 100%;
    justify-content: center;
  }

  /* Typography mobile scaling */
  .mobile-title {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  .mobile-subtitle {
    font-size: 0.875rem;
    line-height: 1.4;
  }

  .mobile-text {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}

/* Small mobile devices (320px - 480px) */
@media (max-width: 480px) {
  .dashboard-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  /* Extra compact stats cards */
  .stats-card {
    min-height: 100px;
    padding: 0.75rem;
  }

  .stats-card .stat-number {
    font-size: 1.25rem;
  }

  /* Smaller touch targets for very small screens */
  .btn-mobile {
    min-height: 40px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  /* Compact course cards */
  .course-card-mobile .course-thumbnail {
    width: 2.5rem;
    height: 2.5rem;
  }

  /* Smaller account info */
  .account-info-mobile {
    padding: 0.75rem;
  }
}

/* Tablet optimizations (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Two-column layout optimizations */
  .tablet-two-col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  /* Stats cards tablet layout */
  .stats-card {
    min-height: 160px;
  }

  /* Course cards tablet layout */
  .course-card-tablet {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
  }

  .course-card-tablet .course-thumbnail {
    width: 4rem;
    height: 4rem;
    flex-shrink: 0;
  }

  /* Activity section tablet */
  .activity-section-tablet {
    max-height: 400px;
    overflow-y: auto;
  }

  /* Quick links tablet grid */
  .quick-links-tablet {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

/* Large tablet and small desktop (1025px - 1280px) */
@media (min-width: 1025px) and (max-width: 1280px) {
  /* Optimize for smaller desktop screens */
  .stats-card {
    min-height: 180px;
  }

  /* Compact course recommendations */
  .course-recommendations-compact {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
  /* Touch device optimizations */
  .touch-friendly {
    min-height: 44px;
    min-width: 44px;
  }

  /* Larger tap targets */
  .stats-card a,
  .quick-link-item,
  .btn {
    min-height: 44px;
    padding: 0.75rem;
  }

  /* Remove hover effects on touch devices */
  .stats-card:hover {
    transform: none;
  }

  /* Enhanced focus states for accessibility */
  .stats-card:focus,
  .quick-link-item:focus,
  .btn:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .stats-card {
    border-width: 2px;
  }

  .quick-link-item {
    border-width: 2px;
  }

  .btn {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .stats-card,
  .quick-link-item,
  .btn {
    transition: none;
  }

  .stats-card:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .dashboard-container {
    background: white;
    color: black;
  }

  .stats-card,
  .quick-link-item {
    border: 1px solid #000;
    background: white;
  }

  .btn {
    display: none;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Utility classes for responsive design */
.mobile-only {
  display: block;
}

.tablet-only,
.desktop-only {
  display: none;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .tablet-only {
    display: block;
  }
}

@media (min-width: 1025px) {
  .tablet-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* Responsive spacing utilities */
.space-mobile {
  margin-bottom: 1rem;
}

.space-tablet {
  margin-bottom: 1.5rem;
}

.space-desktop {
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .space-mobile {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .space-mobile {
    margin-bottom: 2rem;
  }
  
  .space-tablet {
    margin-bottom: 2rem;
  }
}
