<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tutor_payment_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('tutor_id')->unique(); // Foreign key to users table

            // Bank Transfer Settings
            $table->string('bank_name')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_holder')->nullable();
            $table->string('bank_branch')->nullable();

            // E-Wallet Settings
            $table->string('gopay_number')->nullable();
            $table->string('ovo_number')->nullable();
            $table->string('dana_number')->nullable();
            $table->string('shopeepay_number')->nullable();

            // Tax Information
            $table->string('npwp_number')->nullable();
            $table->string('npwp_name')->nullable();
            $table->string('npwp_address')->nullable();
            $table->string('tax_status')->default('non_pkp'); // non_pkp, pkp

            // Preferred Payment Method
            $table->enum('preferred_method', ['bank_transfer', 'gopay', 'ovo', 'dana', 'shopeepay'])->default('bank_transfer');

            // Payout Settings
            $table->decimal('minimum_payout', 8, 2)->default(100000); // Minimum payout amount
            $table->enum('payout_frequency', ['manual', 'weekly', 'monthly'])->default('manual');
            $table->boolean('auto_payout_enabled')->default(false);

            // Verification Status
            $table->boolean('bank_verified')->default(false);
            $table->boolean('tax_verified')->default(false);
            $table->timestamp('bank_verified_at')->nullable();
            $table->timestamp('tax_verified_at')->nullable();

            // Additional Settings
            $table->json('notification_preferences')->nullable(); // Email, SMS preferences
            $table->text('notes')->nullable(); // Admin notes

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tutor_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index('tutor_id');
            $table->index(['bank_verified', 'tax_verified']);
            $table->index('preferred_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutor_payment_settings');
    }
};
