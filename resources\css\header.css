/* Header Component - Isolated CSS Styles */

/* Header Main Container */
.header-main {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

/* Logo Section */
.header-logo-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.header-logo-image {
    width: 2.5rem;
    height: 2.5rem;
}

.header-logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
}

/* Desktop Navigation */
.header-nav {
    display: none;
    align-items: center;
    gap: 2rem;
}

.header-nav-link {
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.header-nav-link:hover {
    color: #FF6B35;
}

/* Right Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-auth {
    display: none;
    align-items: center;
}

/* User Dropdown */
.header-user-dropdown {
    position: relative;
}

.header-user-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-user-button:hover {
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-user-avatar {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.header-user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
}

.header-user-role {
    font-size: 0.75rem;
    color: #6b7280;
}

.header-user-chevron {
    width: 1rem;
    height: 1rem;
    color: #9ca3af;
    transition: transform 0.2s ease;
}

/* User Dropdown Menu */
.header-user-menu {
    position: absolute;
    right: 0;
    top: calc(100% + 0.5rem);
    width: 16rem;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    display: none;
    z-index: 1001;
}

.header-user-menu.show {
    display: block;
}

.header-user-menu-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    margin-bottom: 0.5rem;
}

.header-user-menu-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.header-user-menu-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.header-user-menu-email {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-user-menu-items {
    padding: 0.5rem 0;
}

.header-user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
}

.header-user-menu-item:hover {
    background: #FF6B35;
    color: white;
}

.header-user-menu-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.header-user-menu-icon svg {
    width: 1rem;
    height: 1rem;
}

.header-user-menu-icon-primary {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
}

.header-user-menu-icon-yellow {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.header-user-menu-icon-blue {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.header-user-menu-icon-green {
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.header-user-menu-icon-red {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.header-user-menu-item:hover .header-user-menu-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.header-user-menu-title {
    font-weight: 500;
    margin: 0;
}

.header-user-menu-desc {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-user-menu-item:hover .header-user-menu-desc {
    color: rgba(255, 255, 255, 0.8);
}

.header-user-menu-divider {
    height: 1px;
    background: #f3f4f6;
    margin: 0.5rem 0;
}

.header-user-menu-logout {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: #374151;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-user-menu-logout:hover {
    background: #fef2f2;
    color: #dc2626;
}

/* Guest Actions */
.header-guest-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-guest-login {
    color: #6b7280;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.header-guest-login:hover {
    color: #FF6B35;
}

.header-guest-register {
    background: #FF6B35;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: background 0.2s ease;
}

.header-guest-register:hover {
    background: #E55A2B;
}

.header-guest-tutor {
    border: 1px solid #FF6B35;
    color: #FF6B35;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.header-guest-tutor:hover {
    background: #FF6B35;
    color: white;
}

/* Mobile Toggle */
.header-mobile-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: none;
    border: none;
    border-radius: 0.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-mobile-toggle:hover {
    background: #f3f4f6;
    color: #FF6B35;
}

.header-mobile-icon {
    width: 1.5rem;
    height: 1.5rem;
    transition: transform 0.2s ease;
}

/* Mobile Menu */
.header-mobile-menu {
    display: none;
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-mobile-menu.show {
    display: block;
}

.header-mobile-content {
    padding: 1.5rem 1rem;
}

.header-mobile-nav {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
}

.header-mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.header-mobile-nav-item:hover {
    background: #f3f4f6;
    color: #FF6B35;
    transform: translateX(0.25rem);
}

.header-mobile-nav-icon {
    width: 2rem;
    height: 2rem;
    background: #f3f4f6;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    transition: all 0.2s ease;
}

.header-mobile-nav-item:hover .header-mobile-nav-icon {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
}

.header-mobile-nav-icon svg {
    width: 1rem;
    height: 1rem;
}

/* Mobile User Section */
.header-mobile-user {
    border-top: 1px solid #f3f4f6;
    padding-top: 1.5rem;
}

.header-mobile-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.header-mobile-user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #FF6B35, #E55A2B);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.header-mobile-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.header-mobile-user-role {
    font-size: 0.75rem;
    color: #6b7280;
    margin: 0;
}

.header-mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.header-mobile-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s ease;
    transform: scale(1);
}

.header-mobile-btn:hover {
    transform: scale(1.02);
}

.header-mobile-btn:active {
    transform: scale(0.98);
}

.header-mobile-btn svg {
    width: 1rem;
    height: 1rem;
}

.header-mobile-btn-primary {
    background: #FF6B35;
    color: white;
}

.header-mobile-btn-primary:hover {
    background: #E55A2B;
}

.header-mobile-btn-outline {
    border: 1px solid #d1d5db;
    color: #374151;
    background: white;
}

.header-mobile-btn-outline:hover {
    background: #f3f4f6;
}

.header-mobile-btn-ghost {
    color: #374151;
    background: transparent;
}

.header-mobile-btn-ghost:hover {
    background: #f3f4f6;
}

.header-mobile-logout {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: #dc2626;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-mobile-logout:hover {
    background: #fef2f2;
}

.header-mobile-logout svg {
    width: 1rem;
    height: 1rem;
}

.header-mobile-guest {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Responsive Design */
@media (min-width: 768px) {
    .header-auth {
        display: flex;
    }
}

@media (min-width: 1024px) {
    .header-nav {
        display: flex;
    }

    .header-mobile-toggle {
        display: none;
    }
}

@media (min-width: 768px) and (max-width: 1023px) {
    .header-user-info {
        display: none;
    }

    .header-user-name {
        display: block;
        max-width: 6rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 640px) {
    .header-container {
        padding: 0 0.75rem;
    }

    .header-logo-image {
        width: 2rem;
        height: 2rem;
    }

    .header-logo-text {
        font-size: 1.125rem;
    }

    .header-user-menu {
        width: calc(100vw - 2rem);
        right: -0.75rem;
    }
}
