<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Payment;
use App\Models\User;
use App\Models\MembershipPlan;
use App\Models\Course;
use App\Models\Exam;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->numberBetween(25000, 500000);
        $platformFee = $amount * 0.05; // 5% platform fee
        $tutorEarnings = ($amount - $platformFee) * 0.60; // 60% to tutor

        return [
            'user_id' => User::factory(),
            'payment_type' => $this->faker->randomElement(['membership', 'course', 'exam', 'certification']),
            'payable_id' => null, // Will be set by specific factory methods
            'payable_type' => null, // Will be set by specific factory methods
            'transaction_id' => 'TXN-' . $this->faker->unique()->numerify('##########'),
            'external_transaction_id' => $this->faker->optional()->uuid(),
            'amount' => $amount,
            'platform_fee' => $platformFee,
            'tutor_earnings' => $tutorEarnings,
            'currency' => 'IDR',
            'status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'cancelled']),
            'paid_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 month', 'now'),
            'failed_at' => null,
            'cancelled_at' => null,
            'refunded_at' => null,
            'payment_method' => $this->faker->randomElement(['bank_transfer', 'credit_card', 'e_wallet', 'virtual_account']),
            'payment_gateway' => $this->faker->randomElement(['midtrans', 'xendit', 'gopay', 'ovo']),
            'gateway_response' => null,
            'notes' => $this->faker->optional()->sentence(),
            'receipt_url' => $this->faker->optional()->url(),
            'metadata' => null,
            'referrer_id' => $this->faker->optional(0.2)->randomElement([User::factory()]),
            'referral_bonus' => 0,
            'has_referral_bonus' => false,
        ];
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'paid_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'paid_at' => null,
        ]);
    }

    /**
     * Indicate that the payment is for a membership.
     */
    public function forMembership(): static
    {
        return $this->state(function (array $attributes) {
            $membershipPlan = MembershipPlan::factory()->create();
            return [
                'payment_type' => 'membership',
                'payable_id' => $membershipPlan->id,
                'payable_type' => MembershipPlan::class,
                'amount' => $membershipPlan->price,
                'tutor_earnings' => 0, // No tutor earnings for memberships
            ];
        });
    }

    /**
     * Indicate that the payment is for a course.
     */
    public function forCourse(): static
    {
        return $this->state(function (array $attributes) {
            $course = Course::factory()->create();
            $amount = $course->price;
            $platformFee = $amount * 0.05;
            $tutorEarnings = ($amount - $platformFee) * 0.60;

            return [
                'payment_type' => 'course',
                'payable_id' => $course->id,
                'payable_type' => Course::class,
                'amount' => $amount,
                'platform_fee' => $platformFee,
                'tutor_earnings' => $tutorEarnings,
            ];
        });
    }

    /**
     * Indicate that the payment is for an exam.
     */
    public function forExam(): static
    {
        return $this->state(function (array $attributes) {
            $exam = Exam::factory()->create();
            $amount = $exam->price;
            $platformFee = $amount * 0.05;
            $tutorEarnings = ($amount - $platformFee) * 0.60;

            return [
                'payment_type' => 'exam',
                'payable_id' => $exam->id,
                'payable_type' => Exam::class,
                'amount' => $amount,
                'platform_fee' => $platformFee,
                'tutor_earnings' => $tutorEarnings,
            ];
        });
    }

    /**
     * Indicate that the payment has referral bonus.
     */
    public function withReferral(): static
    {
        return $this->state(function (array $attributes) {
            $amount = $attributes['amount'];
            $platformFee = $amount * 0.05;
            $tutorEarnings = ($amount - $platformFee) * 0.80; // 80% with referral
            $referralBonus = $amount * 0.05; // 5% referral bonus

            return [
                'referrer_id' => User::factory(),
                'tutor_earnings' => $tutorEarnings,
                'referral_bonus' => $referralBonus,
                'has_referral_bonus' => true,
            ];
        });
    }
}
