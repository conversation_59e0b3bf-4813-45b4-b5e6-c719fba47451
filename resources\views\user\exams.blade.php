@extends('layouts.user')

@section('title', '<PERSON><PERSON><PERSON>')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/dashboard-responsive.css') }}">
@endpush

@section('content')
<div class="dashboard-container p-3 sm:p-4 lg:p-6 bg-gray-50 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="space-y-4 lg:space-y-0 lg:flex lg:items-center lg:justify-between">
                <div class="flex-1 text-center sm:text-left">
                    <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight"><PERSON><PERSON><PERSON></h1>
                    <p class="text-gray-600 mt-1 text-sm sm:text-base"><PERSON><PERSON><PERSON> u<PERSON><PERSON> yang telah <PERSON>a ikuti dan lihat hasil</p>
                </div>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 lg:flex-shrink-0">
                    <a href="{{ route('exams.index') }}" class="btn btn-outline min-h-[44px] justify-center text-sm sm:text-base">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <span>Jelajahi Ujian</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5 lg:gap-6 mb-6 lg:mb-8">
        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-blue-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Ujian</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_enrollments'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-green-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Ujian Lulus</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['passed_exams'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-purple-300">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Total Percobaan</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ $stats['total_attempts'] }}</p>
                </div>
            </div>
        </div>

        <div class="stats-card bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200 hover:shadow-md transition-all duration-200 hover:border-orange-300 sm:col-span-2 lg:col-span-1">
            <div class="flex items-center">
                <div class="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <p class="text-xs sm:text-sm font-medium text-gray-600 mb-1">Rata-rata Skor</p>
                    <p class="text-xl sm:text-2xl font-bold text-gray-900">{{ number_format($stats['average_score'], 1) }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrolled Exams -->
    <div class="bg-white rounded-lg shadow-sm mb-6 lg:mb-8">
        <div class="px-4 sm:px-5 lg:px-6 py-4 border-b border-gray-200">
            <h2 class="text-base sm:text-lg font-semibold text-gray-900">Ujian Terdaftar</h2>
        </div>
        <div class="p-4 sm:p-5 lg:p-6">
            @if($examEnrollments->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 lg:gap-6">
                    @foreach($examEnrollments as $enrollment)
                        <div class="bg-gray-50 rounded-lg p-4 sm:p-5 lg:p-6 border border-gray-200">
                            <div class="space-y-3 sm:space-y-0 sm:flex sm:items-start sm:justify-between mb-4">
                                <div class="flex-1 text-center sm:text-left">
                                    <h3 class="font-semibold text-gray-900 mb-1 text-sm sm:text-base line-clamp-2">{{ $enrollment->exam->title }}</h3>
                                    <p class="text-xs sm:text-sm text-gray-600 truncate">{{ $enrollment->exam->tutor->name }}</p>
                                </div>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full w-fit mx-auto sm:mx-0 flex-shrink-0">
                                    Terdaftar
                                </span>
                            </div>

                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-xs sm:text-sm">
                                    <span class="text-gray-600">Durasi:</span>
                                    <span class="font-medium">{{ $enrollment->exam->time_limit ?? 'Tidak terbatas' }} menit</span>
                                </div>
                                <div class="flex justify-between text-xs sm:text-sm">
                                    <span class="text-gray-600">Soal:</span>
                                    <span class="font-medium">{{ $enrollment->exam->questions->count() }} soal</span>
                                </div>
                                <div class="flex justify-between text-xs sm:text-sm">
                                    <span class="text-gray-600">Terdaftar:</span>
                                    <span class="font-medium">{{ $enrollment->enrolled_at->format('d M Y') }}</span>
                                </div>
                            </div>

                            <a href="{{ route('exams.show', $enrollment->exam) }}" class="w-full btn btn-primary min-h-[44px] text-xs sm:text-sm justify-center">
                                Lihat Detail
                            </a>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8 sm:py-12">
                    <div class="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-base sm:text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                    <p class="text-gray-600 mb-6 text-sm sm:text-base px-4">Anda belum mendaftar ujian apapun. Jelajahi ujian yang tersedia.</p>
                    <a href="{{ route('exams.index') }}" class="btn btn-primary min-h-[44px] w-full sm:w-auto">
                        Jelajahi Ujian
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Recent Attempts -->
    <div class="bg-white rounded-lg shadow-sm">
        <div class="px-4 sm:px-5 lg:px-6 py-4 border-b border-gray-200">
            <h2 class="text-base sm:text-lg font-semibold text-gray-900">Riwayat Ujian Terbaru</h2>
        </div>
        <div class="p-4 sm:p-5 lg:p-6">
            @if($examAttempts->count() > 0)
                <div class="space-y-3 sm:space-y-4">
                    @foreach($examAttempts as $attempt)
                        <div class="space-y-3 sm:space-y-0 sm:flex sm:items-center sm:justify-between p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <div class="flex-1 text-center sm:text-left">
                                <h4 class="font-medium text-gray-900 text-sm sm:text-base line-clamp-2">{{ $attempt->exam->title }}</h4>
                                <p class="text-xs sm:text-sm text-gray-600 truncate">{{ $attempt->exam->tutor->name }}</p>
                                <p class="text-xs text-gray-500 mt-1">{{ $attempt->created_at->format('d M Y, H:i') }}</p>
                            </div>
                            <div class="text-center sm:text-right">
                                <div class="space-y-2 sm:space-y-0 sm:flex sm:items-center sm:space-x-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $attempt->score_percentage }}%</p>
                                        {{-- <p class="text-xs text-gray-600">{{ $attempt->correct_answers }}/{{ $attempt->total_questions }}</p> --}}
                                        <p class="text-xs text-blue-600 font-medium">Percobaan: {{ $attempt->user_attempt_count }}/{{ $attempt->max_attempts }}</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $attempt->is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }} w-fit mx-auto sm:mx-0">
                                        {{ $attempt->is_passed ? 'Lulus' : 'Tidak Lulus' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-6 sm:py-8">
                    <p class="text-gray-600 text-sm sm:text-base">Belum ada riwayat ujian.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
