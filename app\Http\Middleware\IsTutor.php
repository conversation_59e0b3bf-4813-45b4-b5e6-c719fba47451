<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class IsTutor
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Anda harus login terlebih dahulu.');
        }

        $user = Auth::user();

        // Check if user is tutor, admin, or superadmin
        if (!$user->isTutor() && !$user->isAdmin() && !$user->isSuperAdmin()) {
            // Instead of throwing 403, redirect to tutor registration
            return redirect()->route('tutor.register.terms')->with('info', 'Untuk mengakses area tutor, <PERSON><PERSON> perlu mendaftar sebagai tutor terlebih dahulu.');
        }

        return $next($request);
    }
}
