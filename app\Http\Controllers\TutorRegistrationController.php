<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\TutorProfile;
use App\Models\User;
use App\Services\FileStorageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TutorRegistrationController extends Controller
{
    /**
     * Show the terms and conditions page (Step 1).
     */
    public function showTerms()
    {
        $user = Auth::user();

        // Check if user is already a tutor (has tutor role)
        if ($user->isTutor()) {
            return redirect()->route('tutor.dashboard')->with('success', 'Anda sudah menjadi tutor yang disetujui.');
        }

        // Check if user already has a tutor profile
        if ($user->hasTutorProfile()) {
            $profile = $user->tutorProfile;
            if ($profile->status === 'approved') {
                return redirect()->route('tutor.dashboard')->with('success', 'Anda sudah menjadi tutor yang disetujui.');
            }
            if ($profile->status === 'submitted' || $profile->status === 'under_review') {
                return redirect()->route('tutor.register.status')->with('info', 'Aplikasi Anda sedang dalam proses review.');
            }
        }

        return view('tutor.register.step1-terms');
    }

    /**
     * Process terms agreement and redirect to profile form.
     */
    public function processTerms(Request $request)
    {
        $request->validate([
            'terms_agreed' => 'required|accepted',
            'privacy_agreed' => 'required|accepted',
        ], [
            'terms_agreed.required' => 'Anda harus menyetujui Syarat dan Ketentuan.',
            'terms_agreed.accepted' => 'Anda harus menyetujui Syarat dan Ketentuan.',
            'privacy_agreed.required' => 'Anda harus menyetujui Kebijakan Privasi.',
            'privacy_agreed.accepted' => 'Anda harus menyetujui Kebijakan Privasi.',
        ]);

        $user = Auth::user();

        // Create or update tutor profile with agreement
        $profile = $user->tutorProfile()->firstOrCreate(
            ['user_id' => $user->id],
            [
                'terms_agreed' => true,
                'privacy_agreed' => true,
                'status' => 'draft'
            ]
        );

        if (!$profile->terms_agreed || !$profile->privacy_agreed) {
            $profile->update([
                'terms_agreed' => true,
                'privacy_agreed' => true,
            ]);
        }

        return redirect()->route('tutor.register.profile');
    }

    /**
     * Show the profile form (Step 2).
     */
    public function showProfile()
    {
        $user = Auth::user();
        $profile = $user->tutorProfile;

        // Redirect if no terms agreement
        if (!$profile || !$profile->terms_agreed || !$profile->privacy_agreed) {
            return redirect()->route('tutor.register.terms')->with('error', 'Anda harus menyetujui syarat dan ketentuan terlebih dahulu.');
        }

        // Refresh the profile to ensure we have the latest data
        $profile = $profile->fresh();

        return view('tutor.register.step2-profile', [
            'profile' => $profile,
            'educationLevels' => TutorProfile::getEducationLevels(),
            'identityTypes' => TutorProfile::getIdentityTypes(),
        ]);
    }

    /**
     * Process profile form submission.
     */
    public function processProfile(Request $request)
    {
        $user = Auth::user();
        $profile = $user->tutorProfile;

        if (!$profile) {
            return redirect()->route('tutor.register.terms')->with('error', 'Sesi tidak valid. Silakan mulai dari awal.');
        }

        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'public_name' => 'required|string|max:255|unique:tutor_profiles,public_name,' . $profile->id,
            'identity_number' => 'required|string|max:50|unique:tutor_profiles,identity_number,' . $profile->id,
            'identity_type' => ['required', Rule::in(array_keys(TutorProfile::getIdentityTypes()))],
            'phone_number' => 'required|string|max:20',
            'education_level' => ['required', Rule::in(array_keys(TutorProfile::getEducationLevels()))],
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'identity_photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'portfolio' => 'nullable|file|mimes:pdf|max:2048',
            'description' => 'nullable|string|max:1000',
            'long_description' => 'nullable|string|max:10000',
        ], [
            'full_name.required' => 'Nama lengkap wajib diisi.',
            'public_name.required' => 'Nama publik wajib diisi.',
            'public_name.unique' => 'Nama publik sudah digunakan, silakan pilih yang lain.',
            'identity_number.required' => 'Nomor identitas wajib diisi.',
            'identity_number.unique' => 'Nomor identitas sudah terdaftar.',
            'identity_type.required' => 'Jenis identitas wajib dipilih.',
            'identity_type.in' => 'Jenis identitas yang dipilih tidak valid.',
            'phone_number.required' => 'Nomor telepon wajib diisi.',
            'education_level.required' => 'Pendidikan terakhir wajib dipilih.',
            'education_level.in' => 'Pendidikan terakhir yang dipilih tidak valid.',
            'profile_picture.image' => 'Foto profil harus berupa gambar.',
            'profile_picture.mimes' => 'Foto profil harus berformat jpeg, png, atau jpg.',
            'profile_picture.max' => 'Ukuran foto profil maksimal 2MB.',
            'identity_photo.image' => 'Foto identitas harus berupa gambar.',
            'identity_photo.mimes' => 'Foto identitas harus berformat jpeg, png, atau jpg.',
            'identity_photo.max' => 'Ukuran foto identitas maksimal 2MB.',
            'portfolio.file' => 'Portfolio harus berupa file.',
            'portfolio.mimes' => 'Portfolio harus berupa file PDF.',
            'portfolio.max' => 'Ukuran portfolio maksimal 2MB.',
            'description.max' => 'Deskripsi singkat maksimal 1000 karakter.',
            'long_description.max' => 'Deskripsi lengkap maksimal 10.000 karakter.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->only([
            'full_name', 'public_name', 'identity_number', 'identity_type',
            'phone_number', 'education_level', 'description', 'long_description'
        ]);

        // Generate slug for public name if it's provided
        if (!empty($data['public_name'])) {
            $data['public_name_slug'] = $profile->generateSlug($data['public_name']);
        }

        // Ensure storage directories exist
        FileStorageService::ensureDirectoriesExist();

        // Create specific tutor directories if they don't exist
        if ($profile->id) {
            Storage::disk('local')->makeDirectory("tutor/{$profile->id}");
            Storage::disk('local')->makeDirectory("tutor/{$profile->id}/ktp");
            Storage::disk('local')->makeDirectory("tutor/{$profile->id}/portfolio");
            Storage::disk('local')->makeDirectory("tutor/{$profile->id}/npwp");
        }

        // Handle profile picture upload (public file)
        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture
            if ($user->profile_picture) {
                FileStorageService::deletePublicFile($user->profile_picture);
            }

            $profilePicturePath = FileStorageService::storePublicUserFile(
                $request->file('profile_picture'),
                $user->id,
                'profile'
            );

            $user->update(['profile_picture' => $profilePicturePath]);
        }

        // Handle private document uploads for tutor
        if ($request->hasFile('identity_photo')) {
            // Delete old identity photo
            if ($profile->identity_photo_path) {
                FileStorageService::deletePrivateFile($profile->identity_photo_path);
            }

            $data['identity_photo_path'] = FileStorageService::storePrivateTutorFile(
                $request->file('identity_photo'),
                $profile->id,
                'ktp'
            );
        }

        if ($request->hasFile('portfolio')) {
            // Delete old portfolio
            if ($profile->portfolio_path) {
                FileStorageService::deletePrivateFile($profile->portfolio_path);
            }

            $data['portfolio_path'] = FileStorageService::storePrivateTutorFile(
                $request->file('portfolio'),
                $profile->id,
                'portfolio'
            );
        }



        $profile->update($data);

        // Go to review page for data confirmation
        return redirect()->route('tutor.register.review');
    }

    /**
     * Show review and submit page (Step 3).
     */
    public function showReview()
    {
        $user = Auth::user();
        $profile = $user->tutorProfile;

        if (!$profile || !$profile->isComplete()) {
            return redirect()->route('tutor.register.profile')->with('error', 'Profil belum lengkap. Silakan lengkapi terlebih dahulu.');
        }

        return view('tutor.register.step3-review', compact('profile'));
    }

    /**
     * Submit application - Auto-approve and redirect to tutor dashboard.
     */
    public function submitApplication()
    {
        $user = Auth::user();
        $profile = $user->tutorProfile;

        if (!$profile || !$profile->isComplete()) {
            return redirect()->route('tutor.register.profile')->with('error', 'Profil belum lengkap.');
        }

        // AUTO-APPROVAL: Langsung approve tanpa memberitahu user bahwa ini otomatis
        $profile->update([
            'status' => 'approved',
            'submitted_at' => now(),
            'reviewed_at' => now(),
            'reviewed_by' => null, // Auto-approved
        ]);

        // Assign roles to user using RBAC system
        // Ensure user has basic user role first
        if (!$user->hasRole(Role::USER)) {
            $user->assignRole(Role::USER);
        }
        // Assign tutor role
        $user->assignRole(Role::TUTOR);

        // Update tutor status in user table
        $user->update([
            'tutor_status' => 'approved'
        ]);

        // Langsung redirect ke tutor dashboard dengan pesan selamat datang
        return redirect()->route('tutor.dashboard')->with('success', 'Selamat! Anda sekarang menjadi tutor di Ngambiskuy. Selamat datang di dashboard tutor Anda!');
    }

    /**
     * Show application status.
     */
    public function showStatus()
    {
        $user = Auth::user();
        $profile = $user->tutorProfile;

        if (!$profile) {
            return redirect()->route('tutor.register.terms');
        }

        return view('tutor.register.status', compact('profile'));
    }
}
