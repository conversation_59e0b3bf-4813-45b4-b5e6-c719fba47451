<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class TutorPaymentSettings extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tutor_id',
        'bank_name',
        'bank_account_number',
        'bank_account_holder',
        'bank_branch',
        'gopay_number',
        'ovo_number',
        'dana_number',
        'shopeepay_number',
        'npwp_number',
        'npwp_name',
        'npwp_address',
        'tax_status',
        'preferred_method',
        'minimum_payout',
        'payout_frequency',
        'auto_payout_enabled',
        'bank_verified',
        'tax_verified',
        'bank_verified_at',
        'tax_verified_at',
        'notification_preferences',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'minimum_payout' => 'decimal:2',
        'auto_payout_enabled' => 'boolean',
        'bank_verified' => 'boolean',
        'tax_verified' => 'boolean',
        'bank_verified_at' => 'datetime',
        'tax_verified_at' => 'datetime',
        'notification_preferences' => 'array',
    ];

    /**
     * Get the tutor that owns the payment settings.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    /**
     * Check if bank details are complete.
     */
    public function hasBankDetails(): bool
    {
        return !empty($this->bank_name) &&
               !empty($this->bank_account_number) &&
               !empty($this->bank_account_holder);
    }

    /**
     * Check if any e-wallet is configured.
     */
    public function hasEWalletDetails(): bool
    {
        return !empty($this->gopay_number) ||
               !empty($this->ovo_number) ||
               !empty($this->dana_number) ||
               !empty($this->shopeepay_number);
    }

    /**
     * Check if tax information is complete.
     */
    public function hasTaxDetails(): bool
    {
        return !empty($this->npwp_number) && !empty($this->npwp_name);
    }

    /**
     * Check if payment settings are complete for payouts.
     */
    public function isCompleteForPayouts(): bool
    {
        $hasPaymentMethod = false;

        switch ($this->preferred_method) {
            case 'bank_transfer':
                $hasPaymentMethod = $this->hasBankDetails();
                break;
            case 'gopay':
                $hasPaymentMethod = !empty($this->gopay_number);
                break;
            case 'ovo':
                $hasPaymentMethod = !empty($this->ovo_number);
                break;
            case 'dana':
                $hasPaymentMethod = !empty($this->dana_number);
                break;
            case 'shopeepay':
                $hasPaymentMethod = !empty($this->shopeepay_number);
                break;
        }

        return $hasPaymentMethod && $this->hasTaxDetails();
    }

    /**
     * Get the payment details for the preferred method.
     */
    public function getPreferredPaymentDetails(): array
    {
        switch ($this->preferred_method) {
            case 'bank_transfer':
                return [
                    'method' => 'bank_transfer',
                    'bank_name' => $this->bank_name,
                    'account_number' => $this->bank_account_number,
                    'account_holder' => $this->bank_account_holder,
                    'branch' => $this->bank_branch,
                ];
            case 'gopay':
                return [
                    'method' => 'gopay',
                    'phone_number' => $this->gopay_number,
                ];
            case 'ovo':
                return [
                    'method' => 'ovo',
                    'phone_number' => $this->ovo_number,
                ];
            case 'dana':
                return [
                    'method' => 'dana',
                    'phone_number' => $this->dana_number,
                ];
            case 'shopeepay':
                return [
                    'method' => 'shopeepay',
                    'phone_number' => $this->shopeepay_number,
                ];
            default:
                return [];
        }
    }

    /**
     * Get formatted minimum payout amount.
     */
    public function getFormattedMinimumPayoutAttribute(): string
    {
        return 'IDR ' . number_format($this->minimum_payout, 0, ',', '.');
    }

    /**
     * Get preferred method name in Indonesian.
     */
    public function getPreferredMethodNameAttribute(): string
    {
        return match($this->preferred_method) {
            'bank_transfer' => 'Transfer Bank',
            'gopay' => 'GoPay',
            'ovo' => 'OVO',
            'dana' => 'DANA',
            'shopeepay' => 'ShopeePay',
            default => ucfirst($this->preferred_method)
        };
    }

    /**
     * Get payout frequency name in Indonesian.
     */
    public function getPayoutFrequencyNameAttribute(): string
    {
        return match($this->payout_frequency) {
            'manual' => 'Manual',
            'weekly' => 'Mingguan',
            'monthly' => 'Bulanan',
            default => ucfirst($this->payout_frequency)
        };
    }
}
