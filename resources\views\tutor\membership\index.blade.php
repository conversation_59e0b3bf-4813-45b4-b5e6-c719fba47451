@extends('layouts.tutor')

@section('title', 'Tutor Membership - Ngambiskuy')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/tutor-dashboard-responsive.css') }}">
@endpush

@section('content')
<div class="tutor-dashboard-container p-3 sm:p-4 lg:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Header -->
    <div class="mb-6 lg:mb-8">
        <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
            <div class="text-center sm:text-left">
                <h1 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 leading-tight">Tutor Membership - NALA AI Assistant</h1>
                <p class="text-gray-600 mt-2 text-sm sm:text-base">Kelola membership dan akses fitur AI-powered teaching tools untuk tutor</p>
            </div>
        </div>
    </div>

    @if($activeMembership)
    <!-- Current Membership -->
    <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 mb-6 lg:mb-8 border border-gray-200">
        <div class="space-y-4 sm:space-y-0 sm:flex sm:items-center sm:justify-between mb-4 sm:mb-6">
            <h2 class="text-lg sm:text-xl font-semibold text-gray-900 text-center sm:text-left">Current Membership</h2>
            <span class="px-3 py-1 bg-emerald-100 text-emerald-800 rounded-full text-xs sm:text-sm font-medium w-fit mx-auto sm:mx-0">
                {{ ucfirst($activeMembership->status) }}
            </span>
        </div>

        <div class="space-y-6 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-6">
            <!-- Plan Info -->
            <div class="lg:col-span-2">
                <h3 class="text-base sm:text-lg font-semibold text-gray-900 mb-2 text-center sm:text-left">
                    {{ $activeMembership->membershipPlan->name }} Plan
                </h3>
                <p class="text-gray-600 mb-4 text-sm sm:text-base text-center sm:text-left">{{ $activeMembership->membershipPlan->description }}</p>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm">
                    <div class="text-center sm:text-left">
                        <span class="text-gray-500 block sm:inline">Started:</span>
                        <span class="font-medium block sm:inline sm:ml-1">{{ $activeMembership->starts_at->format('d M Y') }}</span>
                    </div>
                    <div class="text-center sm:text-left">
                        <span class="text-gray-500 block sm:inline">Expires:</span>
                        <span class="font-medium block sm:inline sm:ml-1">{{ $activeMembership->formatted_expiration }}</span>
                    </div>
                    @if($activeMembership->days_remaining)
                    <div class="text-center sm:text-left sm:col-span-2">
                        <span class="text-gray-500 block sm:inline">Days Remaining:</span>
                        <span class="font-medium text-emerald-600 block sm:inline sm:ml-1">{{ $activeMembership->days_remaining }} days</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- NALA Usage -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-semibold text-gray-900 mb-3 text-center text-sm sm:text-base">NALA Course Builder Usage</h4>

                @if($activeMembership->has_unlimited_nala)
                <div class="text-center">
                    <div class="text-xl sm:text-2xl font-bold text-emerald-600">∞</div>
                    <p class="text-xs sm:text-sm text-gray-600">Unlimited Prompts</p>
                </div>
                @else
                <div class="text-center">
                    <div class="text-xl sm:text-2xl font-bold text-gray-900">
                        {{ $activeMembership->nala_prompts_remaining }}
                    </div>
                    <p class="text-xs sm:text-sm text-gray-600">
                        of {{ $activeMembership->nala_prompts_allocated }} remaining
                    </p>

                    <!-- Progress Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        @php
                            $percentage = $activeMembership->nala_prompts_allocated > 0
                                ? ($activeMembership->nala_prompts_remaining / $activeMembership->nala_prompts_allocated) * 100
                                : 0;
                        @endphp
                        <div class="bg-emerald-600 h-2 rounded-full transition-all duration-300" style="width: {{ $percentage }}%"></div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Tutor Features -->
        <div class="mt-6 pt-6 border-t border-gray-200">
            <h4 class="font-semibold text-gray-900 mb-3 text-center sm:text-left text-sm sm:text-base">Your Tutor Features</h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                <div class="flex items-center justify-center sm:justify-start p-2 sm:p-0">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 {{ $activeMembership->has_blog_access ? 'text-emerald-500' : 'text-gray-400' }} mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-xs sm:text-sm {{ $activeMembership->has_blog_access ? 'text-gray-900' : 'text-gray-500' }}">
                        Blog Creation
                    </span>
                </div>

                <div class="flex items-center justify-center sm:justify-start p-2 sm:p-0">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 {{ $activeMembership->has_ai_teaching_assistants_courses ? 'text-emerald-500' : 'text-gray-400' }} mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-xs sm:text-sm {{ $activeMembership->has_ai_teaching_assistants_courses ? 'text-gray-900' : 'text-gray-500' }}">
                        NALA Course Builder
                    </span>
                </div>

                <div class="flex items-center justify-center sm:justify-start p-2 sm:p-0">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 {{ $activeMembership->has_ice_full ? 'text-emerald-500' : 'text-gray-400' }} mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-xs sm:text-sm {{ $activeMembership->has_ice_full ? 'text-gray-900' : 'text-gray-500' }}">
                        Enhanced Analytics
                    </span>
                </div>

                <div class="flex items-center justify-center sm:justify-start p-2 sm:p-0">
                    <svg class="w-4 h-4 sm:w-5 sm:h-5 {{ $activeMembership->has_priority_support ? 'text-emerald-500' : 'text-gray-400' }} mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-xs sm:text-sm {{ $activeMembership->has_priority_support ? 'text-gray-900' : 'text-gray-500' }}">
                        Priority Support
                    </span>
                </div>
            </div>
        </div>
    </div>
    @else
    <!-- Membership Loading Error -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 sm:p-5 lg:p-6 mb-6 lg:mb-8">
        <div class="space-y-3 sm:space-y-0 sm:flex sm:items-center">
            <svg class="w-5 h-5 sm:w-6 sm:h-6 text-red-600 mr-0 sm:mr-3 mx-auto sm:mx-0 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <div class="text-center sm:text-left flex-1">
                <h3 class="text-base sm:text-lg font-semibold text-red-800">Membership Not Found</h3>
                <p class="text-red-700 text-sm sm:text-base">Unable to load your membership information. Please contact support if this issue persists.</p>
            </div>
        </div>
        <div class="mt-4 text-center sm:text-left">
            <a href="{{ route('payment.pricing') }}"
               class="bg-emerald-600 text-white px-4 py-2 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 min-h-[44px] inline-flex items-center justify-center text-sm sm:text-base">
                Get Membership
            </a>
        </div>
    </div>
    @endif

    <!-- Tutor AI Features Highlight -->
    <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 mb-6 lg:mb-8 border border-emerald-200">
        <div class="space-y-4 sm:space-y-0 sm:flex sm:items-start sm:space-x-4">
            <div class="w-10 h-10 sm:w-12 sm:h-12 bg-emerald-600 rounded-full flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0">
                <svg class="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
            </div>
            <div class="flex-1 text-center sm:text-left">
                <h2 class="text-lg sm:text-xl font-semibold text-emerald-900 mb-2">Unlock Advanced Tutor Tools</h2>
                <p class="text-emerald-700 mb-4 text-sm sm:text-base">
                    Tingkatkan kualitas mengajar Anda dengan akses ke NALA AI Course Builder, blog creation tools, 
                    enhanced analytics, dan fitur tutor premium lainnya untuk membangun personal brand dan meningkatkan engagement.
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div class="flex items-center justify-center sm:justify-start space-x-2">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-xs sm:text-sm text-emerald-800">NALA AI Course Builder</span>
                    </div>
                    <div class="flex items-center justify-center sm:justify-start space-x-2">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-xs sm:text-sm text-emerald-800">Blog Creation for Personal Branding</span>
                    </div>
                    <div class="flex items-center justify-center sm:justify-start space-x-2">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-xs sm:text-sm text-emerald-800">Enhanced Analytics & Insights</span>
                    </div>
                    <div class="flex items-center justify-center sm:justify-start space-x-2">
                        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-emerald-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        <span class="text-xs sm:text-sm text-emerald-800">Priority Support</span>
                    </div>
                </div>
                @if($activeMembership && $activeMembership->membershipPlan->is_free)
                    <div class="mt-4">
                        <a href="{{ route('payment.pricing') }}" class="inline-flex items-center px-4 py-2 bg-emerald-600 text-white text-xs sm:text-sm font-medium rounded-md hover:bg-emerald-700 transition-colors min-h-[44px] w-full sm:w-auto justify-center">
                            <svg class="w-3 h-3 sm:w-4 sm:h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span>Upgrade Tutor Membership</span>
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Membership History -->
    <div class="bg-white rounded-lg shadow-sm p-4 sm:p-5 lg:p-6 border border-gray-200">
        <h2 class="text-lg sm:text-xl font-semibold text-gray-900 mb-4 text-center sm:text-left">Membership History</h2>

        @if($membershipHistory->count() > 0)
        <!-- Mobile Card Layout -->
        <div class="block sm:hidden space-y-4">
            @foreach($membershipHistory as $membership)
            <div class="border border-gray-200 rounded-lg p-4">
                <div class="space-y-2">
                    <div class="flex justify-between items-start">
                        <div class="font-medium text-gray-900 text-sm">{{ $membership->membershipPlan->name }}</div>
                        <span class="px-2 py-1 text-xs leading-5 font-semibold rounded-full
                            {{ $membership->status === 'active' ? 'bg-green-100 text-green-800' :
                               ($membership->status === 'expired' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ ucfirst($membership->status) }}
                        </span>
                    </div>
                    <div class="text-xs text-gray-600">
                        <div>{{ $membership->starts_at->format('d M Y') }} - {{ $membership->formatted_expiration }}</div>
                    </div>
                    <div class="text-sm font-medium text-gray-900">
                        @if($membership->payment)
                            {{ $membership->payment->formatted_amount }}
                        @else
                            Free
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Desktop Table Layout -->
        <div class="hidden sm:block overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                        <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                        <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($membershipHistory as $membership)
                    <tr class="hover:bg-gray-50">
                        <td class="px-4 lg:px-6 py-4">
                            <div class="text-sm font-medium text-gray-900">{{ $membership->membershipPlan->name }}</div>
                        </td>
                        <td class="px-4 lg:px-6 py-4 text-sm text-gray-900">
                            <div class="break-words">{{ $membership->starts_at->format('d M Y') }} - {{ $membership->formatted_expiration }}</div>
                        </td>
                        <td class="px-4 lg:px-6 py-4">
                            <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $membership->status === 'active' ? 'bg-green-100 text-green-800' :
                                   ($membership->status === 'expired' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800') }}">
                                {{ ucfirst($membership->status) }}
                            </span>
                        </td>
                        <td class="px-4 lg:px-6 py-4 text-sm text-gray-900">
                            @if($membership->payment)
                                {{ $membership->payment->formatted_amount }}
                            @else
                                Free
                            @endif
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-8">
            <p class="text-gray-600 text-sm sm:text-base">No membership history found.</p>
        </div>
        @endif
    </div>
</div>

@endsection
