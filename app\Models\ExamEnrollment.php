<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class ExamEnrollment extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'exam_id',
        'enrolled_at',
        'payment_status',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'is_active',
        'expires_at',
        'attempts_used',
        'best_score',
        'has_passed',
        'first_attempt_at',
        'last_attempt_at',
        'passed_at',
        'certificate_issued',
        'certificate_issued_at',
        'certificate_number',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'enrolled_at' => 'datetime',
        'expires_at' => 'datetime',
        'first_attempt_at' => 'datetime',
        'last_attempt_at' => 'datetime',
        'passed_at' => 'datetime',
        'certificate_issued_at' => 'datetime',
        'amount_paid' => 'decimal:2',
        'best_score' => 'decimal:2',
        'is_active' => 'boolean',
        'has_passed' => 'boolean',
        'certificate_issued' => 'boolean',
    ];

    /**
     * Get the user that owns the enrollment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the exam that the enrollment belongs to.
     */
    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }



    /**
     * Check if payment is completed.
     */
    public function isPaid()
    {
        return $this->payment_status === 'completed';
    }

    /**
     * Check if payment is pending.
     */
    public function isPending()
    {
        return $this->payment_status === 'pending';
    }
}
