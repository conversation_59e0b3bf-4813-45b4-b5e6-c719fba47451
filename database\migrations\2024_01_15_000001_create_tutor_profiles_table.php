<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tutor_profiles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');

            // Personal Information
            $table->string('full_name')->nullable();
            $table->string('public_name')->nullable(); // Display name for courses/public profile
            $table->string('public_name_slug')->nullable()->unique(); // URL-friendly slug
            $table->string('identity_number')->nullable(); // KTP/SIM/Passport
            $table->string('identity_type')->default('KTP'); // KTP, SIM, Passport
            $table->string('phone_number')->nullable();

            // Education
            $table->string('education_level')->nullable(); // Dropdown selection

            // Documents
            $table->string('identity_photo_path')->nullable(); // KTP/SIM/Passport photo
            $table->string('portfolio_path')->nullable(); // Portfolio/Resume PDF

            // Additional Information
            $table->text('description')->nullable(); // Short description
            $table->longText('long_description')->nullable(); // Detailed "About me" section for public profile

            // Agreement
            $table->boolean('terms_agreed')->default(false);
            $table->boolean('privacy_agreed')->default(false);

            // Status and Timestamps
            $table->enum('status', ['draft', 'submitted', 'under_review', 'approved', 'rejected'])->default('draft');
            $table->text('rejection_reason')->nullable();
            $table->timestamp('submitted_at')->nullable();
            $table->timestamp('reviewed_at')->nullable();
            $table->uuid('reviewed_by')->nullable();

            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('reviewed_by')->references('id')->on('users')->onDelete('set null');

            // Indexes
            $table->index('user_id');
            $table->index('status');
            $table->index('identity_number');
            $table->index('public_name_slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutor_profiles');
    }
};
