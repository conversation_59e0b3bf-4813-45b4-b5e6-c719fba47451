<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (!Schema::hasColumn('users', 'terms_agreed')) {
                $table->boolean('terms_agreed')->default(false)->after('password');
            }
            if (!Schema::hasColumn('users', 'privacy_agreed')) {
                $table->boolean('privacy_agreed')->default(false)->after('terms_agreed');
            }
            if (!Schema::hasColumn('users', 'agreement_date')) {
                $table->timestamp('agreement_date')->nullable()->after('privacy_agreed');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $columnsToRemove = [];
            
            if (Schema::hasColumn('users', 'terms_agreed')) {
                $columnsToRemove[] = 'terms_agreed';
            }
            if (Schema::hasColumn('users', 'privacy_agreed')) {
                $columnsToRemove[] = 'privacy_agreed';
            }
            if (Schema::hasColumn('users', 'agreement_date')) {
                $columnsToRemove[] = 'agreement_date';
            }
            
            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};
