<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Course;
use App\Models\CourseChapter;
use App\Models\CourseLesson;
use App\Models\Category;
use App\Models\MembershipPlan;
use App\Models\UserMembership;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class NalaAIMaterialBuilderTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $tutor;
    protected $course;
    protected $chapter;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a category
        $category = Category::create([
            'name' => 'Programming',
            'slug' => 'programming',
            'is_active' => true,
            'sort_order' => 1
        ]);

        // Create a tutor user with membership
        $this->tutor = User::create([
            'name' => 'Test Tutor',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_tutor' => true
        ]);

        // Create a membership plan
        $membershipPlan = MembershipPlan::create([
            'name' => 'Standard Plan',
            'slug' => 'standard',
            'description' => 'Standard membership plan',
            'type' => 'individual',
            'price' => 99000,
            'duration_months' => 1,
            'nala_prompts' => 300,
            'is_active' => true
        ]);

        // Create active membership for tutor
        UserMembership::create([
            'user_id' => $this->tutor->id,
            'membership_plan_id' => $membershipPlan->id,
            'status' => 'active',
            'starts_at' => now()->subDay(),
            'expires_at' => now()->addMonth(),
            'nala_prompts_allocated' => 300,
            'nala_prompts_remaining' => 300
        ]);

        // Create a course
        $this->course = Course::create([
            'tutor_id' => $this->tutor->id,
            'category_id' => $category->id,
            'title' => 'Test Course',
            'slug' => 'test-course',
            'description' => 'A test course for AI material builder',
            'level' => 'beginner',
            'duration' => 120,
            'price' => 100000,
            'status' => 'published'
        ]);

        // Create a chapter
        $this->chapter = CourseChapter::create([
            'course_id' => $this->course->id,
            'title' => 'Introduction to Programming',
            'slug' => 'introduction-to-programming',
            'description' => 'Learn the basics of programming',
            'sort_order' => 1,
            'is_published' => true
        ]);
    }

    /** @test */
    public function it_can_generate_material_suggestion_with_no_existing_materials()
    {
        $this->actingAs($this->tutor);

        // Test the controller method directly to bypass routing issues
        $controller = new \App\Http\Controllers\Tutor\AICourseBuilderController();

        // Create a mock request
        $request = new \Illuminate\Http\Request();

        try {
            $response = $controller->generateMaterialSuggestion($request, $this->course->slug, $this->chapter->slug);

            // Check if response is successful
            $this->assertEquals(200, $response->getStatusCode());

            $responseData = json_decode($response->getContent(), true);

            // Verify response structure
            $this->assertTrue($responseData['success']);
            $this->assertArrayHasKey('material_suggestion', $responseData);
            $this->assertArrayHasKey('title', $responseData['material_suggestion']);
            $this->assertArrayHasKey('description', $responseData['material_suggestion']);
            $this->assertArrayHasKey('type', $responseData['material_suggestion']);
            $this->assertArrayHasKey('duration_minutes', $responseData['material_suggestion']);
            $this->assertArrayHasKey('content_outline', $responseData['material_suggestion']);

            // Verify the enhanced functionality - check that existing materials context is empty for new chapter
            $this->assertNotEmpty($responseData['material_suggestion']['title']);
            $this->assertNotEmpty($responseData['material_suggestion']['description']);
            $this->assertContains($responseData['material_suggestion']['type'], ['video', 'text', 'quiz', 'assignment']);
            $this->assertIsNumeric($responseData['material_suggestion']['duration_minutes']);
            $this->assertGreaterThanOrEqual(5, $responseData['material_suggestion']['duration_minutes']);
            $this->assertLessThanOrEqual(60, $responseData['material_suggestion']['duration_minutes']);

        } catch (\Exception $e) {
            // If AI fails, it should return fallback
            $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response ?? null);
        }
    }

    /** @test */
    public function it_can_generate_material_suggestion_with_existing_materials()
    {
        // Create some existing materials
        CourseLesson::create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'title' => 'What is Programming?',
            'slug' => 'what-is-programming',
            'description' => 'Introduction to programming concepts',
            'type' => 'video',
            'duration_minutes' => 15,
            'sort_order' => 1,
            'is_published' => true
        ]);

        CourseLesson::create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'title' => 'Programming Languages Overview',
            'slug' => 'programming-languages-overview',
            'description' => 'Overview of different programming languages',
            'type' => 'text',
            'duration_minutes' => 20,
            'sort_order' => 2,
            'is_published' => true
        ]);

        $this->actingAs($this->tutor);

        // Test the controller method directly to bypass routing issues
        $controller = new \App\Http\Controllers\Tutor\AICourseBuilderController();

        // Create a mock request
        $request = new \Illuminate\Http\Request();

        try {
            $response = $controller->generateMaterialSuggestion($request, $this->course->slug, $this->chapter->slug);

            // Check if response is successful
            $this->assertEquals(200, $response->getStatusCode());

            $responseData = json_decode($response->getContent(), true);

            // Verify response structure
            $this->assertTrue($responseData['success']);
            $this->assertArrayHasKey('material_suggestion', $responseData);

            // The AI should generate a suggestion that complements existing materials
            $suggestion = $responseData['material_suggestion'];
            $this->assertNotEmpty($suggestion['title']);
            $this->assertNotEmpty($suggestion['description']);
            $this->assertContains($suggestion['type'], ['video', 'text', 'quiz', 'assignment']);
            $this->assertIsNumeric($suggestion['duration_minutes']);
            $this->assertGreaterThanOrEqual(5, $suggestion['duration_minutes']);
            $this->assertLessThanOrEqual(60, $suggestion['duration_minutes']);

            // Verify that the suggestion is different from existing materials
            // (This tests that the enhanced functionality considers existing content)
            $this->assertNotEquals('What is Programming?', $suggestion['title']);
            $this->assertNotEquals('Programming Languages Overview', $suggestion['title']);

        } catch (\Exception $e) {
            // If AI fails, it should return fallback
            $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $response ?? null);
        }
    }

    /** @test */
    public function it_fetches_existing_materials_for_ai_context()
    {
        // Create some existing materials with different types
        CourseLesson::create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'title' => 'Introduction Video',
            'slug' => 'introduction-video',
            'description' => 'A video introduction to the topic',
            'type' => 'video',
            'duration_minutes' => 10,
            'sort_order' => 1,
            'is_published' => true
        ]);

        CourseLesson::create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'title' => 'Reading Material',
            'slug' => 'reading-material',
            'description' => 'Essential reading for understanding concepts',
            'type' => 'text',
            'duration_minutes' => 25,
            'sort_order' => 2,
            'is_published' => true
        ]);

        $this->actingAs($this->tutor);

        // Test the controller method directly
        $controller = new \App\Http\Controllers\Tutor\AICourseBuilderController();
        $request = new \Illuminate\Http\Request();

        $response = $controller->generateMaterialSuggestion($request, $this->course->slug, $this->chapter->slug);

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertTrue($responseData['success']);

        // Verify that the AI generates a suggestion that's different from existing materials
        $suggestion = $responseData['material_suggestion'];
        $this->assertNotEquals('Introduction Video', $suggestion['title']);
        $this->assertNotEquals('Reading Material', $suggestion['title']);

        // The suggestion should be complementary (e.g., if we have video and text, maybe suggest quiz or assignment)
        $this->assertNotEmpty($suggestion['title']);
        $this->assertNotEmpty($suggestion['description']);
    }
}
