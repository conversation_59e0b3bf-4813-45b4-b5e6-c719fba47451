<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\TutorProfile;
use Illuminate\Database\Seeder;

class TutorProfileSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get tutor users
        $sariDewi = User::where('email', '<EMAIL>')->first();
        $ahmadRahman = User::where('email', '<EMAIL>')->first();
        $superAdmin = User::where('email', '<EMAIL>')->first();
        $admin = User::where('email', '<EMAIL>')->first();

        // Create profile for Sa<PERSON>
        if ($sariDewi) {
            TutorProfile::create([
                'user_id' => $sariDewi->id,
                'full_name' => 'Sari Dewi Maharani',
                'public_name' => 'Sari Dewi',
                'public_name_slug' => 'sari-dewi',
                'identity_number' => '3201234567890123',
                'identity_type' => 'KTP',
                'phone_number' => '+6281234567890',
                'education_level' => 'S2',
                'description' => 'Data Scientist berpengalaman 8+ tahun dengan spesialisasi Machine Learning dan AI. Passionate dalam mengajar dan berbagi ilmu teknologi terdepan.',
                'long_description' => "Halo! Saya Sari Dewi, seorang Data Scientist dengan pengalaman lebih dari 8 tahun di industri teknologi. Saya memiliki passion yang besar dalam dunia data science, machine learning, dan artificial intelligence.

Latar Belakang Profesional:
• Senior Data Scientist di perusahaan teknologi multinasional
• Berpengalaman menangani proyek-proyek besar dengan dataset jutaan records
• Ahli dalam Python, R, SQL, TensorFlow, dan PyTorch
• Memiliki sertifikasi Google Cloud Professional Data Engineer dan AWS Machine Learning Specialty

Pengalaman Mengajar:
Saya telah mengajar lebih dari 2000 siswa dalam berbagai platform online dan offline. Metodologi pengajaran saya fokus pada:
• Learning by doing - praktik langsung dengan project real-world
• Penjelasan konsep kompleks dengan bahasa yang mudah dipahami
• Mentoring personal untuk memastikan setiap siswa dapat mengikuti materi
• Update materi sesuai dengan perkembangan industri terkini

Keahlian Khusus:
- Machine Learning (Supervised & Unsupervised Learning)
- Deep Learning & Neural Networks
- Natural Language Processing (NLP)
- Computer Vision
- Big Data Analytics dengan Apache Spark
- Data Visualization dengan Tableau dan Power BI
- Statistical Analysis dan A/B Testing

Filosofi Mengajar:
Saya percaya bahwa setiap orang dapat mempelajari data science asalkan diberikan pendekatan yang tepat. Saya selalu memastikan materi yang saya ajarkan tidak hanya teoritis, tetapi juga applicable dalam dunia kerja nyata.

Mari belajar bersama dan wujudkan karir impian Anda di bidang data science!",
                'terms_agreed' => true,
                'privacy_agreed' => true,
                'status' => 'approved',
                'submitted_at' => now()->subDays(10),
                'reviewed_at' => now()->subDays(8),
                'reviewed_by' => $admin->id ?? null,
            ]);
        }

        // Create profile for Ahmad Rahman
        if ($ahmadRahman) {
            TutorProfile::create([
                'user_id' => $ahmadRahman->id,
                'full_name' => 'Ahmad Rahman Hakim',
                'public_name' => 'Ahmad Rahman',
                'public_name_slug' => 'ahmad-rahman',
                'identity_number' => '3301234567890124',
                'identity_type' => 'KTP',
                'phone_number' => '+6281234567891',
                'education_level' => 'S1',
                'description' => 'Full-Stack Developer dengan 6+ tahun pengalaman. Spesialis dalam React, Node.js, dan cloud technologies. Mentor berpengalaman dengan 1500+ siswa.',
                'long_description' => "Assalamualaikum! Saya Ahmad Rahman, seorang Full-Stack Developer yang passionate dalam dunia web development dan cloud computing. Dengan pengalaman 6+ tahun di industri teknologi, saya telah membantu ratusan perusahaan mengembangkan aplikasi web yang scalable dan modern.

Perjalanan Karir:
• Lead Developer di startup teknologi terkemuka
• Freelance developer dengan 100+ project sukses
• Technical consultant untuk berbagai perusahaan UMKM
• Kontributor aktif di komunitas developer Indonesia

Tech Stack Expertise:
Frontend:
- React.js, Next.js, Vue.js
- TypeScript, JavaScript ES6+
- HTML5, CSS3, Tailwind CSS
- State Management (Redux, Zustand)

Backend:
- Node.js, Express.js
- PHP (Laravel, CodeIgniter)
- Python (Django, FastAPI)
- Database: MySQL, PostgreSQL, MongoDB

Cloud & DevOps:
- AWS (EC2, S3, Lambda, RDS)
- Google Cloud Platform
- Docker & Kubernetes
- CI/CD dengan GitHub Actions

Metodologi Pengajaran:
Saya menggunakan pendekatan 'Project-Based Learning' dimana setiap siswa akan membangun aplikasi nyata dari awal hingga deployment. Metode ini terbukti efektif karena:
• Siswa langsung praktik dengan tools industri
• Membangun portfolio yang impressive
• Memahami full development lifecycle
• Siap kerja setelah menyelesaikan kursus

Pencapaian Mengajar:
- 1500+ siswa telah menyelesaikan kursus saya
- 85% siswa mendapat pekerjaan dalam 6 bulan
- Rating 4.9/5 dari student feedback
- Mentor di berbagai bootcamp programming

Komitmen Saya:
Saya berkomitmen untuk selalu update dengan teknologi terbaru dan memastikan materi yang diajarkan sesuai dengan kebutuhan industri. Setiap siswa akan mendapat mentoring personal dan support hingga berhasil berkarir di bidang teknologi.

Let's code together and build amazing things! 🚀",
                'terms_agreed' => true,
                'privacy_agreed' => true,
                'status' => 'approved',
                'submitted_at' => now()->subDays(15),
                'reviewed_at' => now()->subDays(12),
                'reviewed_by' => $admin->id ?? null,
            ]);
        }

        // Create profile for Super Admin (as example approved tutor)
        if ($superAdmin) {
            TutorProfile::create([
                'user_id' => $superAdmin->id,
                'full_name' => 'Dr. Budi Santoso, M.Kom',
                'public_name' => 'Dr. Budi Santoso',
                'public_name_slug' => 'dr-budi-santoso',
                'identity_number' => '3101234567890125',
                'identity_type' => 'KTP',
                'phone_number' => '+6281234567892',
                'education_level' => 'S3',
                'description' => 'Dosen dan peneliti dengan 15+ tahun pengalaman. Ahli dalam Computer Science, AI, dan Software Engineering. Founder Ngambiskuy Platform.',
                'long_description' => "Selamat datang di profil saya! Saya Dr. Budi Santoso, founder dan lead educator di Ngambiskuy Platform. Dengan dedikasi lebih dari 15 tahun di dunia pendidikan teknologi, saya berkomitmen untuk mencerdaskan generasi digital Indonesia.

Latar Belakang Akademik:
• S3 Computer Science - Institut Teknologi Bandung (ITB)
• S2 Teknik Informatika - Universitas Indonesia (UI)
• S1 Teknik Komputer - Institut Teknologi Sepuluh Nopember (ITS)

Pengalaman Profesional:
• Dosen Senior di Fakultas Ilmu Komputer, Universitas Indonesia
• Principal Researcher di Indonesian Institute of Technology
• Technical Advisor untuk berbagai startup teknologi
• Konsultan IT untuk perusahaan Fortune 500

Bidang Keahlian:
Artificial Intelligence & Machine Learning:
- Deep Learning dan Neural Networks
- Computer Vision dan Image Processing
- Natural Language Processing
- Reinforcement Learning

Software Engineering:
- Software Architecture dan Design Patterns
- Microservices dan Distributed Systems
- DevOps dan Cloud Computing
- Agile Development Methodologies

Research & Innovation:
- Published 50+ papers di jurnal internasional
- Holder 5 paten teknologi
- Keynote speaker di konferensi teknologi internasional
- Reviewer untuk top-tier computer science journals

Visi Pendidikan:
Saya percaya bahwa pendidikan teknologi harus:
• Accessible untuk semua kalangan
• Practical dan industry-relevant
• Innovative dalam metodologi
• Sustainable untuk masa depan

Melalui Ngambiskuy, saya ingin menciptakan ekosistem pembelajaran yang memungkinkan setiap orang Indonesia untuk menguasai teknologi dan berkontribusi dalam era digital.

Pencapaian:
- Melatih 10,000+ mahasiswa dan profesional
- Mentor untuk 200+ startup teknologi
- Penerima penghargaan 'Outstanding Educator' 2020-2023
- Kontributor dalam pengembangan kurikulum nasional IT

Mari bergabung dalam perjalanan transformasi digital Indonesia! 🇮🇩",
                'terms_agreed' => true,
                'privacy_agreed' => true,
                'status' => 'approved',
                'submitted_at' => now()->subDays(30),
                'reviewed_at' => now()->subDays(28),
                'reviewed_by' => $admin->id ?? null,
            ]);
        }
    }
}
