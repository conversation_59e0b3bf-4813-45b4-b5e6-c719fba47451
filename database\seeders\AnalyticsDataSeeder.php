<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\CourseLesson;
use App\Models\LessonProgress;
use App\Models\LessonQuiz;
use App\Models\QuizAttempt;
use App\Models\User;
use App\Models\Role;
use App\Models\NalaChatConversation;
use App\Models\NalaChatMessage;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AnalyticsDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating analytics data for tutor dashboard...');

        // Get all students and enrollments
        $students = User::whereDoesntHave('roles', function($query) {
            $query->whereIn('name', [Role::TUTOR, Role::ADMIN, Role::SUPERADMIN]);
        })->get();

        $enrollments = CourseEnrollment::with(['user', 'course'])->get();

        if ($enrollments->count() > 0) {
            // Create lesson progress data
            $this->createLessonProgress($enrollments);
            
            // Create quiz attempts data
            $this->createQuizAttempts($enrollments);
            
            // Create Nala chat conversations for discussion metrics
            $this->createNalaChatConversations($students);
        }

        $this->command->info('Analytics data seeding completed!');
    }

    private function createLessonProgress($enrollments)
    {
        $this->command->info('Creating lesson progress data...');

        foreach ($enrollments as $enrollment) {
            $lessons = CourseLesson::where('course_id', $enrollment->course_id)
                ->where('is_published', true)
                ->get();

            foreach ($lessons as $lesson) {
                // Check if progress already exists
                $existingProgress = LessonProgress::where('user_id', $enrollment->user_id)
                    ->where('lesson_id', $lesson->id)
                    ->first();

                // Create progress for some lessons (not all to simulate realistic progress)
                if (!$existingProgress && rand(1, 100) <= 70) { // 70% chance of having progress
                    $startedAt = $enrollment->enrolled_at->copy()->addDays(rand(0, 10));
                    $timeSpent = rand(5, 60); // 5-60 minutes
                    $isCompleted = rand(1, 100) <= 60; // 60% completion rate
                    $completedAt = $isCompleted ? $startedAt->copy()->addMinutes($timeSpent) : null;

                    LessonProgress::create([
                        'user_id' => $enrollment->user_id,
                        'lesson_id' => $lesson->id,
                        'status' => $isCompleted ? 'completed' : 'in_progress',
                        'progress_percentage' => $isCompleted ? 100 : rand(10, 90),
                        'time_spent_seconds' => $timeSpent * 60, // Convert minutes to seconds
                        'started_at' => $startedAt,
                        'completed_at' => $completedAt,
                        'last_accessed_at' => $startedAt->copy()->addDays(rand(0, 5)),
                        'visit_count' => rand(1, 5),
                    ]);
                }
            }
        }
    }

    private function createQuizAttempts($enrollments)
    {
        $this->command->info('Creating quiz attempts data...');

        foreach ($enrollments as $enrollment) {
            $quizzes = LessonQuiz::whereHas('lesson', function($query) use ($enrollment) {
                $query->where('course_id', $enrollment->course_id);
            })->get();

            foreach ($quizzes as $quiz) {
                // Check if quiz attempts already exist
                $existingAttempts = QuizAttempt::where('user_id', $enrollment->user_id)
                    ->where('quiz_id', $quiz->id)
                    ->count();

                // Create quiz attempts for some quizzes
                if ($existingAttempts == 0 && rand(1, 100) <= 50) { // 50% chance of attempting quiz
                    $attempts = rand(1, 3); // 1-3 attempts per quiz

                    for ($i = 0; $i < $attempts; $i++) {
                        $scorePercentage = rand(30, 100); // Random score between 30-100
                        $isPassed = $scorePercentage >= 70;
                        $attemptedAt = $enrollment->enrolled_at->copy()->addDays(rand(1, 20));
                        $timeTaken = rand(5, 30) * 60; // Convert minutes to seconds
                        $totalQuestions = rand(5, 15);
                        $correctAnswers = round(($scorePercentage / 100) * $totalQuestions);

                        QuizAttempt::create([
                            'user_id' => $enrollment->user_id,
                            'quiz_id' => $quiz->id,
                            'attempt_number' => $i + 1,
                            'started_at' => $attemptedAt,
                            'submitted_at' => $attemptedAt->copy()->addSeconds($timeTaken),
                            'completed_at' => $attemptedAt->copy()->addSeconds($timeTaken),
                            'time_taken' => $timeTaken,
                            'total_questions' => $totalQuestions,
                            'correct_answers' => $correctAnswers,
                            'score_percentage' => $scorePercentage,
                            'total_points' => $correctAnswers * 10,
                            'max_points' => $totalQuestions * 10,
                            'status' => 'completed',
                            'is_passed' => $isPassed,
                        ]);

                        // If passed, don't create more attempts
                        if ($isPassed) break;
                    }
                }
            }
        }
    }

    private function createNalaChatConversations($students)
    {
        $this->command->info('Creating Nala chat conversations for discussion metrics...');

        // Create conversations for some students
        $studentsToChat = $students->random(min(10, $students->count()));

        foreach ($studentsToChat as $student) {
            // Create 1-3 conversations per student
            $conversationCount = rand(1, 3);
            
            for ($i = 0; $i < $conversationCount; $i++) {
                $createdAt = now()->subDays(rand(1, 30));
                $lastMessageAt = $createdAt->copy()->addMinutes(rand(5, 120));

                $conversation = NalaChatConversation::create([
                    'user_id' => $student->id,
                    'session_id' => 'session_' . $student->id . '_' . time() . '_' . $i,
                    'status' => collect(['active', 'archived'])->random(),
                    'started_route' => collect(['tutor.dashboard', 'course.show', 'home'])->random(),
                    'started_context' => 'general_inquiry',
                    'context_data' => json_encode(['type' => 'course_question']),
                    'message_count' => rand(2, 10),
                    'last_message_at' => $lastMessageAt,
                    'created_at' => $createdAt,
                    'updated_at' => $lastMessageAt,
                ]);

                // Create some messages for the conversation
                $messageCount = $conversation->message_count;
                for ($j = 0; $j < $messageCount; $j++) {
                    $messageTime = $createdAt->copy()->addMinutes($j * rand(1, 10));
                    $sender = $j % 2 === 0 ? 'user' : 'nala';

                    NalaChatMessage::create([
                        'conversation_id' => $conversation->id,
                        'sender' => $sender === 'user' ? 'user' : 'ai',
                        'content' => $sender === 'user' ? 'Pertanyaan tentang kursus' : 'Jawaban dari Nala AI',
                        'is_prohibited' => false,
                        'membership_level' => 'free',
                        'status' => 'delivered',
                        'created_at' => $messageTime,
                        'updated_at' => $messageTime,
                    ]);
                }
            }
        }
    }
}
