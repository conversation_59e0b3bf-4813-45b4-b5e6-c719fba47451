<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;

class Course extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tutor_id',
        'category_id',
        'title',
        'slug',
        'description',
        'long_description',
        'level',
        'duration',
        'price',
        'is_free',
        'language',
        'thumbnail',
        'preview_video',
        'status',
        'is_featured',
        'published_at',
        'learning_outcomes',
        'requirements',
        'target_audience',
        'meta_title',
        'meta_description',
        'tags',
        'total_students',
        'average_rating',
        'total_reviews',
        'total_lessons',
        'total_duration_minutes',
        'platform_fee_percentage',
        'tutor_revenue_percentage',
        'tutor_revenue_with_referral_percentage',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'is_free' => 'boolean',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
        'learning_outcomes' => 'array',
        'requirements' => 'array',
        'target_audience' => 'array',
        'tags' => 'array',
        'total_students' => 'integer',
        'average_rating' => 'decimal:2',
        'total_reviews' => 'integer',
        'total_lessons' => 'integer',
        'total_duration_minutes' => 'integer',
        'platform_fee_percentage' => 'decimal:2',
        'tutor_revenue_percentage' => 'decimal:2',
        'tutor_revenue_with_referral_percentage' => 'decimal:2',
    ];

    /**
     * Get the tutor that owns the course.
     */
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    /**
     * Get the category that owns the course.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the chapters for the course.
     */
    public function chapters(): HasMany
    {
        return $this->hasMany(CourseChapter::class)->orderBy('sort_order');
    }

    /**
     * Get published chapters for the course.
     */
    public function publishedChapters(): HasMany
    {
        return $this->chapters()->where('is_published', true);
    }

    /**
     * Get the lessons for the course.
     */
    public function lessons(): HasMany
    {
        return $this->hasMany(CourseLesson::class)->orderBy('sort_order');
    }

    /**
     * Get published lessons for the course.
     */
    public function publishedLessons(): HasMany
    {
        return $this->lessons()->where('is_published', true);
    }

    /**
     * Get the enrollments for the course.
     */
    public function enrollments(): HasMany
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get the enrolled students for the course.
     */
    public function enrolledStudents(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'course_enrollments')
                    ->withPivot(['status', 'amount_paid', 'enrolled_at', 'completed_at'])
                    ->withTimestamps();
    }

    /**
     * Get the payments for this course.
     */
    public function payments(): MorphMany
    {
        return $this->morphMany(Payment::class, 'payable');
    }

    /**
     * Get published courses.
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')->whereNotNull('published_at');
    }

    /**
     * Get featured courses.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Retrieve the model for a bound value.
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // If the value looks like a UUID, search by ID
        if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $value)) {
            return $this->where('id', $value)->first();
        }

        // Otherwise, search by slug (default behavior)
        return $this->where('slug', $value)->first();
    }

    /**
     * Retrieve the child model for a bound value.
     */
    public function resolveChildRouteBinding($childType, $value, $field)
    {
        if ($childType === 'chapter') {
            return $this->chapters()->where('slug', $value)->first();
        }

        return null;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($course) {
            if (empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });

        static::updating(function ($course) {
            if ($course->isDirty('title') && empty($course->slug)) {
                $course->slug = Str::slug($course->title);
            }
        });
    }

    /**
     * Get formatted price in IDR.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free) {
            return 'Gratis';
        }

        return 'IDR ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get level in Indonesian.
     */
    public function getLevelIndonesianAttribute(): string
    {
        return match($this->level) {
            'beginner' => 'Pemula',
            'intermediate' => 'Menengah',
            'advanced' => 'Lanjutan',
            default => $this->level,
        };
    }

    /**
     * Calculate platform fee for this course.
     */
    public function calculatePlatformFee(): float
    {
        return $this->price * ($this->platform_fee_percentage / 100);
    }

    /**
     * Calculate tutor earnings for this course.
     */
    public function calculateTutorEarnings(bool $hasReferral = false): float
    {
        $percentage = $hasReferral
            ? $this->tutor_revenue_with_referral_percentage
            : $this->tutor_revenue_percentage;

        $afterPlatformFee = $this->price - $this->calculatePlatformFee();
        return $afterPlatformFee * ($percentage / 100);
    }

    /**
     * Check if price meets minimum requirement.
     */
    public function meetsMinimumPrice(): bool
    {
        return $this->is_free || $this->price >= 30000;
    }

    /**
     * Get minimum price for paid courses.
     */
    public static function getMinimumPrice(): int
    {
        return 30000;
    }

    /**
     * Scope for paid courses only.
     */
    public function scopePaid($query)
    {
        return $query->where('is_free', false)->where('price', '>', 0);
    }

    /**
     * Scope for free courses only.
     */
    public function scopeFree($query)
    {
        return $query->where('is_free', true);
    }

    /**
     * Check if course is published.
     */
    public function isPublished(): bool
    {
        return $this->status === 'published' && $this->published_at <= now();
    }

    /**
     * Update course statistics based on published chapters and lessons.
     */
    public function updateStatistics(): void
    {
        $publishedChapters = $this->chapters()->where('is_published', true)->with(['lessons' => function ($query) {
            $query->where('is_published', true);
        }])->get();

        $totalLessons = $publishedChapters->sum(function ($chapter) {
            return $chapter->lessons->count();
        });

        $totalDurationMinutes = $publishedChapters->sum(function ($chapter) {
            return $chapter->lessons->sum('duration_minutes');
        });

        $this->update([
            'total_lessons' => $totalLessons,
            'total_duration_minutes' => $totalDurationMinutes,
        ]);
    }
}
