<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => Role::USER,
                'description' => 'Regular user with basic access to courses and exams'
            ],
            [
                'name' => Role::TUTOR,
                'description' => 'Instructor who can create and manage courses and exams'
            ],
            [
                'name' => Role::ADMIN,
                'description' => 'Administrator with elevated privileges to manage platform'
            ],
            [
                'name' => Role::SUPERADMIN,
                'description' => 'Super administrator with full system access and control'
            ],
        ];

        foreach ($roles as $roleData) {
            Role::firstOrCreate(
                ['name' => $roleData['name']],
                ['description' => $roleData['description']]
            );
        }
    }
}
