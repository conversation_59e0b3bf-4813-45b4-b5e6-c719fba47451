<?php

namespace App\Actions\Fortify;

use App\Models\User;
use App\Models\MembershipPlan;
use App\Models\UserMembership;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Validate and create a newly registered user.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {
        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique(User::class),
            ],
            'password' => $this->passwordRules(),
        ])->validate();

        $user = User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
            'terms_agreed' => true,
            'privacy_agreed' => true,
            'agreement_date' => now(),
        ]);

        // Automatically assign free membership to new users
        $this->assignFreeMembership($user);

        return $user;
    }

    /**
     * Assign free membership to a user.
     */
    private function assignFreeMembership(User $user): void
    {
        $freePlan = MembershipPlan::where('slug', 'free')->first();
        if ($freePlan) {
            UserMembership::create([
                'user_id' => $user->id,
                'membership_plan_id' => $freePlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'expires_at' => now()->addMonths($freePlan->duration_months),
                'nala_prompts_allocated' => $freePlan->nala_prompts ?? 0,
                'nala_prompts_remaining' => $freePlan->nala_prompts ?? 0,
                'has_unlimited_nala' => $freePlan->has_unlimited_nala,
                'has_ice_full' => $freePlan->has_ice_full,
                'has_ai_teaching_assistants_courses' => $freePlan->has_ai_teaching_assistants_courses,
                'has_ai_teaching_assistants_tryout' => $freePlan->has_ai_teaching_assistants_tryout,
                'has_free_certifications' => $freePlan->has_free_certifications,
                'has_blog_access' => $freePlan->has_blog_access,
                'career_path_predictor' => $freePlan->career_path_predictor,
                'has_priority_support' => $freePlan->has_priority_support,
            ]);
        }
    }
}
