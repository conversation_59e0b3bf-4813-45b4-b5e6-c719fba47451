<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Role extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * Get the users that have this role.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_roles');
    }

    /**
     * Role constants for easy reference
     */
    public const USER = 'user';
    public const TUTOR = 'tutor';
    public const ADMIN = 'admin';
    public const SUPERADMIN = 'superadmin';

    /**
     * Get all available roles
     */
    public static function getAllRoles(): array
    {
        return [
            self::USER,
            self::TUTOR,
            self::ADMIN,
            self::SUPERADMIN,
        ];
    }

    /**
     * Check if role name is valid
     */
    public static function isValidRole(string $roleName): bool
    {
        return in_array($roleName, self::getAllRoles());
    }
}
