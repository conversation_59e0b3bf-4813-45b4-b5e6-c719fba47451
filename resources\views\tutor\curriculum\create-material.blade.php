@extends('layouts.tutor')

@section('title', '<PERSON>bah Materi - ' . $chapter->title)

@push('styles')
<link rel="stylesheet" href="{{ asset('css/validation-styles.css') }}">
@endpush

@section('content')
<div class="tutor-dashboard-container tutor-material-create-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mb-2">
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-0">Tambah Materi Baru</h1>
                    <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 md:py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 w-fit">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        Material Hub
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">{{ $course->title }} - {{ $chapter->title }}</p>
                <div class="tutor-quick-stats-mobile flex flex-col md:flex-row md:items-center mt-3 md:mt-2 space-y-2 md:space-y-0 md:space-x-4 text-sm text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ $course->category->name }}
                    </span>
                    <span class="flex items-center">
                        @if($course->is_free)
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GRATIS</span>
                        @else
                            <span class="bg-emerald-600 text-white text-xs px-2 py-1 rounded">{{ $course->formatted_price }}</span>
                        @endif
                    </span>
                </div>
            </div>
            <div class="tutor-header-actions flex items-center">
                <a href="{{ route('tutor.curriculum.index', $course) }}" class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly w-full md:w-auto">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kurikulum
                </a>
            </div>
        </div>
    </div>

    <!-- Nala AI Assistant Section -->
    <div class="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-600 rounded-lg shadow-lg mb-6 overflow-hidden">
        <div class="px-4 md:px-6 py-4 md:py-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <img src="{{ asset('images/nala.png') }}" alt="Nala AI" class="w-8 h-8 md:w-10 md:h-10 rounded-full mr-3 border-2 border-white/20">
                        <div>
                            <h3 class="text-lg md:text-xl font-bold text-white">Nala AI Material Builder</h3>
                            <p class="text-purple-100 text-sm">Buat materi pembelajaran dengan bantuan AI</p>
                        </div>
                    </div>
                    <p class="text-purple-100 text-sm md:text-base">
                        Nala akan menganalisis bab "<strong>{{ $chapter->title }}</strong>" dan membuat saran materi yang relevan untuk Anda.
                    </p>
                </div>

                <div class="flex-shrink-0">
                    @if(auth()->user()->hasActiveMembership())
                        <!-- Nala Button for Members -->
                        <button id="ai-material-builder-btn"
                                title="Nala akan membuat saran materi berdasarkan bab: {{ $chapter->title }}"
                                class="bg-white text-purple-600 px-4 md:px-6 py-3 rounded-xl font-semibold hover:bg-purple-50 hover:scale-105 transition-all duration-200 shadow-lg w-full md:w-auto">
                            <div class="flex items-center justify-center space-x-2">
                                <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <span id="ai-material-text">Gunakan Nala</span>
                            </div>
                        </button>
                        <p class="text-purple-100 text-xs mt-2 text-center">⚡ Hemat 80% waktu pembuatan</p>
                    @else
                        <!-- Upgrade CTA for Free Users -->
                        <div class="text-center">
                            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-4 mb-3 border border-white/20">
                                <div class="flex items-center justify-center mb-2">
                                    <svg class="w-5 h-5 text-yellow-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-white font-medium text-sm">Fitur Premium</span>
                                </div>
                                <p class="text-purple-100 text-xs">Nala AI memerlukan membership aktif</p>
                            </div>
                            <a href="{{ route('payment.pricing') }}"
                               class="bg-yellow-400 text-purple-900 px-4 md:px-6 py-3 rounded-xl font-bold hover:bg-yellow-300 transition-all duration-200 shadow-lg w-full md:w-auto inline-block">
                                <div class="flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                    <span>Upgrade Membership</span>
                                </div>
                            </a>
                            <p class="text-purple-100 text-xs mt-2">Mulai dari Rp 49.000/bulan</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Material Creation Form -->
    <div class="bg-white rounded-lg shadow-sm">
        <!-- Server-side validation errors -->
        @if ($errors->any())
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <div class="flex items-center mb-2">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h4 class="font-medium">Terdapat kesalahan pada form:</h4>
                </div>
                <ul class="list-disc list-inside space-y-1">
                    @foreach ($errors->all() as $error)
                        <li class="text-sm">{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Hidden context data for Nala AI -->
        <div id="nala-context" style="display: none;"
             data-course-title="{{ $course->title }}"
             data-course-description="{{ $course->description }}"
             data-course-level="{{ $course->level }}"
             data-course-category="{{ $course->category->name }}"
             data-chapter-title="{{ $chapter->title }}"
             data-chapter-description="{{ $chapter->description ?? '' }}">
        </div>

        <form action="{{ route('tutor.curriculum.store-lesson', [$course, $chapter]) }}" method="POST" enctype="multipart/form-data" id="materialForm" novalidate>
            @csrf

            <div class="tutor-form-mobile p-4 md:p-6 space-y-4 md:space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Materi <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" id="title" name="title" required value="{{ old('title') }}"
                                   class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                                   placeholder="Contoh: 1.1 Pengenalan Konsep Dasar"
                                   data-validation="required|min:5|max:255">
                            <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="validation-message mt-1 text-sm hidden"></div>
                        @error('title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                            Estimasi Durasi (menit) <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="number" id="duration_minutes" name="duration_minutes" required min="1" max="300" value="{{ old('duration_minutes') }}"
                                   class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                                   placeholder="Contoh: 15"
                                   data-validation="required|numeric|min:1|max:300">
                            <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="validation-message mt-1 text-sm hidden"></div>
                        <p class="text-xs text-gray-500 mt-1">Perkiraan waktu yang dibutuhkan untuk menyelesaikan materi ini</p>
                        @error('duration_minutes')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                    <div class="relative">
                        <textarea id="description" name="description" rows="3"
                                  class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none"
                                  placeholder="Deskripsi singkat tentang materi ini..."
                                  data-validation="max:500">{{ old('description') }}</textarea>
                        <div class="validation-icon absolute right-3 top-3 hidden">
                            <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex flex-col md:flex-row md:justify-between md:items-center mt-1 gap-1 md:gap-0">
                        <div class="validation-message text-sm hidden order-2 md:order-1"></div>
                        <div class="text-xs text-gray-500 order-1 md:order-2">
                            <span id="description-count">0</span>/500 karakter
                        </div>
                    </div>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Material Type Selection -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                        Tipe Materi <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <select id="type" name="type" required onchange="toggleMaterialFields()"
                                class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                                data-validation="required">
                            <option value="">Pilih tipe materi</option>
                            <option value="video" {{ old('type') == 'video' ? 'selected' : '' }}>Video</option>
                            <option value="text" {{ old('type') == 'text' ? 'selected' : '' }}>Teks/Artikel</option>
                            <option value="quiz" {{ old('type') == 'quiz' ? 'selected' : '' }}>Kuis</option>
                            <option value="assignment" {{ old('type') == 'assignment' ? 'selected' : '' }}>Tugas</option>
                        </select>
                        <div class="validation-icon absolute right-8 md:right-10 top-1/2 transform -translate-y-1/2 hidden">
                            <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="validation-message mt-1 text-sm hidden"></div>
                    @error('type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Dynamic Content Areas -->
            <div class="border-t border-gray-200">
                <!-- Video Content -->
                <div id="video_content" class="hidden p-4 md:p-6 space-y-4 md:space-y-6">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Konten Video</h3>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Sumber Video</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                            <label class="flex items-center p-3 md:p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors tutor-touch-friendly">
                                <input type="radio" name="video_source" value="url" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="toggleVideoSource()">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">URL Video</span>
                                    <p class="text-xs text-gray-500">YouTube, Vimeo, dll</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 md:p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors tutor-touch-friendly">
                                <input type="radio" name="video_source" value="upload" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300" onchange="toggleVideoSource()">
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Upload Video</span>
                                    <p class="text-xs text-gray-500">File MP4, MOV, AVI</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- URL Input -->
                    <div id="video_url_input" class="hidden">
                        <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">URL Video</label>
                        <div class="relative">
                            <input type="url" id="video_url" name="video_url" value="{{ old('video_url') }}"
                                   class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                                   placeholder="https://www.youtube.com/watch?v=..."
                                   data-validation="url">
                            <div class="validation-icon absolute right-3 top-1/2 transform -translate-y-1/2 hidden">
                                <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="validation-message mt-1 text-sm hidden"></div>
                        <p class="text-xs text-gray-500 mt-1">Mendukung YouTube, Vimeo, dan platform video lainnya</p>
                    </div>

                    <!-- File Upload -->
                    <div id="video_file_input" class="hidden">
                        <label for="video_file" class="block text-sm font-medium text-gray-700 mb-2">Upload Video</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 md:p-8 text-center hover:border-emerald-500 transition-colors">
                            <input type="file" id="video_file" name="video_file" accept="video/*" class="hidden" onchange="handleVideoFileSelect(this)">
                            <input type="hidden" id="uploaded_video_path" name="uploaded_video_path" value="{{ old('uploaded_video_path') }}">

                            <!-- Upload Area -->
                            <div id="video_upload_area" onclick="document.getElementById('video_file').click()" class="cursor-pointer">
                                <svg class="w-8 h-8 md:w-12 md:h-12 text-gray-400 mx-auto mb-3 md:mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-xs md:text-sm text-gray-600 mb-2">Klik untuk upload video atau drag & drop</p>
                                <p class="text-xs text-gray-500">MP4, MOV, AVI (Max: 100MB)</p>
                            </div>

                            <!-- Upload Progress -->
                            <div id="video_upload_progress" class="hidden">
                                <div class="flex items-center justify-center space-x-3 mb-4">
                                    <svg class="w-5 h-5 md:w-6 md:h-6 text-blue-500 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    <span class="text-xs md:text-sm font-medium text-gray-700">Mengupload video...</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2 md:h-3 mb-2">
                                    <div id="upload_progress_bar" class="bg-blue-500 h-2 md:h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-500">
                                    <span id="upload_progress_text">0%</span>
                                    <span id="upload_speed_text">0 KB/s</span>
                                </div>
                                <button type="button" onclick="cancelVideoUpload()" class="text-xs text-red-600 hover:text-red-800 mt-2 tutor-touch-friendly">Batalkan Upload</button>
                            </div>

                            <!-- Upload Success -->
                            <div id="video_file_info" class="hidden">
                                <div class="flex items-center justify-center space-x-2 mb-2">
                                    <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span id="video_file_name" class="text-xs md:text-sm text-gray-700"></span>
                                </div>
                                <div class="text-xs text-gray-500 mb-2">
                                    <span id="video_file_size"></span> • Upload berhasil
                                </div>
                                <button type="button" onclick="clearVideoFile()" class="text-xs text-red-600 hover:text-red-800 tutor-touch-friendly">Hapus file</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text/Article Content -->
                <div id="text_content" class="hidden p-4 md:p-6">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Konten Artikel</h3>

                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Konten</label>
                        <textarea id="content" name="content" rows="15"
                                  class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                                  placeholder="Tulis konten artikel di sini...">{{ old('content') }}</textarea>
                        @error('content')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Quiz Content -->
                <div id="quiz_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Kuis</h3>
                    <div id="quiz_builder">
                        <!-- Quiz builder will be loaded here via JavaScript -->
                        <div class="text-center py-8">
                            <p class="text-gray-500">Kuis builder akan dimuat di sini...</p>
                        </div>
                    </div>
                </div>

                <!-- Assignment Content -->
                <div id="assignment_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Tugas</h3>
                    <div id="assignment_builder">
                        <!-- Assignment builder will be loaded here via JavaScript -->
                        <div class="text-center py-8">
                            <p class="text-gray-500">Assignment builder akan dimuat di sini...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="border-t border-gray-200 p-4 md:p-6">
                <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Pengaturan</h3>

                @if(!$course->is_free)
                    <div class="bg-teal-50 p-3 md:p-4 rounded-lg mb-4">
                        <label class="flex items-start tutor-touch-friendly">
                            <input type="checkbox" name="is_preview" value="1" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1" {{ old('is_preview') ? 'checked' : '' }}>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-700">Preview Materi</span>
                                <p class="text-xs text-gray-500 mt-1">Dapat dilihat sebelum membeli kursus (cocok untuk menarik minat calon siswa)</p>
                            </div>
                        </label>
                    </div>
                @else
                    <div class="bg-green-50 p-3 md:p-4 rounded-lg mb-4">
                        <div class="flex items-start">
                            <svg class="w-4 h-4 md:w-5 md:h-5 text-green-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-700">Kursus Gratis</span>
                                <p class="text-xs text-gray-500 mt-1">Semua materi dalam kursus ini dapat diakses secara gratis</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Form Actions -->
            <!-- Mobile: Vertical Button Layout (primary action first) -->
            <div class="border-t border-gray-200 px-4 md:px-6 py-3 md:py-4 bg-gray-50 rounded-b-lg">
                <div class="flex flex-col md:flex-row md:justify-end gap-3 md:gap-3 md:space-x-0">
                    <button type="submit" id="submit_btn"
                            class="px-6 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors duration-200 flex items-center justify-center tutor-touch-friendly order-1">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span id="submit_text">Simpan Materi</span>
                    </button>
                    <a href="{{ route('tutor.curriculum.index', $course) }}"
                       class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200 tutor-touch-friendly order-2">
                        Batal
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- CSV Import Modal -->
<div id="csvImportModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="flex items-center justify-center min-h-screen px-4 py-8">
        <div class="relative w-full max-w-2xl bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white">Import Soal Kuis dari CSV</h3>
                    <button type="button" onclick="closeImportModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <!-- Instructions -->
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <h4 class="font-medium text-blue-900 mb-2">Cara Import Soal Kuis:</h4>
                    <ol class="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                        <li>Download template CSV (sudah berisi panduan dan contoh)</li>
                        <li>Buka dengan Excel/Google Sheets, hapus contoh, isi soal Anda</li>
                        <li>Simpan sebagai CSV dan upload di sini</li>
                        <li>Preview soal, lalu klik Import untuk menambahkan ke kuis</li>
                    </ol>
                    <div class="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-700">
                        <strong>Tips:</strong> Template sudah berisi panduan lengkap dan contoh untuk setiap tipe soal (Pilihan Ganda, Benar/Salah, Jawaban Singkat)
                    </div>
                </div>

                <!-- Download Template -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">1. Download Template CSV</label>
                    <a href="{{ route('tutor.curriculum.download-quiz-template') }}"
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Template
                    </a>
                    <p class="text-xs text-gray-500 mt-1">Template berisi panduan lengkap, format yang benar, dan contoh soal untuk setiap tipe</p>
                </div>

                <!-- Upload CSV -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">2. Upload File CSV</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                        <input type="file" id="csvFile" accept=".csv" class="hidden" onchange="handleCSVFile(this)">
                        <div id="csvUploadArea" onclick="document.getElementById('csvFile').click()" class="cursor-pointer">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium text-blue-600 hover:text-blue-500">Klik untuk upload</span>
                                    atau drag & drop file CSV
                                </p>
                                <p class="text-xs text-gray-500">CSV hingga 2MB</p>
                            </div>
                        </div>
                        <div id="csvFileInfo" class="hidden mt-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900" id="csvFileName"></p>
                                        <p class="text-xs text-gray-500" id="csvFileSize"></p>
                                    </div>
                                </div>
                                <button type="button" onclick="clearCSVFile()" class="text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div id="csvPreview" class="hidden mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">3. Preview Soal</label>
                    <div class="border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                        <div id="csvPreviewContent"></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <!-- Mobile: Vertical Button Layout (primary action first) -->
            <div class="bg-gray-50 px-4 md:px-6 py-3 md:py-4 flex flex-col md:flex-row md:justify-end gap-3 md:gap-3 md:space-x-0">
                <button type="button" onclick="importQuestions()" id="importBtn" disabled
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors tutor-touch-friendly order-1">
                    Import Soal
                </button>
                <button type="button" onclick="closeImportModal()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors tutor-touch-friendly order-2">
                    Batal
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border-radius: 0.5rem !important;
        border-color: #d1d5db !important;
    }
    .tox-editor-header {
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }
    .tox-edit-area {
        border-radius: 0 0 0.5rem 0.5rem !important;
    }
</style>
@endpush

@push('scripts')
<!-- TinyMCE -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<script>
// Initialize TinyMCE
function initializeTinyMCE() {
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }
    });
}

// Toggle material type fields
function toggleMaterialFields() {
    const type = document.getElementById('type').value;

    // First, disable required validation for all hidden content areas
    const allContentAreas = ['video_content', 'text_content', 'quiz_content', 'assignment_content'];

    allContentAreas.forEach(areaId => {
        const area = document.getElementById(areaId);
        area.classList.add('hidden');

        // Remove required attributes from fields in hidden areas
        const requiredFields = area.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.removeAttribute('required');
            field.setAttribute('data-was-required', 'true');
        });
    });

    // Show relevant content area and restore required attributes
    if (type === 'video') {
        const videoContent = document.getElementById('video_content');
        videoContent.classList.remove('hidden');
        restoreRequiredAttributes(videoContent);
    } else if (type === 'text') {
        const textContent = document.getElementById('text_content');
        textContent.classList.remove('hidden');
        restoreRequiredAttributes(textContent);
        // Initialize TinyMCE when text area is shown
        setTimeout(initializeTinyMCE, 100);
    } else if (type === 'quiz') {
        const quizContent = document.getElementById('quiz_content');
        quizContent.classList.remove('hidden');
        restoreRequiredAttributes(quizContent);

        // Show loading state
        const quizBuilder = document.getElementById('quiz_builder');
        quizBuilder.innerHTML = `
            <div class="text-center py-8">
                <div class="inline-flex items-center">
                    <svg class="w-5 h-5 mr-2 animate-spin text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span class="text-gray-600">Memuat kuis builder...</span>
                </div>
            </div>
        `;

        // Load quiz builder with a small delay to show loading state
        setTimeout(() => loadQuizBuilder(), 100);
    } else if (type === 'assignment') {
        const assignmentContent = document.getElementById('assignment_content');
        assignmentContent.classList.remove('hidden');
        restoreRequiredAttributes(assignmentContent);

        // Show loading state
        const assignmentBuilder = document.getElementById('assignment_builder');
        assignmentBuilder.innerHTML = `
            <div class="text-center py-8">
                <div class="inline-flex items-center">
                    <svg class="w-5 h-5 mr-2 animate-spin text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    <span class="text-gray-600">Memuat assignment builder...</span>
                </div>
            </div>
        `;

        // Load assignment builder with a small delay to show loading state
        setTimeout(() => loadAssignmentBuilder(), 100);
    }
}

// Helper function to restore required attributes for visible fields
function restoreRequiredAttributes(container) {
    const fieldsToRestore = container.querySelectorAll('[data-was-required="true"]');
    fieldsToRestore.forEach(field => {
        field.setAttribute('required', 'required');
    });
}

// Toggle video source
function toggleVideoSource() {
    const urlRadio = document.querySelector('input[name="video_source"][value="url"]');
    const uploadRadio = document.querySelector('input[name="video_source"][value="upload"]');
    const urlInput = document.getElementById('video_url_input');
    const fileInput = document.getElementById('video_file_input');

    if (urlRadio.checked) {
        urlInput.classList.remove('hidden');
        fileInput.classList.add('hidden');
    } else if (uploadRadio.checked) {
        urlInput.classList.add('hidden');
        fileInput.classList.remove('hidden');
    }
}

// Load quiz builder
function loadQuizBuilder() {
    // Reset question counter when loading quiz builder
    questionCounter = 0;

    document.getElementById('quiz_builder').innerHTML = `
        <div class="space-y-6">
            <!-- Quiz Settings -->
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-4">Pengaturan Kuis</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Batas Waktu (menit)</label>
                        <input type="number" name="quiz_time_limit" min="1" max="180"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="30">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal Percobaan</label>
                        <input type="number" name="quiz_max_attempts" min="1" max="10" value="3"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nilai Lulus (%)</label>
                        <input type="number" name="quiz_passing_score" min="0" max="100" value="70"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" name="quiz_shuffle_questions" id="quiz_shuffle"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="quiz_shuffle" class="ml-2 text-sm text-gray-700">Acak urutan soal</label>
                    </div>
                </div>
            </div>

            <!-- Quiz Instructions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Kuis</label>
                <textarea name="quiz_instructions" rows="3"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan instruksi untuk kuis ini..."></textarea>
            </div>

            <!-- Questions Section -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900">Soal Kuis</h4>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="showImportModal()"
                                class="btn btn-sm btn-outline">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            Import CSV
                        </button>
                        <button type="button" onclick="addQuizQuestion()"
                                class="btn btn-sm btn-primary">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Tambah Soal
                        </button>
                    </div>
                </div>
                <div id="quiz_questions_container" class="space-y-4">
                    <!-- Questions will be added here -->
                </div>
            </div>
        </div>
    `;

    // Add first question by default
    addQuizQuestion();
}

// Load assignment builder
function loadAssignmentBuilder() {
    document.getElementById('assignment_builder').innerHTML = `
        <div class="space-y-6">
            <!-- Assignment Settings -->
            <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-4">Pengaturan Tugas</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Deadline <span class="text-red-500">*</span></label>
                        <input type="datetime-local" name="assignment_deadline" required data-was-required="true"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal Poin</label>
                        <input type="number" name="assignment_max_points" min="1" max="1000" value="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal File</label>
                        <input type="number" name="assignment_max_files" min="1" max="10" value="3"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ukuran File Max (MB)</label>
                        <input type="number" name="assignment_max_file_size" min="1" max="100" value="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <div class="mt-4 space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" name="assignment_allow_late" id="assignment_allow_late"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="assignment_allow_late" class="ml-2 text-sm text-gray-700">Izinkan pengumpulan terlambat</label>
                    </div>
                    <div id="late_penalty_section" class="hidden ml-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Penalti per hari (%)</label>
                        <input type="number" name="assignment_late_penalty" min="0" max="100" value="10"
                               class="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Assignment Instructions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Tugas <span class="text-red-500">*</span></label>
                <textarea name="assignment_instructions" rows="4" required data-was-required="true"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Jelaskan tugas yang harus dikerjakan siswa..."></textarea>
            </div>

            <!-- Assignment Requirements -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Persyaratan Tugas</label>
                <textarea name="assignment_requirements" rows="3"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Sebutkan persyaratan khusus untuk tugas ini..."></textarea>
            </div>

            <!-- File Types -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipe File yang Diizinkan</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="pdf" checked
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">PDF</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="doc" checked
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">DOC</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="docx" checked
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">DOCX</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="txt"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">TXT</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="zip"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">ZIP</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="jpg"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">JPG</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="png"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">PNG</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="mp4"
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">MP4</span>
                    </label>
                </div>
            </div>
        </div>
    `;

    // Add event listener for late submission toggle
    document.getElementById('assignment_allow_late').addEventListener('change', function() {
        const latePenaltySection = document.getElementById('late_penalty_section');
        if (this.checked) {
            latePenaltySection.classList.remove('hidden');
        } else {
            latePenaltySection.classList.add('hidden');
        }
    });
}

// Quiz question management
let questionCounter = 0;

function addQuizQuestion() {
    questionCounter++;
    const container = document.getElementById('quiz_questions_container');
    const questionDiv = document.createElement('div');
    questionDiv.className = 'border border-gray-200 rounded-lg p-4';
    questionDiv.id = `question_${questionCounter}`;

    // Use zero-based index for form fields (server expects 0, 1, 2, etc.)
    const arrayIndex = questionCounter - 1;

    questionDiv.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h5 class="font-medium text-gray-900">Soal ${questionCounter}</h5>
            <button type="button" onclick="removeQuizQuestion(${questionCounter})"
                    class="text-red-600 hover:text-red-800">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan</label>
                <textarea name="quiz_questions[${arrayIndex}][question]" rows="3" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan pertanyaan..."></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                    <select name="quiz_questions[${arrayIndex}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="multiple_choice">Pilihan Ganda</option>
                        <option value="true_false">Benar/Salah</option>
                        <option value="short_answer">Jawaban Singkat</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                    <input type="number" name="quiz_questions[${arrayIndex}][points]" min="1" max="100" value="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
            </div>

            <!-- Options for multiple choice -->
            <div id="options_${questionCounter}" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Pilihan Jawaban</label>
                <div class="space-y-2">
                    ${generateQuestionOptions(questionCounter, arrayIndex)}
                </div>
                <button type="button" onclick="addQuestionOption(${questionCounter})"
                        class="text-sm text-primary hover:text-primary-dark">
                    + Tambah Pilihan
                </button>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                <textarea name="quiz_questions[${arrayIndex}][explanation]" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Penjelasan jawaban yang benar..."></textarea>
            </div>
        </div>
    `;

    container.appendChild(questionDiv);
}

function removeQuizQuestion(questionId) {
    const questionDiv = document.getElementById(`question_${questionId}`);
    if (questionDiv) {
        questionDiv.remove();

        // Renumber remaining questions
        renumberQuizQuestions();
    }
}

function renumberQuizQuestions() {
    const container = document.getElementById('quiz_questions_container');
    const questions = container.children;

    // Update question counter to match actual number of questions
    questionCounter = questions.length;

    // Renumber each question
    for (let i = 0; i < questions.length; i++) {
        const questionDiv = questions[i];
        const newNumber = i + 1;
        const oldId = questionDiv.id;
        const newId = `question_${newNumber}`;

        // Update question div ID
        questionDiv.id = newId;

        // Update question title
        const titleElement = questionDiv.querySelector('h5');
        if (titleElement) {
            titleElement.textContent = `Soal ${newNumber}`;
        }

        // Update remove button onclick
        const removeButton = questionDiv.querySelector('button[onclick*="removeQuizQuestion"]');
        if (removeButton) {
            removeButton.setAttribute('onclick', `removeQuizQuestion(${newNumber})`);
        }

        // Update form field names to maintain proper indexing
        updateQuestionFieldNames(questionDiv, newNumber);
    }
}

function updateQuestionFieldNames(questionDiv, questionNumber) {
    // Use zero-based index for array (server expects 0, 1, 2, etc.)
    const arrayIndex = questionNumber - 1;

    // Update all input and select field names within this question
    const fields = questionDiv.querySelectorAll('input, select, textarea');

    fields.forEach(field => {
        if (field.name && field.name.includes('quiz_questions[')) {
            // Replace the question index in the field name with zero-based index
            field.name = field.name.replace(/quiz_questions\[\d+\]/, `quiz_questions[${arrayIndex}]`);
        }
    });

    // Update option container ID
    const optionsContainer = questionDiv.querySelector('[id*="options_"]');
    if (optionsContainer) {
        optionsContainer.id = `options_${questionNumber}`;
    }

    // Update onchange handlers for type select
    const typeSelect = questionDiv.querySelector('select[onchange*="toggleQuestionOptions"]');
    if (typeSelect) {
        typeSelect.setAttribute('onchange', `toggleQuestionOptions(${questionNumber})`);
    }

    // Update add option button onclick
    const addOptionButton = questionDiv.querySelector('button[onclick*="addQuestionOption"]');
    if (addOptionButton) {
        addOptionButton.setAttribute('onclick', `addQuestionOption(${questionNumber})`);
    }
}

function generateQuestionOptions(questionId, arrayIndex = null) {
    // If arrayIndex is not provided, calculate it from questionId
    const index = arrayIndex !== null ? arrayIndex : questionId - 1;

    return `
        <div class="flex items-center space-x-2">
            <input type="radio" name="quiz_questions[${index}][correct_answer]" value="0"
                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
            <input type="text" name="quiz_questions[${index}][options][]"
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                   placeholder="Pilihan A" required>
        </div>
        <div class="flex items-center space-x-2">
            <input type="radio" name="quiz_questions[${index}][correct_answer]" value="1"
                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
            <input type="text" name="quiz_questions[${index}][options][]"
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                   placeholder="Pilihan B" required>
        </div>
    `;
}

function addQuestionOption(questionId) {
    const optionsContainer = document.querySelector(`#options_${questionId} .space-y-2`);
    const optionCount = optionsContainer.children.length;
    const optionDiv = document.createElement('div');
    optionDiv.className = 'flex items-center space-x-2';

    // Calculate array index (zero-based) from questionId
    const arrayIndex = questionId - 1;

    optionDiv.innerHTML = `
        <input type="radio" name="quiz_questions[${arrayIndex}][correct_answer]" value="${optionCount}"
               class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
        <input type="text" name="quiz_questions[${arrayIndex}][options][]"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
               placeholder="Pilihan ${String.fromCharCode(65 + optionCount)}" required>
        <button type="button" onclick="this.parentElement.remove()"
                class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    optionsContainer.appendChild(optionDiv);
}

function toggleQuestionOptions(questionId) {
    // Calculate array index (zero-based) from questionId
    const arrayIndex = questionId - 1;
    const typeSelect = document.querySelector(`select[name="quiz_questions[${arrayIndex}][type]"]`);
    const optionsDiv = document.getElementById(`options_${questionId}`);

    if (typeSelect.value === 'multiple_choice') {
        // Reset to multiple choice options
        optionsDiv.innerHTML = `
            <label class="block text-sm font-medium text-gray-700">Pilihan Jawaban</label>
            <div class="space-y-2">
                ${generateQuestionOptions(questionId, arrayIndex)}
            </div>
            <button type="button" onclick="addQuestionOption(${questionId})"
                    class="text-sm text-primary hover:text-primary-dark">
                + Tambah Pilihan
            </button>
        `;
        optionsDiv.style.display = 'block';
    } else if (typeSelect.value === 'true_false') {
        // Show true/false options
        optionsDiv.innerHTML = `
            <label class="block text-sm font-medium text-gray-700">Jawaban yang Benar</label>
            <div class="space-y-2">
                <div class="flex items-center space-x-2">
                    <input type="radio" name="quiz_questions[${arrayIndex}][correct_answer]" value="true"
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <label class="text-sm text-gray-700">Benar</label>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="radio" name="quiz_questions[${arrayIndex}][correct_answer]" value="false"
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <label class="text-sm text-gray-700">Salah</label>
                </div>
            </div>
        `;
        optionsDiv.style.display = 'block';
    } else {
        optionsDiv.style.display = 'none';
    }
}

// CSV Import functionality
let csvData = [];

// Helper function to format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showImportModal() {
    document.getElementById('csvImportModal').classList.remove('hidden');
    document.getElementById('csvImportModal').style.display = 'flex';
    document.getElementById('csvImportModal').style.alignItems = 'center';
    document.getElementById('csvImportModal').style.justifyContent = 'center';
}

function closeImportModal() {
    document.getElementById('csvImportModal').classList.add('hidden');
    document.getElementById('csvImportModal').style.display = 'none';
    clearCSVFile();
}



function handleCSVFile(input) {
    const file = input.files[0];
    if (file) {
        // Check file size (2MB limit)
        const maxSize = 2 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File terlalu besar! Maksimal 2MB.');
            input.value = '';
            return;
        }

        // Check file type
        if (!file.name.toLowerCase().endsWith('.csv')) {
            alert('Format file tidak didukung! Gunakan file CSV.');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('csvUploadArea').style.display = 'none';
        document.getElementById('csvFileInfo').classList.remove('hidden');
        document.getElementById('csvFileName').textContent = file.name;
        document.getElementById('csvFileSize').textContent = formatFileSize(file.size);

        // Read and parse CSV
        const reader = new FileReader();
        reader.onload = function(e) {
            const csv = e.target.result;
            parseCSV(csv);
        };
        reader.readAsText(file);
    }
}

function clearCSVFile() {
    document.getElementById('csvFile').value = '';
    document.getElementById('csvUploadArea').style.display = 'block';
    document.getElementById('csvFileInfo').classList.add('hidden');
    document.getElementById('csvPreview').classList.add('hidden');
    document.getElementById('importBtn').disabled = true;
    csvData = [];
}

function parseCSV(csv) {
    const lines = csv.split('\\n');
    const headers = lines[0].split(',').map(h => h.trim());

    // Validate headers
    const requiredHeaders = ['question', 'type', 'correct_answer', 'points'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

    if (missingHeaders.length > 0) {
        alert('Header CSV tidak lengkap! Missing: ' + missingHeaders.join(', '));
        clearCSVFile();
        return;
    }

    csvData = [];
    let validRows = 0;

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());
        if (values.length < headers.length) continue;

        const row = {};
        headers.forEach((header, index) => {
            row[header] = values[index] || '';
        });

        // Validate required fields
        if (row.question && row.type && row.points) {
            csvData.push(row);
            validRows++;
        }
    }

    if (validRows === 0) {
        alert('Tidak ada data valid yang ditemukan dalam file CSV!');
        clearCSVFile();
        return;
    }

    // Show preview
    showCSVPreview();
    document.getElementById('importBtn').disabled = false;
}

function showCSVPreview() {
    const previewDiv = document.getElementById('csvPreviewContent');
    let html = '<div class="space-y-3">';

    csvData.slice(0, 3).forEach((row, index) => {
        html += `
            <div class="border border-gray-200 rounded-lg p-3">
                <div class="font-medium text-gray-900">Soal ${index + 1}: ${row.question}</div>
                <div class="text-sm text-gray-600 mt-1">
                    <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">${getTypeLabel(row.type)}</span>
                    <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs">${row.points} poin</span>
                </div>
            </div>
        `;
    });

    if (csvData.length > 3) {
        html += `<div class="text-center text-gray-500 text-sm">... dan ${csvData.length - 3} soal lainnya</div>`;
    }

    html += '</div>';
    html += `<div class="mt-4 p-3 bg-green-50 rounded-lg">
        <div class="text-sm text-green-800">
            <strong>Total: ${csvData.length} soal</strong> siap untuk diimport
        </div>
    </div>`;

    previewDiv.innerHTML = html;
    document.getElementById('csvPreview').classList.remove('hidden');
}

function getTypeLabel(type) {
    switch(type) {
        case 'multiple_choice': return 'Pilihan Ganda';
        case 'true_false': return 'Benar/Salah';
        case 'short_answer': return 'Jawaban Singkat';
        default: return type;
    }
}

function importQuestions() {
    if (csvData.length === 0) {
        alert('Tidak ada data untuk diimport!');
        return;
    }

    // Clear existing questions
    document.getElementById('quiz_questions_container').innerHTML = '';
    questionCounter = 0;

    // Import each question
    csvData.forEach(row => {
        questionCounter++;
        const container = document.getElementById('quiz_questions_container');
        const questionDiv = document.createElement('div');
        questionDiv.className = 'border border-gray-200 rounded-lg p-4';
        questionDiv.id = `question_${questionCounter}`;

        let optionsHtml = '';
        if (row.type === 'multiple_choice') {
            const options = [row.option_a, row.option_b, row.option_c, row.option_d].filter(opt => opt);
            const correctIndex = ['A', 'B', 'C', 'D'].indexOf(row.correct_answer.toUpperCase());

            optionsHtml = options.map((option, index) => `
                <div class="flex items-center space-x-2">
                    <input type="radio" name="quiz_questions[${questionCounter}][correct_answer]" value="${index}"
                           ${index === correctIndex ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <input type="text" name="quiz_questions[${questionCounter}][options][]" value="${option}"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Pilihan ${String.fromCharCode(65 + index)}" required>
                </div>
            `).join('');
        } else if (row.type === 'true_false') {
            const isTrue = row.correct_answer.toUpperCase() === 'A' || row.correct_answer.toLowerCase() === 'benar';
            optionsHtml = `
                <div class="flex items-center space-x-2">
                    <input type="radio" name="quiz_questions[${questionCounter}][correct_answer]" value="true"
                           ${isTrue ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <label class="text-sm text-gray-700">Benar</label>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="radio" name="quiz_questions[${questionCounter}][correct_answer]" value="false"
                           ${!isTrue ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <label class="text-sm text-gray-700">Salah</label>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h5 class="font-medium text-gray-900">Soal ${questionCounter}</h5>
                <button type="button" onclick="removeQuizQuestion(${questionCounter})"
                        class="text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan</label>
                    <textarea name="quiz_questions[${questionCounter}][question]" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Masukkan pertanyaan...">${row.question}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                        <select name="quiz_questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="multiple_choice" ${row.type === 'multiple_choice' ? 'selected' : ''}>Pilihan Ganda</option>
                            <option value="true_false" ${row.type === 'true_false' ? 'selected' : ''}>Benar/Salah</option>
                            <option value="short_answer" ${row.type === 'short_answer' ? 'selected' : ''}>Jawaban Singkat</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                        <input type="number" name="quiz_questions[${questionCounter}][points]" min="1" max="100" value="${row.points || 10}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <!-- Options for multiple choice -->
                <div id="options_${questionCounter}" class="space-y-2" ${row.type === 'short_answer' ? 'style="display: none;"' : ''}>
                    <label class="block text-sm font-medium text-gray-700">Pilihan Jawaban</label>
                    <div class="space-y-2">
                        ${optionsHtml}
                    </div>
                    ${row.type === 'multiple_choice' ? `<button type="button" onclick="addQuestionOption(${questionCounter})" class="text-sm text-primary hover:text-primary-dark">+ Tambah Pilihan</button>` : ''}
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                    <textarea name="quiz_questions[${questionCounter}][explanation]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Penjelasan jawaban yang benar...">${row.explanation || ''}</textarea>
                </div>
            </div>
        `;

        container.appendChild(questionDiv);
    });

    // Close modal
    closeImportModal();

    // Show success message
    alert(`Berhasil mengimport ${csvData.length} soal kuis!`);
}

// Video upload functionality (simplified version)
let currentUpload = null;

function handleVideoFileSelect(input) {
    const file = input.files[0];
    if (file) {
        // Check file size (100MB limit)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File terlalu besar! Maksimal 100MB.');
            input.value = '';
            return;
        }

        // Check file type
        const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/quicktime'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung! Gunakan MP4, MOV, atau AVI.');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('video_upload_area').classList.add('hidden');
        document.getElementById('video_file_info').classList.remove('hidden');
        document.getElementById('video_file_name').textContent = file.name;
        document.getElementById('video_file_size').textContent = formatFileSize(file.size);
    }
}

function clearVideoFile() {
    document.getElementById('video_file').value = '';
    document.getElementById('uploaded_video_path').value = '';
    document.getElementById('video_upload_area').classList.remove('hidden');
    document.getElementById('video_file_info').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes >= 1024 * 1024) {
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(1) + ' KB';
    } else {
        return bytes + ' bytes';
    }
}

// Real-time validation system for material form
class MaterialFormValidator {
    constructor(formId) {
        this.form = document.getElementById(formId);
        if (!this.form) {
            console.error(`Form with ID '${formId}' not found`);
            return;
        }
        console.log(`MaterialFormValidator initialized for form: ${formId}`);
        this.validationRules = {
            required: (value) => value.trim() !== '',
            min: (value, param) => {
                // For numeric fields, check numeric value; for text fields, check length
                if (!isNaN(value) && value !== '') {
                    return parseFloat(value) >= parseFloat(param);
                }
                return value.length >= parseInt(param);
            },
            max: (value, param) => {
                // For numeric fields, check numeric value; for text fields, check length
                if (!isNaN(value) && value !== '') {
                    return parseFloat(value) <= parseFloat(param);
                }
                return value.length <= parseInt(param);
            },
            numeric: (value) => !isNaN(value) && value !== '',
            url: (value) => value === '' || /^https?:\/\/.+/.test(value),
        };
        this.init();
    }

    init() {
        if (!this.form) return;

        // Add event listeners to all form fields with validation
        const fields = this.form.querySelectorAll('[data-validation]');
        console.log(`Found ${fields.length} fields with data-validation attributes`);

        fields.forEach(field => {
            this.addFieldListeners(field);
        });

        // Character counter for description
        const descriptionField = document.getElementById('description');
        if (descriptionField) {
            descriptionField.addEventListener('input', this.updateCharacterCount.bind(this));
            this.updateCharacterCount(); // Initial count
        }
    }

    addFieldListeners(field) {
        const events = ['input', 'blur', 'change'];
        events.forEach(event => {
            field.addEventListener(event, () => {
                setTimeout(() => this.validateField(field), 100);
            });
        });
    }

    validateField(field) {
        const rules = field.dataset.validation.split('|');
        const value = field.value;
        const fieldName = field.name || field.id || 'unknown';

        let isValid = true;
        let errorMessage = '';

        console.log(`Validating field: ${fieldName}, value: "${value}", rules: ${rules.join(', ')}`);

        for (const rule of rules) {
            const [ruleName, param] = rule.split(':');

            if (!this.validationRules[ruleName]) {
                console.log(`Unknown validation rule: ${ruleName}`);
                continue;
            }

            const ruleValid = this.validationRules[ruleName](value, param);

            if (!ruleValid) {
                isValid = false;
                errorMessage = this.getErrorMessage(ruleName, param, field);
                console.log(`Field ${fieldName} failed validation rule: ${ruleName}, error: ${errorMessage}`);
                break;
            }
        }

        this.updateFieldUI(field, isValid, errorMessage);
        return isValid;
    }

    getErrorMessage(ruleName, param, field) {
        const fieldName = field.closest('div').querySelector('label')?.textContent?.replace('*', '').trim() || 'Field';

        const messages = {
            required: `${fieldName} harus diisi`,
            min: `${fieldName} minimal ${param} karakter`,
            max: `${fieldName} maksimal ${param} karakter`,
            numeric: `${fieldName} harus berupa angka`,
            url: `Format URL tidak valid`
        };

        // Special cases
        if (ruleName === 'min' && field.type === 'number') {
            return `${fieldName} minimal ${param}`;
        }
        if (ruleName === 'max' && field.type === 'number') {
            return `${fieldName} maksimal ${param}`;
        }

        return messages[ruleName] || `${fieldName} tidak valid`;
    }

    updateFieldUI(field, isValid, errorMessage) {
        const container = field.closest('div');

        // Remove existing validation feedback
        const existingFeedback = container.querySelector('.validation-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        // Update field styling
        field.classList.remove('border-red-500', 'border-green-500', 'validation-field');
        field.classList.add('validation-field');

        if (field.value.trim() !== '') {
            field.classList.add(isValid ? 'valid' : 'invalid');
        }

        // Create enhanced validation feedback
        if (field.value.trim() !== '' || field.dataset.validation.includes('required')) {
            this.createValidationFeedback(field, isValid, errorMessage, container);
        }
    }

    createValidationFeedback(field, isValid, errorMessage, container) {
        const feedback = document.createElement('div');
        feedback.className = 'validation-feedback';

        if (isValid && field.value.trim() !== '') {
            feedback.classList.add('success');
            feedback.innerHTML = `
                <div class="validation-feedback-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <strong>Bagus!</strong> ${this.getSuccessMessage(field)}
                </div>
            `;
        } else if (!isValid && errorMessage) {
            feedback.classList.add('error');
            feedback.innerHTML = `
                <div class="validation-feedback-icon">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <strong>Perlu diperbaiki:</strong> ${errorMessage}
                    ${this.getHelpText(field)}
                </div>
            `;
        }

        if (feedback.innerHTML) {
            container.appendChild(feedback);
        }
    }

    getSuccessMessage(field) {
        const fieldId = field.id;
        const successMessages = {
            'title': 'Judul materi sudah sesuai',
            'duration_minutes': 'Durasi sudah ditentukan',
            'description': 'Deskripsi materi sudah lengkap',
            'video_url': 'URL video sudah valid'
        };

        return successMessages[fieldId] || 'Data sudah valid';
    }

    getHelpText(field) {
        const helpTexts = {
            'title': '<br><small>💡 Tip: Gunakan judul yang jelas seperti "1.1 Pengenalan Konsep Dasar"</small>',
            'duration_minutes': '<br><small>💡 Tip: Estimasi waktu yang dibutuhkan siswa untuk menyelesaikan materi ini</small>',
            'video_url': '<br><small>💡 Tip: Pastikan URL dimulai dengan https:// dan dapat diakses</small>'
        };

        return helpTexts[field.id] || '';
    }

    updateCharacterCount() {
        const descriptionField = document.getElementById('description');
        const counter = document.getElementById('description-count');

        if (descriptionField && counter) {
            const count = descriptionField.value.length;
            counter.textContent = count;

            // Update counter color based on length
            const counterContainer = counter.parentElement;
            counterContainer.classList.remove('text-red-500', 'text-green-600', 'text-gray-500');

            if (count > 500) {
                counterContainer.classList.add('text-red-500');
            } else if (count > 0) {
                counterContainer.classList.add('text-green-600');
            } else {
                counterContainer.classList.add('text-gray-500');
            }
        }
    }

    validateForm() {
        if (!this.form) {
            console.error('Form not found, cannot validate');
            return false;
        }

        const fields = this.form.querySelectorAll('[data-validation]');
        let isFormValid = true;
        let validatedFields = [];
        let skippedFields = [];

        console.log(`Total fields with data-validation: ${fields.length}`);

        // Validate individual fields, but skip fields in hidden content areas
        fields.forEach(field => {
            const fieldName = field.name || field.id || 'unknown';

            // Check if field is in a hidden content area
            const hiddenParent = field.closest('#video_content.hidden, #text_content.hidden, #quiz_content.hidden, #assignment_content.hidden');

            // Skip validation for fields in hidden areas
            if (hiddenParent) {
                skippedFields.push(fieldName);
                console.log(`Skipping validation for hidden field: ${fieldName}`);
                return;
            }

            validatedFields.push(fieldName);
            console.log(`Validating field: ${fieldName}`);

            if (!this.validateField(field)) {
                console.log(`Field validation failed: ${fieldName}`);
                isFormValid = false;
            } else {
                console.log(`Field validation passed: ${fieldName}`);
            }
        });

        console.log(`Validated fields: ${validatedFields.join(', ')}`);
        console.log(`Skipped fields: ${skippedFields.join(', ')}`);
        console.log(`Overall form validation result: ${isFormValid}`);

        return isFormValid;
    }
}

// Enhanced form validation
let isSubmitting = false;
document.getElementById('materialForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Prevent multiple submissions
    if (isSubmitting) {
        console.log('Form submission already in progress, ignoring...');
        return false;
    }

    isSubmitting = true;
    console.log('Form submission started...');

    // Disable HTML5 validation for hidden fields before submission
    disableValidationForHiddenFields();

    // Also disable HTML5 validation entirely for this form to prevent browser validation conflicts
    this.setAttribute('novalidate', 'true');

    // Validate form using our validator
    console.log('Starting MaterialFormValidator validation...');
    if (window.materialValidator && !window.materialValidator.validateForm()) {
        console.log('MaterialFormValidator validation failed');
        showMaterialValidationSummary();
        isSubmitting = false; // Reset submission flag

        // Scroll to first invalid field
        const firstInvalidField = document.querySelector('.border-red-500, .validation-field.invalid');
        if (firstInvalidField) {
            firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            firstInvalidField.focus();
        }

        return false;
    }
    console.log('MaterialFormValidator validation passed');

    // Explicit validation for required fields
    const title = document.getElementById('title').value.trim();
    const duration = document.getElementById('duration_minutes').value.trim();
    const type = document.getElementById('type').value;

    console.log('Form data:', { title, duration, type });

    if (!title) {
        showCustomError('Judul materi harus diisi.');
        isSubmitting = false;
        return false;
    }

    if (!duration || duration < 1) {
        showCustomError('Estimasi durasi harus diisi dan minimal 1 menit.');
        isSubmitting = false;
        return false;
    }

    if (!type) {
        showCustomError('Tipe materi harus dipilih.');
        isSubmitting = false;
        return false;
    }

    if (type === 'video') {
        const videoSource = document.querySelector('input[name="video_source"]:checked');
        const videoUrl = document.getElementById('video_url').value;
        const videoFile = document.getElementById('video_file').files[0];

        if (!videoSource) {
            showCustomError('Pilih sumber video (URL atau Upload).');
            isSubmitting = false;
            return false;
        }

        if (videoSource.value === 'url' && !videoUrl) {
            showCustomError('Masukkan URL video.');
            isSubmitting = false;
            return false;
        }

        if (videoSource.value === 'upload' && !videoFile) {
            showCustomError('Upload video terlebih dahulu.');
            isSubmitting = false;
            return false;
        }
    }

    // Validate assignment fields if assignment type is selected
    if (type === 'assignment') {
        // Check if assignment builder has been loaded
        const assignmentBuilder = document.getElementById('assignment_builder');
        if (!assignmentBuilder || assignmentBuilder.innerHTML.includes('Assignment builder akan dimuat di sini')) {
            showCustomError('Assignment builder belum dimuat. Silakan tunggu sebentar atau refresh halaman.');
            isSubmitting = false;
            return false;
        }

        const assignmentDeadline = document.querySelector('input[name="assignment_deadline"]');
        const assignmentInstructions = document.querySelector('textarea[name="assignment_instructions"]');

        if (!assignmentDeadline || !assignmentDeadline.value.trim()) {
            showCustomError('Tanggal deadline tugas harus diisi.');
            isSubmitting = false;
            return false;
        }

        if (!assignmentInstructions || !assignmentInstructions.value.trim()) {
            showCustomError('Instruksi tugas harus diisi.');
            isSubmitting = false;
            return false;
        }
    }

    // Validate quiz fields if quiz type is selected
    if (type === 'quiz') {
        console.log('Validating quiz...');

        // Check if quiz builder has been loaded
        const quizBuilder = document.getElementById('quiz_builder');
        if (!quizBuilder || quizBuilder.innerHTML.includes('Kuis builder akan dimuat di sini')) {
            showCustomError('Kuis builder belum dimuat. Silakan tunggu sebentar atau refresh halaman.');
            isSubmitting = false;
            return false;
        }

        const quizQuestions = document.querySelectorAll('#quiz_questions_container > div');
        console.log('Found quiz questions:', quizQuestions.length);

        if (quizQuestions.length === 0) {
            console.log('No quiz questions found');
            showCustomError('Kuis harus memiliki minimal 1 soal. Klik "Tambah Soal" untuk menambahkan soal.');
            isSubmitting = false;
            return false;
        }

        // Validate each question
        for (let i = 0; i < quizQuestions.length; i++) {
            const questionDiv = quizQuestions[i];
            const questionText = questionDiv.querySelector('textarea[name*="quiz_questions"][name*="[question]"]');
            const questionType = questionDiv.querySelector('select[name*="quiz_questions"][name*="[type]"]');

            console.log(`Validating question ${i + 1}:`, {
                questionText: questionText?.value,
                questionType: questionType?.value
            });

            if (!questionText || !questionText.value.trim()) {
                console.log(`Question ${i + 1} text validation failed`);
                showCustomError(`Soal ${i + 1}: Pertanyaan harus diisi.`);
                questionText.scrollIntoView({ behavior: 'smooth', block: 'center' });
                questionText.focus();
                isSubmitting = false;
                return false;
            }

            if (!questionType || !questionType.value) {
                console.log(`Question ${i + 1} type validation failed`);
                showCustomError(`Soal ${i + 1}: Tipe soal harus dipilih.`);
                questionType.scrollIntoView({ behavior: 'smooth', block: 'center' });
                questionType.focus();
                isSubmitting = false;
                return false;
            }

            // Validate multiple choice options
            if (questionType.value === 'multiple_choice') {
                const options = questionDiv.querySelectorAll('input[name*="quiz_questions"][name*="[options]"]');
                let filledOptions = 0;

                options.forEach(option => {
                    if (option.value.trim()) {
                        filledOptions++;
                    }
                });

                if (filledOptions < 2) {
                    showCustomError(`Soal ${i + 1}: Soal pilihan ganda harus memiliki minimal 2 pilihan jawaban.`);
                    isSubmitting = false;
                    return false;
                }

                // Check if correct answer is selected
                const correctAnswerRadio = questionDiv.querySelector('input[name*="quiz_questions"][name*="[correct_answer]"]:checked');
                if (!correctAnswerRadio) {
                    showCustomError(`Soal ${i + 1}: Pilih jawaban yang benar.`);
                    isSubmitting = false;
                    return false;
                }
            }

            // Validate true/false correct answer
            if (questionType.value === 'true_false') {
                const correctAnswerRadio = questionDiv.querySelector('input[name*="quiz_questions"][name*="[correct_answer]"]:checked');
                if (!correctAnswerRadio) {
                    showCustomError(`Soal ${i + 1}: Pilih jawaban yang benar (Benar/Salah).`);
                    isSubmitting = false;
                    return false;
                }
            }
        }
    }

    console.log('All validations passed, submitting form...');

    // Remove any existing error messages
    const errorDiv = document.getElementById('material-error-summary');
    if (errorDiv) {
        errorDiv.remove();
    }

    // Show success message briefly
    showSuccessToast('Validasi berhasil! Menyimpan materi...');

    // Show loading state
    const submitBtn = document.getElementById('submit_btn');
    const submitText = document.getElementById('submit_text');
    submitBtn.disabled = true;
    submitText.innerHTML = `
        <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Menyimpan...
    `;

    // Log form data before submission for debugging
    const formData = new FormData(this);
    console.log('Form data being submitted:');
    for (let [key, value] of formData.entries()) {
        console.log(key, value);
    }

    // Submit the form
    this.submit();
});

// Function to disable HTML5 validation for hidden fields and dynamic content
function disableValidationForHiddenFields() {
    // Disable validation for hidden content areas
    const hiddenContainers = document.querySelectorAll('#video_content.hidden, #text_content.hidden, #quiz_content.hidden, #assignment_content.hidden');

    hiddenContainers.forEach(container => {
        const requiredFields = container.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.removeAttribute('required');
            field.setAttribute('data-was-required', 'true');
        });
    });

    // Also disable validation for any dynamically generated quiz/assignment fields that might not exist
    const dynamicSelectors = [
        'input[name*="quiz_questions"]',
        'textarea[name*="quiz_questions"]',
        'select[name*="quiz_questions"]',
        'input[name*="assignment_"]',
        'textarea[name*="assignment_"]'
    ];

    dynamicSelectors.forEach(selector => {
        const fields = document.querySelectorAll(selector);
        fields.forEach(field => {
            if (field.hasAttribute('required')) {
                field.removeAttribute('required');
                field.setAttribute('data-was-required', 'true');
            }
        });
    });
}

function showMaterialValidationSummary() {
    const invalidFields = document.querySelectorAll('.border-red-500, .validation-field.invalid');

    if (invalidFields.length > 0) {
        let errorMessages = [];

        invalidFields.forEach(field => {
            const fieldContainer = field.closest('div');
            const label = fieldContainer.querySelector('label');
            const fieldName = label ? label.textContent.replace('*', '').trim() : (field.name || field.id || 'Field');

            const validationMessage = fieldContainer.querySelector('.validation-message');
            const errorText = validationMessage && !validationMessage.classList.contains('hidden')
                ? validationMessage.textContent
                : `${fieldName} tidak valid`;

            errorMessages.push(errorText);
        });

        showValidationErrorModal(errorMessages);
    } else {
        showCustomError('Mohon periksa kembali semua field yang diperlukan.');
    }
}

function showValidationErrorModal(errorMessages) {
    // Remove existing modal if any
    const existingModal = document.getElementById('validation-error-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal
    const modal = document.createElement('div');
    modal.id = 'validation-error-modal';
    modal.className = 'fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 p-4';

    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-96 overflow-hidden">
            <div class="bg-red-500 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Kesalahan Validasi Form
                    </h3>
                    <button onclick="closeValidationErrorModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-6 max-h-64 overflow-y-auto">
                <p class="text-gray-700 mb-4">Mohon perbaiki kesalahan berikut sebelum melanjutkan:</p>
                <ul class="space-y-2">
                    ${errorMessages.map(msg => `
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span class="text-sm text-gray-700">${msg}</span>
                        </li>
                    `).join('')}
                </ul>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex justify-end">
                <button onclick="closeValidationErrorModal()"
                        class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium">
                    Tutup
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeValidationErrorModal();
        }
    });
}

function closeValidationErrorModal() {
    const modal = document.getElementById('validation-error-modal');
    if (modal) {
        modal.remove();
    }
}

function showSuccessToast(message) {
    // Remove existing toast if any
    const existingToast = document.getElementById('success-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // Create toast
    const toast = document.createElement('div');
    toast.id = 'success-toast';
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full';

    toast.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
    }, 100);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        toast.classList.add('translate-x-full');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

function showCustomError(message) {
    // Create or update error message
    let errorDiv = document.getElementById('material-error-summary');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.id = 'material-error-summary';
        errorDiv.className = 'mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg';

        // Insert at the top of the form
        const form = document.getElementById('materialForm');
        form.insertBefore(errorDiv, form.firstChild);
    }

    errorDiv.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">${message}</span>
        </div>
    `;

    // Scroll to error
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (errorDiv) {
            errorDiv.remove();
        }
    }, 5000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize validator
    console.log('Initializing MaterialFormValidator...');
    const validator = new MaterialFormValidator('materialForm');
    window.materialValidator = validator;
    console.log('MaterialFormValidator initialized successfully');

    // Check if there's an old type value and show the appropriate content
    const oldType = '{{ old("type") }}';
    if (oldType) {
        document.getElementById('type').value = oldType;
        toggleMaterialFields();
    }
});

// Nala AI Material Builder functionality
document.addEventListener('DOMContentLoaded', function() {
    const nalaBtn = document.getElementById('ai-material-builder-btn');
    if (!nalaBtn) return; // Button doesn't exist (user doesn't have membership)

    nalaBtn.addEventListener('click', async function() {
        const btn = this;
        const btnText = document.getElementById('ai-material-text');
        const originalText = btnText.innerHTML;

        // Check if we have the necessary context
        const contextElement = document.getElementById('nala-context');
        if (!contextElement) {
            showNotification('❌ Konteks bab tidak ditemukan. Silakan refresh halaman.', 'error');
            return;
        }

        // Show loading state with animation
        btn.disabled = true;
        btnText.innerHTML = '<svg class="w-4 h-4 animate-spin inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Nala sedang bekerja...';
        btn.classList.add('opacity-75');

    try {
        const response = await fetch('{{ route("tutor.ai-material-builder.generate", [$course, $chapter]) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();

        // Handle membership requirement error
        if (!response.ok && data.error === 'membership_required') {
            showMembershipUpgradeModal(data.message, data.upgrade_url);
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (data.success && data.material_suggestion) {
            fillFormWithAIMaterialSuggestion(data.material_suggestion);

            // Show success notification with fallback indicator
            const successMessage = data.is_fallback
                ? '✨ Nala memberikan saran materi (mode fallback). Silakan review dan edit sesuai kebutuhan.'
                : '✨ Nala berhasil membuat saran materi yang amazing! Silakan review dan edit sesuai kebutuhan.';

            showNotification(successMessage, 'success');

            // Scroll to form
            document.querySelector('.bg-white.rounded-lg.shadow-sm').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        } else {
            throw new Error(data.error || 'Failed to generate material suggestion');
        }
    } catch (error) {
        console.error('AI Material Builder Error:', error);

        // More specific error messages
        let errorMessage = '❌ Maaf, terjadi kesalahan saat membuat saran materi. Silakan coba lagi.';
        if (error.message.includes('HTTP error')) {
            errorMessage = '❌ Koneksi bermasalah. Periksa internet Anda dan coba lagi.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = '❌ Tidak dapat terhubung ke server. Silakan coba lagi.';
        }

        showNotification(errorMessage, 'error');
    } finally {
        // Reset button state
        btn.disabled = false;
        btnText.innerHTML = originalText;
        btn.classList.remove('opacity-75');
    }
    });
});

function fillFormWithAIMaterialSuggestion(suggestion) {
    console.log('Filling form with AI material suggestion:', suggestion);

    // Fill basic material information
    const titleField = document.getElementById('title');
    if (titleField && suggestion.title) {
        titleField.value = suggestion.title;
        titleField.dispatchEvent(new Event('input'));
    }

    const descField = document.getElementById('description');
    if (descField && suggestion.description) {
        descField.value = suggestion.description;
        descField.dispatchEvent(new Event('input'));
        // Update character count
        updateDescriptionCount();
    }

    const durationField = document.getElementById('duration_minutes');
    if (durationField && suggestion.duration_minutes) {
        durationField.value = suggestion.duration_minutes;
        durationField.dispatchEvent(new Event('input'));
    }

    const typeField = document.getElementById('type');
    if (typeField && suggestion.type) {
        typeField.value = suggestion.type;
        typeField.dispatchEvent(new Event('change'));
        // Trigger the material type change to show appropriate content area
        toggleMaterialFields();

        // If it's text type and we have content outline, fill the content area
        if (suggestion.type === 'text' && suggestion.content_outline) {
            setTimeout(() => {
                const contentField = document.getElementById('content');
                if (contentField) {
                    contentField.value = suggestion.content_outline;
                    // If TinyMCE is initialized, update it
                    if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
                        tinymce.get('content').setContent(suggestion.content_outline);
                    }
                }
            }, 500); // Wait for TinyMCE to initialize
        }
    }

    // Show success message
    console.log('Form filled successfully with AI material suggestion');
}

// Beautiful notification system (from course creation page)
function showNotification(message, type = 'error') {
    // Remove existing notification
    const existing = document.getElementById('validation-notification');
    if (existing) existing.remove();

    const notification = document.createElement('div');
    notification.id = 'validation-notification';
    notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg border-l-4 ${type === 'error' ? 'border-red-500' : 'border-green-500'} transform transition-all duration-300 translate-x-full`;

    notification.innerHTML = `
        <div class="p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    ${type === 'error' ?
                        '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>' :
                        '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'
                    }
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium ${type === 'error' ? 'text-red-800' : 'text-green-800'}">${message}</p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button onclick="this.closest('#validation-notification').remove()" class="inline-flex ${type === 'error' ? 'text-red-400 hover:text-red-600' : 'text-green-400 hover:text-green-600'}">
                        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }
    }, 4000);
}

function updateDescriptionCount() {
    const descField = document.getElementById('description');
    const countElement = document.getElementById('description-count');
    if (descField && countElement) {
        countElement.textContent = descField.value.length;
    }
}

// Show membership upgrade modal
function showMembershipUpgradeModal(message, upgradeUrl) {
    // Remove existing modal if any
    const existingModal = document.getElementById('membership-upgrade-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal
    const modal = document.createElement('div');
    modal.id = 'membership-upgrade-modal';
    modal.className = 'fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 p-4';

    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full overflow-hidden transform transition-all">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-4">
                <div class="flex items-center">
                    <img src="{{ asset('images/nala.png') }}" alt="Nala AI" class="w-10 h-10 rounded-full mr-3 border-2 border-white/20">
                    <div>
                        <h3 class="text-lg font-bold text-white">Nala AI Premium</h3>
                        <p class="text-purple-100 text-sm">Upgrade Required</p>
                    </div>
                </div>
            </div>

            <!-- Body -->
            <div class="p-6">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-700 mb-4">${message}</p>

                    <!-- Benefits -->
                    <div class="bg-purple-50 rounded-lg p-4 mb-4">
                        <h4 class="font-semibold text-purple-900 mb-2">Dengan NALA Membership:</h4>
                        <ul class="text-sm text-purple-800 space-y-1">
                            <li>✨ AI Material Builder</li>
                            <li>🚀 AI Course Builder</li>
                            <li>💬 Unlimited AI Chat</li>
                            <li>📊 Advanced Analytics</li>
                        </ul>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex flex-col sm:flex-row gap-3">
                    <button onclick="closeUpgradeModal()"
                            class="flex-1 px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                        Nanti Saja
                    </button>
                    <a href="${upgradeUrl}"
                       class="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center font-semibold">
                        Upgrade Sekarang
                    </a>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Animate in
    setTimeout(() => {
        modal.querySelector('.bg-white').classList.add('scale-100');
    }, 10);
}

function closeUpgradeModal() {
    const modal = document.getElementById('membership-upgrade-modal');
    if (modal) {
        modal.remove();
    }
}
</script>
@endpush
