@extends('layouts.app')

@section('title', 'Blog ' . $category->name . ' - Ngambiskuy Advance Learning Assistance')

@push('styles')
<style>
/* Blog Category Mobile-First Responsive Styles */
.blog-category-page {
    min-height: 100vh;
    background: #f8fafc;
}

/* Hero Section */
.blog-category-hero {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .blog-category-hero {
        padding: 4rem 0;
    }
}

.blog-category-hero-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .blog-category-hero-title {
        font-size: 2.5rem;
    }
}

@media (min-width: 1024px) {
    .blog-category-hero-title {
        font-size: 3rem;
    }
}

.blog-category-hero-description {
    font-size: 1rem;
    line-height: 1.5;
    color: rgba(219, 234, 254, 0.9);
    max-width: 48rem;
    margin: 0 auto;
}

@media (min-width: 768px) {
    .blog-category-hero-description {
        font-size: 1.125rem;
    }
}

@media (min-width: 1024px) {
    .blog-category-hero-description {
        font-size: 1.25rem;
    }
}

/* Breadcrumb */
.blog-category-breadcrumb {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
}

@media (min-width: 768px) {
    .blog-category-breadcrumb {
        padding: 1.5rem 0;
    }
}

.blog-category-breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    flex-wrap: wrap;
}

.blog-category-breadcrumb-link {
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    padding: 0.25rem 0;
}

.blog-category-breadcrumb-link:hover {
    color: #2563eb;
}

.blog-category-breadcrumb-current {
    color: #111827;
    font-weight: 500;
}

.blog-category-breadcrumb-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Search & Filter Section */
.blog-category-search-section {
    padding: 1.5rem 0;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

@media (min-width: 768px) {
    .blog-category-search-section {
        padding: 2rem 0;
    }
}

.blog-category-search-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
}

@media (min-width: 768px) {
    .blog-category-search-container {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: 1.5rem;
    }
}

.blog-category-search-form {
    flex: 1;
    max-width: 100%;
}

@media (min-width: 768px) {
    .blog-category-search-form {
        max-width: 28rem;
    }
}

.blog-category-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    min-height: 44px;
    transition: all 0.2s ease;
}

.blog-category-search-input:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: transparent;
}

.blog-category-filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

@media (min-width: 768px) {
    .blog-category-filter-container {
        justify-content: flex-end;
    }
}

.blog-category-filter-btn {
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    text-decoration: none;
    white-space: nowrap;
    background-color: #f3f4f6;
    color: #374151;
}

.blog-category-filter-btn:hover {
    background-color: #e5e7eb;
}

.blog-category-filter-btn.active {
    background-color: #2563eb;
    color: white;
}

.blog-category-filter-btn.active:hover {
    background-color: #1d4ed8;
}

/* Blog Posts Grid */
.blog-category-posts-section {
    padding: 2rem 0;
}

@media (min-width: 768px) {
    .blog-category-posts-section {
        padding: 3rem 0;
    }
}

.blog-category-results-info {
    margin-bottom: 1.5rem;
}

.blog-category-results-text {
    color: #6b7280;
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .blog-category-results-text {
        font-size: 1rem;
    }
}

.blog-category-posts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .blog-category-posts-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (min-width: 1024px) {
    .blog-category-posts-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.blog-category-post-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.blog-category-post-card:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.blog-category-post-image-container {
    position: relative;
}

.blog-category-post-image {
    width: 100%;
    height: 12rem;
    object-fit: cover;
}

.blog-category-post-category {
    position: absolute;
    top: 0.75rem;
    left: 0.75rem;
    background-color: #f3f4f6;
    color: #374151;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
}

.blog-category-post-content {
    padding: 1.5rem;
}

.blog-category-post-content-inner {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.blog-category-post-title {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-category-post-title-link {
    color: #111827;
    text-decoration: none;
    transition: color 0.2s ease;
}

.blog-category-post-title-link:hover {
    color: #2563eb;
}

.blog-category-post-excerpt {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-category-post-author {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.blog-category-post-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    object-fit: cover;
}

.blog-category-post-author-info {
    flex: 1;
}

.blog-category-post-author-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.125rem;
}

.blog-category-post-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.blog-category-pagination {
    margin-top: 3rem;
}

.blog-category-empty-state {
    text-align: center;
    padding: 3rem 0;
}

.blog-category-empty-icon {
    margin: 0 auto;
    height: 3rem;
    width: 3rem;
    color: #9ca3af;
}

.blog-category-empty-title {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
}

.blog-category-empty-text {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
}

.blog-category-empty-action {
    margin-top: 1.5rem;
}

.blog-category-empty-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: background-color 0.2s ease;
    min-height: 44px;
}

.blog-category-empty-btn:hover {
    background-color: #1d4ed8;
}
</style>
@endpush

@section('content')
<div class="blog-category-page">
    <!-- Hero Section -->
    <section class="blog-category-hero">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="blog-category-hero-title">{{ $category->name }}</h1>
                @if($category->description)
                <p class="blog-category-hero-description">
                    {{ $category->description }}
                </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Breadcrumb -->
    <nav class="blog-category-breadcrumb">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="blog-category-breadcrumb-nav">
                <a href="{{ route('home') }}" class="blog-category-breadcrumb-link">Home</a>
                <svg class="blog-category-breadcrumb-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="{{ route('blog.index') }}" class="blog-category-breadcrumb-link">Blog</a>
                <svg class="blog-category-breadcrumb-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="blog-category-breadcrumb-current">{{ $category->name }}</span>
            </div>
        </div>
    </nav>

    <!-- Search & Filter Section -->
    <section class="blog-category-search-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="blog-category-search-container">
                <!-- Search -->
                <form method="GET" class="blog-category-search-form">
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="Cari artikel dalam {{ $category->name }}..."
                               class="blog-category-search-input">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </form>

                <!-- Categories Filter -->
                <div class="blog-category-filter-container">
                    <a href="{{ route('blog.index') }}"
                       class="blog-category-filter-btn">
                        Semua Kategori
                    </a>
                    @foreach($categories as $cat)
                    <a href="{{ route('blog.category', $cat->slug) }}"
                       class="blog-category-filter-btn {{ $cat->id === $category->id ? 'active' : '' }}">
                        {{ $cat->name }}
                    </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Posts Grid -->
    <section class="blog-category-posts-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($blogPosts->count() > 0)
                <div class="blog-category-results-info">
                    <p class="blog-category-results-text">
                        Menampilkan {{ $blogPosts->count() }} dari {{ $blogPosts->total() }} artikel dalam kategori <strong>{{ $category->name }}</strong>
                        @if(request('search'))
                            untuk pencarian "<strong>{{ request('search') }}</strong>"
                        @endif
                    </p>
                </div>

                <div class="blog-category-posts-grid">
                    @foreach($blogPosts as $post)
                    <article class="blog-category-post-card">
                        <div class="blog-category-post-image-container">
                            <img src="{{ $post->featured_image ? asset('storage/' . $post->featured_image) : asset('images/blog/placeholder.svg') }}"
                                 alt="{{ $post->title }}" class="blog-category-post-image">
                            @if($post->category)
                            <span class="blog-category-post-category">{{ $post->category->name }}</span>
                            @endif
                        </div>

                        <div class="blog-category-post-content">
                            <div class="blog-category-post-content-inner">
                                <h3 class="blog-category-post-title">
                                    <a href="{{ route('blog.show', $post->slug) }}" class="blog-category-post-title-link">
                                        {{ $post->title }}
                                    </a>
                                </h3>

                                <p class="blog-category-post-excerpt">{{ $post->excerpt }}</p>

                                <div class="blog-category-post-author">
                                    <img src="{{ $post->author->profile_picture ? asset('storage/' . $post->author->profile_picture) : asset('images/avatars/placeholder.svg') }}"
                                         alt="{{ $post->author->name }}" class="blog-category-post-avatar">
                                    <div class="blog-category-post-author-info">
                                        <p class="blog-category-post-author-name">{{ $post->author->name }}</p>
                                        <div class="blog-category-post-meta">
                                            <span>{{ $post->formatted_published_date }}</span>
                                            <span>•</span>
                                            <span>{{ $post->read_time_text }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="blog-category-pagination">
                    {{ $blogPosts->appends(request()->query())->links() }}
                </div>
            @else
                <div class="blog-category-empty-state">
                    <svg class="blog-category-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="blog-category-empty-title">Tidak ada artikel</h3>
                    <p class="blog-category-empty-text">
                        @if(request('search'))
                            Tidak ada artikel dalam kategori {{ $category->name }} yang sesuai dengan pencarian "{{ request('search') }}".
                        @else
                            Belum ada artikel yang dipublikasikan dalam kategori {{ $category->name }}.
                        @endif
                    </p>
                    <div class="blog-category-empty-action">
                        <a href="{{ route('blog.index') }}"
                           class="blog-category-empty-btn">
                            Lihat Semua Artikel
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>
@endsection
