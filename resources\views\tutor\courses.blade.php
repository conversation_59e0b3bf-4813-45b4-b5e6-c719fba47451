@extends('layouts.tutor')

@section('title', 'Kurs<PERSON>')

@section('content')
<div class="tutor-dashboard-container tutor-course-management-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON>er -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex flex-col md:flex-row md:items-center md:space-x-3 mb-2">
                    <h1 class="text-xl md:text-2xl font-bold text-gray-900 mb-2 md:mb-0">Ku<PERSON><PERSON></h1>
                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 border border-emerald-200 w-fit">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Course Manager
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">Kelola dan pantau semua kursus yang Anda buat</p>
                <div class="tutor-quick-stats-mobile flex flex-col md:flex-row md:items-center mt-3 md:mt-2 space-y-2 md:space-y-0 md:space-x-4">
                    <div class="flex items-center text-sm text-emerald-600">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        {{ $publishedCourses->count() }} kursus aktif
                    </div>
                    <div class="flex items-center text-sm text-teal-600">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        {{ $draftCourses->count() }} draft
                    </div>
                </div>
            </div>
            <div class="tutor-header-actions flex flex-col md:flex-row md:items-center md:space-x-3 gap-3">
                <a href="{{ route('tutor.create-course') }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg hover:shadow-xl transition-all tutor-touch-friendly">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Buat Kursus Baru
                </a>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow-sm mb-4 md:mb-6 border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="tutor-tabs-mobile -mb-px flex flex-col md:flex-row md:space-x-8 px-3 md:px-6" aria-label="Tabs">
                <button class="{{ request('tab') === 'drafts' ? 'border-transparent text-gray-500' : 'border-emerald-500 text-emerald-600' }} tutor-touch-friendly whitespace-nowrap py-3 md:py-4 px-1 border-b-2 font-medium text-sm flex items-center justify-between md:justify-center" id="published-tab">
                    <span>Kursus Aktif</span>
                    <span class="bg-emerald-500 text-white ml-2 py-0.5 px-2.5 rounded-full text-xs">{{ $publishedCourses->count() }}</span>
                </button>
                <button class="{{ request('tab') === 'drafts' ? 'border-emerald-500 text-emerald-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-emerald-300' }} tutor-touch-friendly whitespace-nowrap py-3 md:py-4 px-1 border-b-2 font-medium text-sm flex items-center justify-between md:justify-center" id="drafts-tab">
                    <span>Draft</span>
                    <span class="bg-gray-100 text-gray-900 ml-2 py-0.5 px-2.5 rounded-full text-xs">{{ $draftCourses->count() }}</span>
                </button>
                <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-emerald-300 tutor-touch-friendly whitespace-nowrap py-3 md:py-4 px-1 border-b-2 font-medium text-sm" id="analytics-tab">
                    Analitik
                </button>
            </nav>
        </div>
    </div>

        <!-- Published Courses Tab -->
        <div id="published-content" class="tab-content {{ request('tab') === 'drafts' ? 'hidden' : '' }}">
            @if($publishedCourses->count() > 0)
                <div class="tutor-course-grid-mobile grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    @foreach($publishedCourses as $course)
                        <div class="tutor-course-card-mobile bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                            <div class="h-40 md:h-48 bg-gradient-to-br from-blue-500 to-purple-600 relative">
                                @if($course->thumbnail)
                                    <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                                @endif
                                <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                <div class="absolute bottom-3 md:bottom-4 left-3 md:left-4 text-white">
                                    <h3 class="text-base md:text-lg font-bold">{{ $course->title }}</h3>
                                    <p class="text-xs md:text-sm opacity-90">{{ $course->category->name }}</p>
                                </div>
                                <div class="absolute top-3 md:top-4 right-3 md:right-4">
                                    @if($course->is_free)
                                        <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">GRATIS</span>
                                    @else
                                        <span class="bg-primary text-white px-2 py-1 rounded-full text-xs font-medium">{{ $course->formatted_price }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="p-4 md:p-6">
                                <div class="flex items-center justify-between text-xs md:text-sm text-gray-600 mb-3 md:mb-4">
                                    <span>{{ number_format($course->total_students) }} siswa</span>
                                    <span>Rating: {{ number_format($course->average_rating, 1) }}/5</span>
                                </div>
                                <div class="flex items-center justify-between text-xs md:text-sm text-gray-600 mb-3 md:mb-4">
                                    <span>{{ $course->chapters->count() }} bab • {{ $course->lessons->count() }} materi</span>
                                    <span class="font-bold {{ $course->is_free ? 'text-green-600' : 'text-primary' }}">
                                        @if($course->is_free)
                                            GRATIS
                                        @else
                                            {{ $course->formatted_price }}
                                        @endif
                                    </span>
                                </div>
                                <div class="course-actions flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
                                    <a href="{{ route('tutor.curriculum.index', $course) }}" class="flex-1 btn btn-primary text-xs md:text-sm tutor-touch-friendly">
                                        <svg class="w-3 h-3 md:w-4 md:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        Kelola Kurikulum
                                    </a>
                                    <a href="{{ route('course.show', $course) }}" target="_blank" class="flex-1 btn btn-outline text-xs md:text-sm tutor-touch-friendly">
                                        <svg class="w-3 h-3 md:w-4 md:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        Lihat Publik
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="bg-white rounded-lg shadow-sm p-12 text-center border border-gray-200">
                    <div class="w-16 h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Belum Ada Kursus Aktif</h3>
                    <p class="text-gray-600 mb-6 max-w-md mx-auto">
                        Mulai berbagi pengetahuan Anda dengan membuat kursus pertama. Gunakan AI Course Builder untuk membantu Anda.
                    </p>
                    <!-- Mobile: Vertical Button Layout (primary action first) -->
                    <div class="flex flex-col md:flex-row gap-3 justify-center">
                        <a href="{{ route('tutor.create-course') }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white tutor-touch-friendly order-1">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Buat Kursus Baru
                        </a>
                        <button class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50 tutor-touch-friendly order-2">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Gunakan AI Builder
                        </button>
                    </div>
                </div>
            @endif
        </div>

        <!-- Drafts Tab -->
        <div id="drafts-content" class="tab-content {{ request('tab') === 'drafts' ? '' : 'hidden' }}">
            @if($draftCourses->count() > 0)
                <div class="tutor-course-grid-mobile grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                    @foreach($draftCourses as $course)
                        <div class="tutor-course-card-mobile bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                            <div class="h-40 md:h-48 bg-gradient-to-br from-gray-400 to-gray-600 relative">
                                @if($course->thumbnail)
                                    <img src="{{ asset('storage/' . $course->thumbnail) }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                                @endif
                                <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                                <div class="absolute bottom-3 md:bottom-4 left-3 md:left-4 text-white">
                                    <h3 class="text-base md:text-lg font-bold">{{ $course->title }}</h3>
                                    <p class="text-xs md:text-sm opacity-90">{{ $course->category->name }}</p>
                                </div>
                                <div class="absolute top-3 md:top-4 right-3 md:right-4">
                                    <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                        Draft
                                    </span>
                                </div>
                            </div>
                            <div class="p-4 md:p-6">
                                <div class="mb-3 md:mb-4">
                                    @php
                                        $totalLessons = $course->lessons->count();
                                        $publishedLessons = $course->lessons->where('is_published', true)->count();
                                        $completionPercentage = $totalLessons > 0 ? round(($publishedLessons / $totalLessons) * 100) : 0;
                                    @endphp
                                    <div class="flex justify-between text-xs md:text-sm text-gray-600 mb-1">
                                        <span>Progress Kurikulum</span>
                                        <span>{{ $completionPercentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: {{ $completionPercentage }}%"></div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between text-xs md:text-sm text-gray-600 mb-3 md:mb-4">
                                    <span>{{ $course->chapters->count() }} bab • {{ $course->lessons->count() }} materi</span>
                                    <span class="hidden md:inline">{{ $course->updated_at->diffForHumans() }}</span>
                                </div>

                                <!-- Mobile: Vertical Button Layout -->
                                <div class="course-actions flex flex-col space-y-2">
                                    <a href="{{ route('tutor.curriculum.index', $course) }}" class="btn btn-primary text-xs md:text-sm tutor-touch-friendly order-1">
                                        <svg class="w-3 h-3 md:w-4 md:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        Kelola Kurikulum
                                    </a>
                                    <div class="flex space-x-2 order-2">
                                        <a href="{{ route('tutor.edit-course', $course) }}" class="flex-1 btn btn-outline text-xs md:text-sm text-blue-600 hover:bg-blue-50 tutor-touch-friendly">
                                            <svg class="w-3 h-3 md:w-4 md:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit Kursus
                                        </a>
                                        <button class="flex-1 btn btn-outline text-xs md:text-sm text-red-600 hover:bg-red-50 tutor-touch-friendly">
                                            <svg class="w-3 h-3 md:w-4 md:h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            Hapus Draft
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="bg-white rounded-xl shadow-sm p-8 md:p-12 text-center">
                    <div class="w-16 h-16 md:w-24 md:h-24 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                        <svg class="w-8 h-8 md:w-12 md:h-12 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-2">Belum Ada Draft</h3>
                    <p class="text-gray-600 mb-6 md:mb-8 max-w-md mx-auto text-sm md:text-base">
                        Draft kursus yang sedang Anda kerjakan akan muncul di sini.
                    </p>
                    <a href="{{ route('tutor.create-course') }}" class="btn btn-primary tutor-touch-friendly">
                        Mulai Membuat Kursus
                    </a>
                </div>
            @endif
        </div>

        <!-- Analytics Tab -->
        <div id="analytics-content" class="tab-content hidden">
            <div class="bg-white rounded-xl shadow-sm p-8 md:p-12 text-center">
                <div class="w-16 h-16 md:w-24 md:h-24 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 md:mb-6">
                    <svg class="w-8 h-8 md:w-12 md:h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg md:text-xl font-bold text-gray-900 mb-2">Analitik Kursus</h3>
                <p class="text-gray-600 mb-6 md:mb-8 max-w-md mx-auto text-sm md:text-base">
                    Lihat performa detail setiap kursus Anda dengan analitik mendalam.
                </p>
                <a href="{{ route('tutor.analytics') }}" class="btn btn-primary tutor-touch-friendly">
                    Lihat Analitik Detail
                </a>
            </div>
        </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('[id$="-tab"]');
    const contents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-emerald-500', 'text-emerald-600');
                t.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active classes to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-emerald-500', 'text-emerald-600');

            // Hide all content
            contents.forEach(content => content.classList.add('hidden'));

            // Show corresponding content
            const contentId = this.id.replace('-tab', '-content');
            document.getElementById(contentId).classList.remove('hidden');
        });
    });
});
</script>
@endsection
