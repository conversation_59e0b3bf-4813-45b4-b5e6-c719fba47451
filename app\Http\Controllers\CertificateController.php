<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\LessonProgress;
use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\Certificate;

use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class CertificateController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Download certificate for a completed course.
     */
    public function downloadCourseCertificate(Course $course)
    {
        $user = Auth::user();

        // Load course with necessary relationships
        $course->load(['chapters.lessons', 'tutor']);

        // Check if user is enrolled in the course or has access to free course
        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        // For free courses, check if user has paid membership for certificate access
        if ($course->is_free || $course->price == 0) {
            $membership = $user->activeMembership;
            
            if (!$membership || !$membership->has_free_certifications) {
                return redirect()->back()->with('error', 'Sertifikat kursus gratis hanya tersedia untuk member berbayar. Silakan upgrade membership Anda untuk mengakses sertifikat.');
            }
        } else {
            // For paid courses, enrollment is required
            if (!$enrollment) {
                abort(404, 'Anda belum terdaftar dalam kursus ini.');
            }
        }

        // Calculate course progress
        $lessonIds = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('id');
        });

        $totalLessons = $lessonIds->count();

        if ($totalLessons === 0) {
            abort(404, 'Kursus ini belum memiliki pelajaran.');
        }

        $courseProgress = LessonProgress::where('user_id', $user->id)
            ->whereIn('lesson_id', $lessonIds)
            ->get();

        $completedLessons = $courseProgress->where('status', 'completed')->count();
        $progressPercentage = round(($completedLessons / $totalLessons) * 100);

        // Check if course is completed (100% progress)
        if ($progressPercentage < 100) {
            return redirect()->back()->with('error', 'Anda harus menyelesaikan semua pelajaran untuk mendapatkan sertifikat.');
        }

        // Get completion date (latest completed lesson)
        $completionDate = $courseProgress->where('status', 'completed')
            ->max('completed_at') ?? $enrollment->updated_at;

        // Generate certificate ID
        $certificateId = 'NGMB-' . strtoupper(substr($course->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Store certificate record in database
        $this->storeCertificateRecord($certificateId, $user, $course, 'course', Carbon::parse($completionDate), [
            'total_lessons' => $totalLessons,
            'total_duration' => $course->total_duration_minutes ?? 0,
        ]);

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'course' => $course,
            'enrollment' => $enrollment,
            'completion_date' => Carbon::parse($completionDate),
            'certificate_id' => $certificateId,
            'total_lessons' => $totalLessons,
            'total_duration' => $course->total_duration_minutes ?? 0,
            'issue_date' => now(),
        ];

        // Generate PDF with specific options to avoid GD extension
        $pdf = Pdf::loadView('certificates.course-certificate', $certificateData);
        $pdf->setPaper('A4', 'landscape');

        // Configure DomPDF options to disable image processing that requires GD
        $pdf->getDomPDF()->getOptions()->set([
            'isRemoteEnabled' => false,
            'isPhpEnabled' => false,
            'isJavascriptEnabled' => false,
            'isHtml5ParserEnabled' => true,
        ]);

        // Download the certificate
        $filename = 'Sertifikat-' . str_replace(' ', '-', $course->title) . '-' . str_replace(' ', '-', $user->name) . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Preview certificate for a completed course.
     */
    public function previewCourseCertificate(Course $course)
    {
        $user = Auth::user();

        // Load course with necessary relationships
        $course->load(['chapters.lessons', 'tutor']);

        // Check if user is enrolled in the course or has access to free course
        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        // For free courses, check if user has paid membership for certificate access
        if ($course->is_free || $course->price == 0) {
            $membership = $user->activeMembership;
            
            if (!$membership || !$membership->has_free_certifications) {
                return redirect()->back()->with('error', 'Sertifikat kursus gratis hanya tersedia untuk member berbayar. Silakan upgrade membership Anda untuk mengakses sertifikat.');
            }
        } else {
            // For paid courses, enrollment is required
            if (!$enrollment) {
                abort(404, 'Anda belum terdaftar dalam kursus ini.');
            }
        }

        // Calculate course progress
        $lessonIds = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('id');
        });

        $totalLessons = $lessonIds->count();

        if ($totalLessons === 0) {
            abort(404, 'Kursus ini belum memiliki pelajaran.');
        }

        $courseProgress = LessonProgress::where('user_id', $user->id)
            ->whereIn('lesson_id', $lessonIds)
            ->get();

        $completedLessons = $courseProgress->where('status', 'completed')->count();
        $progressPercentage = round(($completedLessons / $totalLessons) * 100);

        // Check if course is completed (100% progress)
        if ($progressPercentage < 100) {
            return redirect()->back()->with('error', 'Anda harus menyelesaikan semua pelajaran untuk melihat sertifikat.');
        }

        // Get completion date (latest completed lesson)
        $completionDate = $courseProgress->where('status', 'completed')
            ->max('completed_at') ?? $enrollment->updated_at;

        // Generate certificate ID
        $certificateId = 'NGMB-' . strtoupper(substr($course->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Store certificate record in database
        $this->storeCertificateRecord($certificateId, $user, $course, 'course', Carbon::parse($completionDate), [
            'total_lessons' => $totalLessons,
            'total_duration' => $course->total_duration_minutes ?? 0,
        ]);

        // Redirect to verification page instead of showing certificate template
        return redirect()->route('certificate.verify', $certificateId);
    }

    /**
     * Download certificate for a passed exam.
     */
    public function downloadExamCertificate(Exam $exam)
    {
        $user = Auth::user();

        // Check if user has active membership (required for exam certificates)
        $membership = $user->activeMembership;

        if (!$membership) {
            return redirect()->route('payment.pricing')
                ->with('error', 'Sertifikat ujian hanya tersedia untuk member berbayar. Silakan upgrade membership Anda untuk mengakses sertifikat.');
        }

        // Load exam with necessary relationships
        $exam->load(['tutor']);

        // Check if user has passed the exam
        $passedAttempt = ExamAttempt::where('user_id', $user->id)
            ->where('exam_id', $exam->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->first();

        if (!$passedAttempt) {
            abort(404, 'Anda belum lulus ujian ini atau belum mengikuti ujian.');
        }

        // Generate certificate ID
        $certificateId = 'EXAM-' . strtoupper(substr($exam->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Store certificate record in database
        $this->storeCertificateRecord($certificateId, $user, $exam, 'exam', Carbon::parse($passedAttempt->completed_at), [
            'score_percentage' => $passedAttempt->score_percentage,
            'total_questions' => $passedAttempt->total_questions,
            'correct_answers' => $passedAttempt->correct_answers,
        ]);

        // Prepare certificate data
        $certificateData = [
            'user' => $user,
            'exam' => $exam,
            'attempt' => $passedAttempt,
            'completion_date' => Carbon::parse($passedAttempt->completed_at),
            'certificate_id' => $certificateId,
            'score_percentage' => $passedAttempt->score_percentage,
            'total_questions' => $passedAttempt->total_questions,
            'correct_answers' => $passedAttempt->correct_answers,
            'issue_date' => now(),
        ];

        // Generate PDF with specific options to avoid GD extension
        $pdf = Pdf::loadView('certificates.exam-certificate', $certificateData);
        $pdf->setPaper('A4', 'landscape');

        // Configure DomPDF options to disable image processing that requires GD
        $pdf->getDomPDF()->getOptions()->set([
            'isRemoteEnabled' => false,
            'isPhpEnabled' => false,
            'isJavascriptEnabled' => false,
            'isHtml5ParserEnabled' => true,
        ]);

        // Download the certificate
        $filename = 'Sertifikat-Ujian-' . str_replace(' ', '-', $exam->title) . '-' . str_replace(' ', '-', $user->name) . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Preview certificate for a passed exam.
     */
    public function previewExamCertificate(Exam $exam)
    {
        $user = Auth::user();

        // Check if user has active membership (required for exam certificates)
        $membership = $user->activeMembership;

        if (!$membership) {
            return redirect()->route('payment.pricing')
                ->with('error', 'Sertifikat ujian hanya tersedia untuk member berbayar. Silakan upgrade membership Anda untuk mengakses sertifikat.');
        }

        // Load exam with necessary relationships
        $exam->load(['tutor']);

        // Check if user has passed the exam
        $passedAttempt = ExamAttempt::where('user_id', $user->id)
            ->where('exam_id', $exam->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->first();

        if (!$passedAttempt) {
            abort(404, 'Anda belum lulus ujian ini atau belum mengikuti ujian.');
        }

        // Generate certificate ID
        $certificateId = 'EXAM-' . strtoupper(substr($exam->id, 0, 4)) . '-' . strtoupper(substr($user->id, 0, 4)) . '-' . date('Y');

        // Store certificate record in database
        $this->storeCertificateRecord($certificateId, $user, $exam, 'exam', Carbon::parse($passedAttempt->completed_at), [
            'score_percentage' => $passedAttempt->score_percentage,
            'total_questions' => $passedAttempt->total_questions,
            'correct_answers' => $passedAttempt->correct_answers,
        ]);

        // Redirect to verification page instead of showing certificate template
        return redirect()->route('certificate.verify', $certificateId);
    }

    /**
     * Store certificate record in database for verification.
     */
    private function storeCertificateRecord(string $certificateId, $user, $certifiable, string $type, Carbon $completionDate, array $additionalData = []): void
    {
        // Check if certificate already exists
        $existingCertificate = Certificate::where('certificate_id', $certificateId)->first();

        if (!$existingCertificate) {
            Certificate::create([
                'certificate_id' => $certificateId,
                'user_id' => $user->id,
                'type' => $type,
                'certifiable_id' => $certifiable->id,
                'certifiable_type' => get_class($certifiable),
                'user_name' => $user->name,
                'title' => $certifiable->title,
                'instructor_name' => $certifiable->tutor->name,
                'completion_date' => $completionDate,
                'issue_date' => now(),
                'certificate_data' => $additionalData,
                'is_active' => true,
            ]);
        }
    }

    /**
     * Verify certificate by certificate ID (public method).
     */
    public function verify(string $certificateId)
    {
        $certificate = Certificate::where('certificate_id', $certificateId)
            ->where('is_active', true)
            ->with(['user', 'certifiable'])
            ->first();

        if (!$certificate) {
            abort(404, 'Certificate not found or has been revoked.');
        }

        return view('certificates.verify', compact('certificate'));
    }


}
