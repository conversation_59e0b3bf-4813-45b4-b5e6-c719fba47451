<?php

namespace App\Http\Controllers\Nala;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TutorController extends Controller
{
    private $geminiApiKey;
    private $geminiModel;

    public function __construct()
    {
        $this->geminiApiKey = config('services.gemini.api_key', env('GEMINI_API_KEY'));
        $this->geminiModel = config('services.gemini.model', env('GEMINI_MODEL', 'gemini-2.0-flash'));
    }

    /**
     * Handle tutor-related questions - Send data to Gemini AI
     */
    public function handleTutorQuestion($message, $context, $userProfile, $membership, $tutorContext = [])
    {
        try {
            // Enhanced debug logging
            Log::info('Nala TutorController: Received tutor question', [
                'message' => $message,
                'context' => $context,
                'membership' => $membership,
                'tutor_context_keys' => array_keys($tutorContext),
                'tutor_context_full' => $tutorContext,
                'courses_count' => count($tutorContext['courses'] ?? []),
                'exams_count' => count($tutorContext['exams'] ?? []),
                'blogs_count' => count($tutorContext['blogs'] ?? []),
                'tutor_info' => $tutorContext['tutor'] ?? null,
                'stats_info' => $tutorContext['stats'] ?? null
            ]);

            // Build comprehensive tutor data for Gemini
            $tutorData = $this->buildTutorDataForGemini($tutorContext, $context, $userProfile, $membership);

            // Create system prompt with tutor data
            $systemPrompt = $this->buildTutorSystemPrompt($userProfile, $tutorData, $membership);

            // Create user prompt
            $userPrompt = $this->buildTutorUserPrompt($message, $context, $tutorData);

            // Call Gemini AI with tutor data
            return $this->callGeminiAPI($systemPrompt, $userPrompt);

        } catch (\Exception $e) {
            Log::error('Nala TutorController Error: ' . $e->getMessage());
            return $this->getFallbackTutorResponse($message, $tutorContext);
        }
    }

    /**
     * Build tutor data for Gemini AI
     */
    private function buildTutorDataForGemini($tutorContext, $context, $userProfile, $membership)
    {
        $tutorData = [
            'tutor_info' => $tutorContext['tutor'] ?? [],
            'stats' => $tutorContext['stats'] ?? [],
            'courses' => $tutorContext['courses'] ?? [],
            'exams' => $tutorContext['exams'] ?? [],
            'blogs' => $tutorContext['blogs'] ?? [],
            'context' => $context,
            'user_membership' => $membership
        ];

        return $tutorData;
    }

    /**
     * Build system prompt for tutor questions
     */
    private function buildTutorSystemPrompt($userProfile, $tutorData, $membership)
    {
        $userName = $userProfile['basic_info']['name'] ?? 'Pengguna';
        $tutorName = $tutorData['tutor_info']['name'] ?? 'Tutor';

        $prompt = "Anda adalah Nala, asisten AI Ngambiskuy yang membantu user memahami profil tutor.

KONTEKS TUTOR:
Nama: {$tutorName}";

        // Only include available information, skip null/empty fields
        if (!empty($tutorData['tutor_info']['job_title'])) {
            $prompt .= "\nPosisi: " . $tutorData['tutor_info']['job_title'];
        }
        if (!empty($tutorData['tutor_info']['company'])) {
            $prompt .= "\nPerusahaan: " . $tutorData['tutor_info']['company'];
        }
        if (!empty($tutorData['tutor_info']['location'])) {
            $prompt .= "\nLokasi: " . $tutorData['tutor_info']['location'];
        }
        if (!empty($tutorData['tutor_info']['education_level'])) {
            $prompt .= "\nPendidikan: " . $tutorData['tutor_info']['education_level'];
        }
        if (!empty($tutorData['tutor_info']['joined_date'])) {
            $prompt .= "\nBergabung: " . $tutorData['tutor_info']['joined_date'];
        }

        $prompt .= "\n\nSTATISTIK TUTOR:";

        // Only show meaningful statistics
        if (($tutorData['stats']['total_courses'] ?? 0) > 0) {
            $prompt .= "\n- Total Kursus: " . $tutorData['stats']['total_courses'];
        }
        if (($tutorData['stats']['total_exams'] ?? 0) > 0) {
            $prompt .= "\n- Total Ujian: " . $tutorData['stats']['total_exams'];
        }
        if (($tutorData['stats']['total_students'] ?? 0) > 0) {
            $prompt .= "\n- Total Siswa: " . $tutorData['stats']['total_students'];
        }
        if (($tutorData['stats']['average_rating'] ?? 0) > 0) {
            $prompt .= "\n- Rating Rata-rata: " . $tutorData['stats']['average_rating'];
        }

        // Only include skills if available
        if (!empty($tutorData['tutor_info']['skills'])) {
            $skills = is_array($tutorData['tutor_info']['skills'])
                ? implode(', ', $tutorData['tutor_info']['skills'])
                : $tutorData['tutor_info']['skills'];
            $prompt .= "\n\nKEAHLIAN TUTOR:\n- " . $skills;
        }

        // Only include description if available
        if (!empty($tutorData['tutor_info']['description'])) {
            $prompt .= "\n\nDESKRIPSI TUTOR:\n" . $tutorData['tutor_info']['description'];
        }

        // Add published courses information (only show if available)
        if (!empty($tutorData['courses']) && is_array($tutorData['courses']) && count($tutorData['courses']) > 0) {
            $publishedCourses = [];
            foreach ($tutorData['courses'] as $course) {
                $courseStatus = $course['status'] ?? 'draft';
                $isPublished = ($course['is_published'] ?? false) || $courseStatus === 'published';

                if ($isPublished) {
                    $publishedCourses[] = $course;
                }
            }

            if (!empty($publishedCourses)) {
                $prompt .= "\n\nKURSUS YANG DITAWARKAN:";
                foreach (array_slice($publishedCourses, 0, 5) as $course) {
                    $courseTitle = $course['title'] ?? 'Kursus';
                    $courseLevel = $course['level'] ?? '';

                    $prompt .= "\n- {$courseTitle}";
                    if (!empty($courseLevel)) {
                        $prompt .= " ({$courseLevel})";
                    }

                    if (!empty($course['description'])) {
                        $description = substr($course['description'], 0, 80);
                        $prompt .= " - {$description}...";
                    }

                    if (!empty($course['category'])) {
                        $prompt .= " [Kategori: {$course['category']}]";
                    }
                }

                if (count($publishedCourses) > 5) {
                    $prompt .= "\n- Dan " . (count($publishedCourses) - 5) . " kursus lainnya";
                }
            }
        }

        // Add published exams information (only show if available)
        if (!empty($tutorData['exams']) && is_array($tutorData['exams']) && count($tutorData['exams']) > 0) {
            $publishedExams = array_filter($tutorData['exams'], function($exam) {
                return ($exam['is_published'] ?? false) || ($exam['status'] ?? 'draft') === 'published';
            });

            if (!empty($publishedExams)) {
                $prompt .= "\n\nUJIAN YANG TERSEDIA:";
                foreach (array_slice($publishedExams, 0, 3) as $exam) {
                    $examTitle = $exam['title'] ?? 'Ujian';
                    $examLevel = $exam['difficulty_level'] ?? '';

                    $prompt .= "\n- {$examTitle}";
                    if (!empty($examLevel)) {
                        $prompt .= " ({$examLevel})";
                    }
                }

                if (count($publishedExams) > 3) {
                    $prompt .= "\n- Dan " . (count($publishedExams) - 3) . " ujian lainnya";
                }
            }
        }

        $prompt .= "\n\nTUGAS ANDA:
1. Berikan informasi positif tentang tutor berdasarkan data yang tersedia
2. Fokus pada keunggulan: kursus yang dipublikasikan, jumlah siswa, rating, dan keahlian
3. Jawab maksimal 2-3 kalimat yang langsung to the point
4. SELALU sertakan Call-to-Action (CTA) yang relevan
5. Gunakan bahasa ramah dan natural seperti teman

PANDUAN CTA:
- Jika ada kursus spesifik: 'Daftar sekarang di kursus [nama kursus]!'
- Jika tidak ada kursus spesifik: 'Lihat kursus lainnya' atau 'Jelajahi kursus terbaru'
- Untuk tutor dengan banyak kursus: 'Yuk cek semua kursusnya!'

LARANGAN MUTLAK:
- JANGAN PERNAH sebutkan data yang kosong/null (posisi, perusahaan, lokasi, pendidikan)
- JANGAN gunakan kata 'belum', 'tidak ada', 'kosong', atau bahasa negatif lainnya
- JANGAN buat informasi yang tidak ada dalam data konteks
- JANGAN sebutkan nama tutor lain atau contoh fiktif
- JANGAN berlebihan dalam memuji
- JANGAN sebutkan sistem teknis atau database

CONTOH RESPONS YANG BAIK:
'Kak [Nama] sudah membimbing [X] siswa dengan rating [Y]! Beliau punya [Z] kursus [bidang] yang bisa kamu ikuti. Yuk daftar di kursus [nama kursus]!'

User: {$userName}
Membership: {$membership}

Jawab dengan positif, singkat, dan selalu ada CTA!";

        return $prompt;
    }

    /**
     * Build user prompt for tutor questions
     */
    private function buildTutorUserPrompt($message, $context, $tutorData)
    {
        $tutorName = $tutorData['tutor_info']['name'] ?? 'Tutor';

        $prompt = "Pertanyaan user tentang tutor {$tutorName}: {$message}

Konteks halaman: {$context}

Berikan jawaban yang:
1. Fokus pada hal positif dan tersedia saja
2. Maksimal 2-3 kalimat singkat dan padat
3. WAJIB ada CTA yang relevan
4. Natural dan ramah seperti teman
5. Hindari menyebut data yang kosong/tidak ada";

        return $prompt;
    }

    /**
     * Get fallback response for tutor questions
     */
    private function getFallbackTutorResponse($message, $tutorContext)
    {
        $tutorName = $tutorContext['tutor']['name'] ?? 'tutor ini';

        $fallbackResponses = [
            "Kak {$tutorName} adalah tutor berpengalaman di Ngambiskuy! Yuk lihat kursus-kursusnya yang menarik.",
            "{$tutorName} punya keahlian yang bagus untuk membantu perjalanan belajar kamu. Cek profil lengkapnya!",
            "Tutor {$tutorName} siap membimbing kamu mencapai tujuan belajar. Jelajahi kursus terbaru!",
            "Saya siap bantu kamu mengenal lebih dekat dengan {$tutorName}. Lihat kursus lainnya yuk!"
        ];

        return $fallbackResponses[array_rand($fallbackResponses)];
    }

    /**
     * Call Gemini AI API
     */
    private function callGeminiAPI($systemPrompt, $userPrompt)
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$this->geminiModel}:generateContent?key={$this->geminiApiKey}";

        $payload = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $systemPrompt . "\n\n" . $userPrompt]
                    ]
                ]
            ],
            'generationConfig' => [
                'temperature' => 0.8,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 400,
            ]
        ];

        $response = Http::timeout(20)->post($url, $payload);

        if (!$response->successful()) {
            throw new \Exception('Gemini API request failed: ' . $response->body());
        }

        $data = $response->json();

        if (!isset($data['candidates'][0]['content']['parts'][0]['text'])) {
            throw new \Exception('Invalid response format from Gemini API');
        }

        return trim($data['candidates'][0]['content']['parts'][0]['text']);
    }
}
