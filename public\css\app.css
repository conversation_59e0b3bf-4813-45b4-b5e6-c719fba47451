/* Tailwind CSS base styles */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom CSS Variables */
:root {
  --primary: #FF6B35;
  --primary-dark: #E55A2B;
  --primary-light: #FF8C42;
}

/* Font Family */
body {
  font-family: 'Inter', sans-serif;
}

/* Custom Component Classes */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-primary text-white hover:bg-primary-dark focus:ring-primary;
  background-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-outline {
  @apply border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  border-color: var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
}

.btn-secondary {
  @apply bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500;
}

.btn-ghost {
  @apply text-gray-700 hover:text-gray-900 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-500 text-white hover:bg-green-600 focus:ring-green-500;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary;
}

.select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary bg-white;
}

.card {
  @apply bg-white rounded-lg shadow-sm border;
}

/* Primary color utilities */
.text-primary {
  color: var(--primary);
}

.bg-primary {
  background-color: var(--primary);
}

.border-primary {
  border-color: var(--primary);
}

.bg-primary\/10 {
  background-color: rgba(255, 107, 53, 0.1);
}

.bg-primary\/20 {
  background-color: rgba(255, 107, 53, 0.2);
}

/* Course tab styles */
.course-tab {
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors;
}

.course-tab.active {
  @apply bg-primary text-white;
  background-color: var(--primary);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Gradient text */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--primary), #ef4444);
}

/* Custom animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-menu.hidden {
    display: none;
  }
  
  .mobile-menu {
    display: block;
  }
}

/* Hover effects */
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Focus states */
.focus\:ring-primary:focus {
  --tw-ring-color: var(--primary);
}

.focus\:border-primary:focus {
  border-color: var(--primary);
}