@extends('layouts.tutor')

@section('title', 'Preview Kursus - ' . $course->title)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header with <PERSON><PERSON> Controls -->
    <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('tutor.curriculum.index', $course) }}" 
                       class="text-gray-600 hover:text-gray-900 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">Preview Kursus</h1>
                        <p class="text-sm text-gray-600"><PERSON><PERSON><PERSON> seperti yang dilihat siswa</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Mode Preview
                    </span>
                    <a href="{{ route('course.show', $course) }}" 
                       target="_blank"
                       class="btn bg-teal-600 hover:bg-teal-700 text-white text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-2M7 7l10 10M17 7v4m0 0h-4"></path>
                        </svg>
                        Lihat Live
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content (Student View) -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Course Header -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <div class="flex items-start space-x-4">
                        @if($course->thumbnail)
                            <img src="{{ asset('storage/' . $course->thumbnail) }}" 
                                 alt="{{ $course->title }}" 
                                 class="w-32 h-20 object-cover rounded-lg">
                        @endif
                        <div class="flex-1">
                            <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $course->title }}</h1>
                            <p class="text-gray-600 mb-3">{{ $course->description }}</p>
                            
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span>{{ $totalLessons }} materi</span>
                                <span>{{ floor($totalDuration / 60) }}j {{ $totalDuration % 60 }}m</span>
                                <span class="capitalize">{{ $course->level }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Description -->
                @if($course->long_description)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-3">Tentang Kursus</h2>
                    <div class="prose prose-sm max-w-none text-gray-700">
                        {!! nl2br(e($course->long_description)) !!}
                    </div>
                </div>
                @endif

                <!-- Learning Outcomes -->
                @if($course->learning_outcomes)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-3">Yang Akan Anda Pelajari</h2>
                    <ul class="space-y-2">
                        @foreach($course->learning_outcomes as $outcome)
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-gray-700">{{ $outcome }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Course Curriculum -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Kurikulum Kursus</h2>
                    
                    @if($course->chapters->count() > 0)
                        <div class="space-y-4">
                            @foreach($course->chapters as $chapter)
                                <div class="border border-gray-200 rounded-lg">
                                    <div class="p-4 bg-gray-50">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-medium text-gray-900">{{ $chapter->title }}</h3>
                                            <span class="text-sm text-gray-500">{{ $chapter->lessons->count() }} materi</span>
                                        </div>
                                        @if($chapter->description)
                                            <p class="text-sm text-gray-600 mt-1">{{ $chapter->description }}</p>
                                        @endif
                                    </div>
                                    
                                    @if($chapter->lessons->count() > 0)
                                        <div class="divide-y divide-gray-200">
                                            @foreach($chapter->lessons as $lesson)
                                                <div class="p-4 flex items-center justify-between">
                                                    <div class="flex items-center space-x-3">
                                                        <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                                                            @if($lesson->type === 'video')
                                                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-6V7a2 2 0 00-2-2H5a2 2 0 00-2 2v3m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3z"></path>
                                                                </svg>
                                                            @elseif($lesson->type === 'text')
                                                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                                </svg>
                                                            @elseif($lesson->type === 'quiz')
                                                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                            @endif
                                                        </div>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-gray-900">{{ $lesson->title }}</h4>
                                                            <p class="text-xs text-gray-500">{{ $lesson->type_indonesian }}</p>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                        @if($lesson->duration_minutes > 0)
                                                            <span>{{ $lesson->duration_minutes }} menit</span>
                                                        @endif
                                                        @if($lesson->is_preview)
                                                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Preview</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-500 text-center py-8">Belum ada kurikulum yang tersedia.</p>
                    @endif
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Course Info Card -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <div class="text-center mb-4">
                        @if($course->is_free)
                            <div class="text-3xl font-bold text-green-600 mb-2">Gratis</div>
                        @else
                            <div class="text-3xl font-bold text-gray-900 mb-2">
                                Rp {{ number_format($course->price, 0, ',', '.') }}
                            </div>
                        @endif
                    </div>
                    
                    <button class="w-full btn bg-teal-600 hover:bg-teal-700 text-white mb-3" disabled>
                        {{ $course->is_free ? 'Daftar Gratis' : 'Beli Sekarang' }}
                    </button>
                    
                    <p class="text-xs text-gray-500 text-center">
                        <em>Tombol tidak aktif dalam mode preview</em>
                    </p>
                </div>

                <!-- Instructor Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Instruktur</h3>
                    <div class="flex items-center space-x-3">
                        @if($course->tutor->profile_picture)
                            <img src="{{ asset('storage/' . $course->tutor->profile_picture) }}" 
                                 alt="{{ $course->tutor->name }}" 
                                 class="w-12 h-12 rounded-full object-cover">
                        @else
                            <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        @endif
                        <div>
                            <h4 class="font-medium text-gray-900">{{ $course->tutor->name }}</h4>
                            <p class="text-sm text-gray-600">Tutor</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
