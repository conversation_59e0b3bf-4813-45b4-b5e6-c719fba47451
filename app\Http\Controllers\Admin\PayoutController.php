<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\PayoutRequest;
use App\Models\User;

class PayoutController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'is.admin']);
    }

    /**
     * Display a listing of payout requests.
     */
    public function index(Request $request)
    {
        $query = PayoutRequest::with(['tutor'])
            ->orderBy('requested_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('requested_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('requested_at', '<=', $request->date_to);
        }

        // Search by tutor name or request ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('request_id', 'like', "%{$search}%")
                  ->orWhereHas('tutor', function($tutorQuery) use ($search) {
                      $tutorQuery->where('name', 'like', "%{$search}%")
                                 ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $payoutRequests = $query->paginate(20);

        // Get statistics
        $stats = [
            'total' => PayoutRequest::count(),
            'pending' => PayoutRequest::where('status', 'pending')->count(),
            'processing' => PayoutRequest::where('status', 'processing')->count(),
            'approved' => PayoutRequest::where('status', 'approved')->count(),
            'paid' => PayoutRequest::where('status', 'paid')->count(),
            'rejected' => PayoutRequest::where('status', 'rejected')->count(),
            'total_amount_pending' => PayoutRequest::whereIn('status', ['pending', 'processing', 'approved'])->sum('amount'),
            'total_amount_paid' => PayoutRequest::where('status', 'paid')->sum('amount'),
        ];

        return view('admin.payouts.index', compact('payoutRequests', 'stats'));
    }

    /**
     * Display the specified payout request.
     */
    public function show(PayoutRequest $payoutRequest)
    {
        $payoutRequest->load(['tutor', 'processedBy']);
        
        return view('admin.payouts.show', compact('payoutRequest'));
    }

    /**
     * Update the status of a payout request.
     */
    public function updateStatus(Request $request, PayoutRequest $payoutRequest)
    {
        $request->validate([
            'status' => 'required|in:processing,approved,paid,rejected,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $admin = Auth::user();
            $status = $request->status;
            $notes = $request->notes;

            switch ($status) {
                case 'processing':
                    $payoutRequest->update([
                        'status' => 'processing',
                        'processed_at' => now(),
                        'processed_by' => $admin->id,
                        'notes' => $notes,
                    ]);
                    break;

                case 'approved':
                    $payoutRequest->markAsApproved($notes, $admin->id);
                    break;

                case 'paid':
                    $payoutRequest->markAsPaid($notes);
                    break;

                case 'rejected':
                    $payoutRequest->markAsRejected($notes ?: 'Permintaan payout ditolak oleh admin.', $admin->id);
                    break;

                case 'cancelled':
                    $payoutRequest->markAsCancelled($notes ?: 'Permintaan payout dibatalkan oleh admin.');
                    break;
            }

            DB::commit();

            // Log the action
            Log::info('Payout request status updated by admin', [
                'admin_id' => $admin->id,
                'admin_name' => $admin->name,
                'payout_request_id' => $payoutRequest->id,
                'request_id' => $payoutRequest->request_id,
                'old_status' => $payoutRequest->getOriginal('status'),
                'new_status' => $status,
                'notes' => $notes,
            ]);

            $statusMessages = [
                'processing' => 'Permintaan payout berhasil diubah ke status processing.',
                'approved' => 'Permintaan payout berhasil disetujui.',
                'paid' => 'Permintaan payout berhasil ditandai sebagai dibayar.',
                'rejected' => 'Permintaan payout berhasil ditolak.',
                'cancelled' => 'Permintaan payout berhasil dibatalkan.',
            ];

            return response()->json([
                'success' => true,
                'message' => $statusMessages[$status],
                'payout_request' => [
                    'id' => $payoutRequest->id,
                    'status' => $payoutRequest->status,
                    'status_name' => $payoutRequest->status_name,
                    'processed_at' => $payoutRequest->processed_at ? $payoutRequest->processed_at->format('d M Y H:i') : null,
                    'notes' => $payoutRequest->notes,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Error updating payout request status: ' . $e->getMessage(), [
                'admin_id' => Auth::id(),
                'payout_request_id' => $payoutRequest->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui status payout. Silakan coba lagi.'
            ], 500);
        }
    }

    /**
     * Get payout request data for AJAX.
     */
    public function getData(PayoutRequest $payoutRequest)
    {
        $payoutRequest->load(['tutor', 'processedBy']);

        return response()->json([
            'success' => true,
            'payout_request' => [
                'id' => $payoutRequest->id,
                'request_id' => $payoutRequest->request_id,
                'tutor_name' => $payoutRequest->tutor->name,
                'tutor_email' => $payoutRequest->tutor->email,
                'amount' => $payoutRequest->formatted_amount,
                'net_amount' => $payoutRequest->formatted_net_amount,
                'platform_fee' => 'IDR ' . number_format($payoutRequest->platform_fee, 0, ',', '.'),
                'status' => $payoutRequest->status,
                'status_name' => $payoutRequest->status_name,
                'payment_method' => $payoutRequest->payment_method_name,
                'payment_details' => $payoutRequest->payment_details,
                'tutor_notes' => $payoutRequest->tutor_notes,
                'admin_notes' => $payoutRequest->notes,
                'requested_at' => $payoutRequest->requested_at->format('d M Y H:i'),
                'processed_at' => $payoutRequest->processed_at ? $payoutRequest->processed_at->format('d M Y H:i') : null,
                'processed_by' => $payoutRequest->processedBy ? $payoutRequest->processedBy->name : null,
            ]
        ]);
    }

    /**
     * Export payout requests to CSV.
     */
    public function export(Request $request)
    {
        $query = PayoutRequest::with(['tutor']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('requested_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('requested_at', '<=', $request->date_to);
        }

        $payoutRequests = $query->orderBy('requested_at', 'desc')->get();

        $filename = 'payout_requests_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($payoutRequests) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Request ID',
                'Tutor Name',
                'Tutor Email',
                'Amount',
                'Platform Fee',
                'Net Amount',
                'Status',
                'Payment Method',
                'Requested At',
                'Processed At',
                'Notes'
            ]);

            // CSV data
            foreach ($payoutRequests as $payout) {
                fputcsv($file, [
                    $payout->request_id,
                    $payout->tutor->name,
                    $payout->tutor->email,
                    $payout->amount,
                    $payout->platform_fee,
                    $payout->net_amount,
                    $payout->status_name,
                    $payout->payment_method_name,
                    $payout->requested_at->format('Y-m-d H:i:s'),
                    $payout->processed_at ? $payout->processed_at->format('Y-m-d H:i:s') : '',
                    $payout->notes ?: ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
