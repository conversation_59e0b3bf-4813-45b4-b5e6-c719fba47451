@extends('layouts.app')

@section('title', '<PERSON><PERSON>')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary/5 via-white to-secondary/5 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Tutor <PERSON>rb<PERSON> di <span class="text-primary">Ngambiskuy</span>
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Belajar dari para ahli terpercaya dengan pengalaman industri yang luas. 
                Temukan tutor yang tepat untuk mencapai tujuan belajar Anda.
            </p>
            
            <!-- Platform Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="text-3xl font-bold text-primary mb-2">{{ number_format($platformStats['total_tutors']) }}</div>
                    <div class="text-gray-600 text-sm">Tutor Berpengalaman</div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="text-3xl font-bold text-primary mb-2">{{ number_format($platformStats['total_courses']) }}</div>
                    <div class="text-gray-600 text-sm">Kursus Tersedia</div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="text-3xl font-bold text-primary mb-2">{{ number_format($platformStats['total_students']) }}+</div>
                    <div class="text-gray-600 text-sm">Siswa Terdaftar</div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-100">
                    <div class="text-3xl font-bold text-primary mb-2">{{ $platformStats['average_rating'] }}</div>
                    <div class="text-gray-600 text-sm">Rating Rata-rata</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="bg-white py-8 border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <form method="GET" action="{{ route('tutors.index') }}" class="space-y-4">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Input -->
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" 
                               name="search" 
                               value="{{ request('search') }}"
                               placeholder="Cari tutor berdasarkan nama, keahlian, atau lokasi..."
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary">
                    </div>
                </div>

                <!-- Education Level Filter -->
                <div class="lg:w-64">
                    <select name="education_level" 
                            class="block w-full py-3 px-3 border border-gray-300 bg-white rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="">Semua Pendidikan</option>
                        @foreach($educationLevels as $level)
                            <option value="{{ $level }}" {{ request('education_level') == $level ? 'selected' : '' }}>
                                {{ $level }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Sort Filter -->
                <div class="lg:w-64">
                    <select name="sort" 
                            class="block w-full py-3 px-3 border border-gray-300 bg-white rounded-lg shadow-sm focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="newest" {{ request('sort') == 'newest' ? 'selected' : '' }}>Terbaru</option>
                        <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Terpopuler</option>
                        <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Rating Tertinggi</option>
                        <option value="courses" {{ request('sort') == 'courses' ? 'selected' : '' }}>Terbanyak Kursus</option>
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Nama A-Z</option>
                    </select>
                </div>

                <!-- Search Button -->
                <button type="submit" 
                        class="lg:w-auto px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors">
                    <span class="flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Cari
                    </span>
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Tutors Grid -->
<section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($tutors->count() > 0)
            <!-- Results Header -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">
                        {{ $tutors->total() }} Tutor Ditemukan
                    </h2>
                    <p class="text-gray-600">
                        @if(request()->hasAny(['search', 'education_level']))
                            Hasil pencarian untuk filter yang dipilih
                        @else
                            Semua tutor terbaik tersedia di platform kami
                        @endif
                    </p>
                </div>
            </div>

            <!-- Tutors Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($tutors as $tutor)
                <div class="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 overflow-hidden group">
                    <!-- Tutor Header -->
                    <div class="p-6 text-center">
                        <!-- Profile Picture -->
                        <div class="relative mx-auto mb-4">
                            <div class="w-20 h-20 rounded-full overflow-hidden border-4 border-primary/20 mx-auto group-hover:border-primary/40 transition-colors">
                                <img src="{{ $tutor->user->getProfilePictureUrl() }}" 
                                     alt="{{ $tutor->public_name }}"
                                     class="w-full h-full object-cover">
                            </div>
                        </div>

                        <!-- Tutor Name -->
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $tutor->public_name }}</h3>
                        
                        <!-- Job Title & Company -->
                        @if($tutor->user->job_title || $tutor->user->company)
                        <p class="text-sm text-gray-600 mb-2">
                            @if($tutor->user->job_title)
                                {{ $tutor->user->job_title }}
                                @if($tutor->user->company) at {{ $tutor->user->company }} @endif
                            @else
                                {{ $tutor->user->company }}
                            @endif
                        </p>
                        @endif

                        <!-- Education Level -->
                        @if($tutor->education_level)
                        <span class="inline-block px-3 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full mb-3">
                            {{ $tutor->education_level }}
                        </span>
                        @endif

                        <!-- Description -->
                        @if($tutor->description)
                        <p class="text-sm text-gray-600 mb-4 line-clamp-3">{{ $tutor->description }}</p>
                        @endif
                    </div>

                    <!-- Statistics -->
                    <div class="px-6 pb-4">
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-lg font-bold text-primary">{{ $tutor->stats['total_courses'] }}</div>
                                <div class="text-xs text-gray-500">Kursus</div>
                            </div>
                            <div>
                                <div class="text-lg font-bold text-primary">{{ number_format($tutor->stats['total_students']) }}</div>
                                <div class="text-xs text-gray-500">Siswa</div>
                            </div>
                        </div>
                        
                        @if($tutor->stats['average_rating'] > 0)
                        <div class="flex items-center justify-center mt-3">
                            <div class="flex items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    <svg class="w-4 h-4 {{ $i <= $tutor->stats['average_rating'] ? 'text-yellow-400' : 'text-gray-300' }}" 
                                         fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                    </svg>
                                @endfor
                                <span class="ml-2 text-sm text-gray-600">{{ $tutor->stats['average_rating'] }}</span>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- View Profile Button -->
                    <div class="px-6 pb-6">
                        <a href="{{ route('tutor.public-profile', $tutor->public_name_slug) }}" 
                           class="block w-full text-center py-3 px-4 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors group-hover:bg-primary/90">
                            Lihat Profil
                        </a>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                {{ $tutors->links() }}
            </div>
        @else
            <!-- No Results -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">Tidak ada tutor ditemukan</h3>
                    <p class="text-gray-600 mb-6">
                        Coba ubah filter pencarian atau kata kunci untuk menemukan tutor yang sesuai.
                    </p>
                    <a href="{{ route('tutors.index') }}" 
                       class="inline-flex items-center px-4 py-2 bg-primary text-white font-medium rounded-lg hover:bg-primary/90 transition-colors">
                        Lihat Semua Tutor
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>
@endsection

@push('styles')
<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush

@push('scripts')
<script>
// Set tutors listing context for Nala AI
window.tutorsListingContext = {
    page_type: 'tutors_listing',
    total_tutors: {{ $tutors->total() }},
    current_filters: {
        search: '{{ request()->get('search', '') }}',
        education_level: '{{ request()->get('education_level', '') }}',
        sort: '{{ request()->get('sort', 'newest') }}'
    },
    platform_stats: {
        total_tutors: {{ $platformStats['total_tutors'] }},
        total_courses: {{ $platformStats['total_courses'] }},
        total_students: {{ $platformStats['total_students'] }},
        average_rating: {{ $platformStats['average_rating'] }}
    },
    featured_tutors: [
        @foreach($tutors->take(3) as $tutor)
        {
            name: '{{ $tutor->public_name }}',
            slug: '{{ $tutor->user->username }}',
            education_level: '{{ $tutor->education_level }}',
            courses_count: {{ $tutor->courses_count ?? 0 }},
            students_count: {{ $tutor->students_count ?? 0 }},
            rating: {{ $tutor->average_rating ?? 0 }}
        }{{ !$loop->last ? ',' : '' }}
        @endforeach
    ]
};

// Initialize Nala AI with tutors listing context
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Nala AI to be initialized first
    const initializeTutorsContext = () => {
        if (window.nalaAIAssistant) {
            // Set context for tutors listing page
            window.nalaAIAssistant.currentContext = 'tutors_listing';
            window.nalaAIAssistant.tutorsListingContext = window.tutorsListingContext;

            console.log('Nala AI: Tutors listing context set', window.tutorsListingContext);
        } else {
            // Retry after a short delay if Nala AI is not ready yet
            setTimeout(initializeTutorsContext, 100);
        }
    };

    initializeTutorsContext();
});
</script>
@endpush
