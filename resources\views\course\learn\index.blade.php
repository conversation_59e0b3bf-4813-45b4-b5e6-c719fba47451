@extends('layouts.app')

@section('title', 'Belajar: ' . $course->title . ' - Ngambiskuy')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/coursera-learning.css') }}">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
@endpush

@section('content')
<!-- Coursera-style Learning Interface -->
<div class="coursera-learning-layout">
    <!-- Top Navigation Bar -->
    <div class="top-nav-bar">
        <div class="nav-container">
            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb-nav">
                <a href="{{ route('home') }}" class="breadcrumb-link">Beranda</a>
                <svg class="breadcrumb-separator" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <a href="{{ route('course.show', $course) }}" class="breadcrumb-link">{{ $course->title }}</a>
                <svg class="breadcrumb-separator" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <span class="breadcrumb-current">Belajar</span>
            </nav>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" aria-label="Open course navigation">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                </svg>
            </button>

            <!-- Navigation Controls -->
            <div class="nav-controls">
                <button class="nav-btn" id="prevBtn" disabled>
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                    Previous
                </button>
                <button class="nav-btn primary" id="nextBtn">
                    Next
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Layout Container -->
    <div class="main-layout">
        <!-- Fixed Left Sidebar -->
        <aside class="course-sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <button class="menu-toggle" id="menuToggle">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                    Hide menu
                </button>
            </div>

            <!-- Course Navigation -->
            <div class="course-navigation">
                <div class="course-info-compact">
                    <h2 class="course-title-compact">{{ $course->title }}</h2>
                    <div class="progress-indicator">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ $progressPercentage }}%"></div>
                        </div>
                        <span class="progress-text">{{ round($progressPercentage) }}% selesai</span>
                    </div>
                </div>

                <!-- Course Curriculum -->
                <div class="curriculum-list">
                    @foreach($course->chapters as $chapterIndex => $chapter)
                        <div class="curriculum-section">
                            <div class="section-header">
                                <h3 class="section-title">{{ $chapter->title }}</h3>
                                <span class="section-meta">{{ $chapter->lessons->count() }} items</span>
                            </div>

                            <div class="section-items">
                                @foreach($chapter->lessons as $lessonIndex => $lesson)
                                    @php
                                        $isCompleted = $userProgress->where('lesson_id', $lesson->id)->where('status', 'completed')->isNotEmpty();
                                        $isInProgress = $userProgress->where('lesson_id', $lesson->id)->where('status', 'in_progress')->isNotEmpty();
                                        $isCurrentLesson = request()->route('lesson') && request()->route('lesson')->id === $lesson->id;
                                    @endphp
                                    <a href="{{ route('course.lesson', [$course, $lesson]) }}"
                                       class="curriculum-item {{ $isCompleted ? 'completed' : ($isInProgress ? 'in-progress' : '') }} {{ $isCurrentLesson ? 'current' : '' }}"
                                       data-lesson-id="{{ $lesson->id }}"
                                       data-lesson-type="{{ $lesson->type }}"
                                       title="{{ $lesson->title }}"
                                       aria-label="Lesson: {{ $lesson->title }} - {{ $isCompleted ? 'Completed' : ($isInProgress ? 'In Progress' : 'Not Started') }}">
                                        <div class="item-status">
                                            @if($isCompleted)
                                                <svg class="status-check" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                                </svg>
                                            @else
                                                <div class="status-dot"></div>
                                            @endif
                                        </div>
                                        <div class="item-content">
                                            <div class="item-type">
                                                @if($lesson->type === 'video')
                                                    📹 Video
                                                @elseif($lesson->type === 'text')
                                                    📖 Reading
                                                @elseif($lesson->type === 'quiz')
                                                    🧠 Quiz
                                                @else
                                                    📝 Lesson
                                                @endif
                                                @if($lesson->duration)
                                                    • {{ $lesson->duration }} min
                                                @endif
                                            </div>
                                            <div class="item-title">{{ $lesson->title }}</div>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="content-area">
            <!-- Content Header -->
            <div class="content-header">
                <div class="lesson-navigation">
                    <h1 class="lesson-title">{{ $course->title }}</h1>
                    <div class="lesson-meta">
                        <span class="instructor-name">{{ $course->tutor->name }}</span>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Video/Content Player Area -->
                <div class="content-player">
                    @if($nextLesson)
                        <!-- Preview Content -->
                        <div class="preview-content" data-lesson-id="{{ $nextLesson->id }}">
                            <div class="preview-image">
                                <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/course-placeholder.svg') }}"
                                     alt="{{ $course->title }}"
                                     class="course-image"
                                     loading="lazy"
                                     onerror="this.src='{{ asset('images/course-placeholder.svg') }}'">
                                <div class="play-overlay">
                                    <div class="play-button">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                    </div>
                                    <div class="play-text">Mulai Belajar</div>
                                </div>
                            </div>
                            <div class="preview-info">
                                <div class="lesson-badge">
                                    @if($nextLesson->type === 'video')
                                        📹 Video Lesson
                                    @elseif($nextLesson->type === 'text')
                                        📖 Reading Material
                                    @elseif($nextLesson->type === 'quiz')
                                        🧠 Quiz
                                    @else
                                        📝 Lesson
                                    @endif
                                </div>
                                <h2 class="preview-title">{{ $nextLesson->title }}</h2>
                                <p class="preview-description">
                                    Mulai belajar dengan materi pertama dari kursus ini.
                                    @if($nextLesson->duration)
                                        Estimasi waktu: {{ $nextLesson->duration }} menit.
                                    @endif
                                </p>
                                <div class="preview-meta">
                                    <span class="lesson-number">Lesson {{ $loop->iteration ?? 1 }}</span>
                                    @if($nextLesson->duration)
                                        <span class="lesson-duration">⏱️ {{ $nextLesson->duration }} min</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Course Completed -->
                        <div class="completion-content">
                            <div class="completion-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                </svg>
                            </div>
                            <h2 class="completion-title">Selamat! Kursus Selesai</h2>
                            <p class="completion-description">
                                Anda telah menyelesaikan semua materi dalam kursus ini.
                            </p>
                        </div>
                    @endif
                </div>

                <!-- Content Tabs -->
                <div class="content-tabs">
                    <div class="tab-navigation">
                        <button class="tab-btn active" data-tab="overview">Overview</button>
                        <button class="tab-btn" data-tab="notes">Notes</button>
                        <button class="tab-btn" data-tab="downloads">Downloads</button>
                        <button class="tab-btn" data-tab="discuss">Discuss</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Overview Tab -->
                        <div class="tab-pane active" id="overview">
                            <div class="overview-content">
                                <h3 class="content-title">Tentang Kursus Ini</h3>
                                <div class="course-description">
                                    <p>{{ $course->description ?? 'Deskripsi kursus akan ditampilkan di sini.' }}</p>
                                </div>

                                <div class="course-stats">
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                            </svg>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value">{{ $course->level }}</div>
                                            <div class="stat-label">Level</div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                            </svg>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value">{{ $totalLessons }}</div>
                                            <div class="stat-label">Total Materi</div>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                                                <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
                                            </svg>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value">{{ $course->duration }} jam</div>
                                            <div class="stat-label">Durasi</div>
                                        </div>
                                    </div>
                                </div>

                                @if($nextLesson)
                                    <div class="start-learning">
                                        <a href="{{ route('course.lesson', [$course, $nextLesson]) }}" class="start-btn">
                                            Mulai Belajar
                                            <svg viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Notes Tab -->
                        <div class="tab-pane" id="notes">
                            <div class="notes-content">
                                <h3 class="content-title">Catatan Anda</h3>
                                <p class="empty-state">Belum ada catatan. Mulai belajar untuk membuat catatan.</p>
                            </div>
                        </div>

                        <!-- Downloads Tab -->
                        <div class="tab-pane" id="downloads">
                            <div class="downloads-content">
                                <h3 class="content-title">Materi Download</h3>
                                <p class="empty-state">Tidak ada materi download tersedia.</p>
                            </div>
                        </div>

                        <!-- Discuss Tab -->
                        <div class="tab-pane" id="discuss">
                            <div class="discuss-content">
                                <h3 class="content-title">Diskusi</h3>
                                <p class="empty-state">Belum ada diskusi. Mulai diskusi dengan sesama peserta.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="mobile-sidebar-overlay"></div>

    <!-- Mobile Sidebar -->
    <div class="mobile-sidebar">
        <div class="mobile-sidebar-header">
            <h3 class="mobile-sidebar-title">Course Navigation</h3>
            <button class="mobile-sidebar-close" aria-label="Close navigation">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        </div>

        <!-- Mobile Course Navigation (cloned from desktop) -->
        <div class="course-navigation">
            <div class="course-info-compact">
                <h2 class="course-title-compact">{{ $course->title }}</h2>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ $progressPercentage }}%"></div>
                    </div>
                    <span class="progress-text">{{ round($progressPercentage) }}% complete</span>
                </div>
            </div>

            <!-- Mobile Curriculum -->
            <div class="curriculum-list">
                @foreach($course->chapters as $chapterIndex => $chapter)
                    <div class="curriculum-section">
                        <div class="section-header">
                            <h3 class="section-title">{{ $chapter->title }}</h3>
                            <span class="section-meta">{{ $chapter->lessons->count() }} items</span>
                        </div>

                        <div class="section-items">
                            @foreach($chapter->lessons as $lessonIndex => $lesson)
                                @php
                                    $isCompleted = $userProgress->where('lesson_id', $lesson->id)->where('status', 'completed')->isNotEmpty();
                                    $isInProgress = $userProgress->where('lesson_id', $lesson->id)->where('status', 'in_progress')->isNotEmpty();
                                    $isCurrentLesson = request()->route('lesson') && request()->route('lesson')->id === $lesson->id;
                                @endphp
                                <a href="{{ route('course.lesson', [$course, $lesson]) }}"
                                   class="curriculum-item {{ $isCompleted ? 'completed' : ($isInProgress ? 'in-progress' : '') }} {{ $isCurrentLesson ? 'current' : '' }}"
                                   data-lesson-id="{{ $lesson->id }}"
                                   onclick="closeMobileSidebar()">
                                    <div class="item-status">
                                        @if($isCompleted)
                                            <svg class="status-check" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                            </svg>
                                        @else
                                            <div class="status-dot"></div>
                                        @endif
                                    </div>
                                    <div class="item-content">
                                        <div class="item-type">
                                            @if($lesson->type === 'video')
                                                📹 Video
                                            @elseif($lesson->type === 'text')
                                                📖 Reading
                                            @elseif($lesson->type === 'quiz')
                                                🧠 Quiz
                                            @else
                                                📝 Lesson
                                            @endif
                                            @if($lesson->duration)
                                                • {{ $lesson->duration }} min
                                            @endif
                                        </div>
                                        <div class="item-title">{{ $lesson->title }}</div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/coursera-learning.js') }}"></script>
@endpush
