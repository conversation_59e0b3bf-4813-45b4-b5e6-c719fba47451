<?php $__env->startSection('title', 'Pengaturan - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Pengaturan</h1>
                <p class="text-gray-600 mt-1">Kelola preferensi akun dan pengaturan aplikasi Anda</p>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <form action="<?php echo e(route('user.settings.update')); ?>" method="POST" class="space-y-6">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <!-- Account Settings -->
        

        <?php if($hasLearningPreferences || $hasMinatBelajar): ?>
        <!-- Learning Preferences -->
        <div class="bg-white rounded-xl shadow-sm p-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Preferensi Belajar</h2>

            <div class="space-y-6">
                <?php if($hasLearningPreferences): ?>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Gaya Belajar</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <?php
                            $learningStyles = ['visual', 'auditory', 'kinesthetic', 'reading'];
                            $userLearningPrefs = $user->learning_preferences ?? [];
                        ?>
                        <?php $__currentLoopData = $learningStyles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $style): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="learning_preferences[]" value="<?php echo e($style); ?>" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   <?php echo e(in_array($style, $userLearningPrefs) ? 'checked' : ''); ?>>
                            <span class="ml-2 text-sm text-gray-700"><?php echo e(ucfirst($style)); ?></span>
                        </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($hasMinatBelajar): ?>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Minat Pembelajaran</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        <?php
                            $interests = ['programming', 'design', 'ai', 'data_science', 'mobile_dev', 'business', 'marketing', 'finance'];
                            $userInterests = $user->minat_belajar ?? [];
                        ?>
                        <?php $__currentLoopData = $interests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $interest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="minat_belajar[]" value="<?php echo e($interest); ?>" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   <?php echo e(in_array($interest, $userInterests) ? 'checked' : ''); ?>>
                            <span class="ml-2 text-sm text-gray-700">
                                <?php switch($interest):
                                    case ('programming'): ?> Programming <?php break; ?>
                                    <?php case ('design'): ?> Design <?php break; ?>
                                    <?php case ('ai'): ?> AI/ML <?php break; ?>
                                    <?php case ('data_science'): ?> Data Science <?php break; ?>
                                    <?php case ('mobile_dev'): ?> Mobile Dev <?php break; ?>
                                    <?php case ('business'): ?> Business <?php break; ?>
                                    <?php case ('marketing'): ?> Marketing <?php break; ?>
                                    <?php case ('finance'): ?> Finance <?php break; ?>
                                    <?php default: ?> <?php echo e(ucfirst($interest)); ?>

                                <?php endswitch; ?>
                            </span>
                        </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if($hasWorkPreferences || $hasIndustryInterests): ?>
        <!-- Career Preferences -->
        <div class="bg-white rounded-xl shadow-sm p-8">
            <h2 class="text-xl font-bold text-gray-900 mb-6">Preferensi Karir</h2>

            <div class="space-y-6">
                <?php if($hasWorkPreferences): ?>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Preferensi Kerja</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <?php
                            $workPrefs = ['remote', 'hybrid', 'onsite', 'freelance', 'full_time', 'part_time'];
                            $userWorkPrefs = $user->work_preferences ?? [];
                        ?>
                        <?php $__currentLoopData = $workPrefs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pref): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="work_preferences[]" value="<?php echo e($pref); ?>" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   <?php echo e(in_array($pref, $userWorkPrefs) ? 'checked' : ''); ?>>
                            <span class="ml-2 text-sm text-gray-700">
                                <?php switch($pref):
                                    case ('remote'): ?> Remote <?php break; ?>
                                    <?php case ('hybrid'): ?> Hybrid <?php break; ?>
                                    <?php case ('onsite'): ?> Onsite <?php break; ?>
                                    <?php case ('freelance'): ?> Freelance <?php break; ?>
                                    <?php case ('full_time'): ?> Full Time <?php break; ?>
                                    <?php case ('part_time'): ?> Part Time <?php break; ?>
                                    <?php default: ?> <?php echo e(ucfirst($pref)); ?>

                                <?php endswitch; ?>
                            </span>
                        </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <?php if($hasIndustryInterests): ?>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Minat Industri</label>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                        <?php
                            $industries = ['technology', 'finance', 'healthcare', 'education', 'ecommerce', 'gaming', 'startup', 'consulting'];
                            $userIndustries = $user->industry_interests ?? [];
                        ?>
                        <?php $__currentLoopData = $industries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $industry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <label class="flex items-center">
                            <input type="checkbox" name="industry_interests[]" value="<?php echo e($industry); ?>" 
                                   class="rounded border-gray-300 text-primary focus:ring-primary" 
                                   <?php echo e(in_array($industry, $userIndustries) ? 'checked' : ''); ?>>
                            <span class="ml-2 text-sm text-gray-700">
                                <?php switch($industry):
                                    case ('technology'): ?> Technology <?php break; ?>
                                    <?php case ('finance'): ?> Finance <?php break; ?>
                                    <?php case ('healthcare'): ?> Healthcare <?php break; ?>
                                    <?php case ('education'): ?> Education <?php break; ?>
                                    <?php case ('ecommerce'): ?> E-commerce <?php break; ?>
                                    <?php case ('gaming'): ?> Gaming <?php break; ?>
                                    <?php case ('startup'): ?> Startup <?php break; ?>
                                    <?php case ('consulting'): ?> Consulting <?php break; ?>
                                    <?php default: ?> <?php echo e(ucfirst($industry)); ?>

                                <?php endswitch; ?>
                            </span>
                        </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="btn btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Simpan Pengaturan
            </button>
        </div>
    </form>
</div>

<script>
// Auto-save functionality and real-time updates
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[type="checkbox"]');
    
    // Add change listeners to all inputs for real-time feedback
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual feedback when settings change
            const saveButton = document.querySelector('button[type="submit"]');
            saveButton.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
            saveButton.classList.remove('btn-primary');
            saveButton.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>Simpan Perubahan';
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/user/settings.blade.php ENDPATH**/ ?>