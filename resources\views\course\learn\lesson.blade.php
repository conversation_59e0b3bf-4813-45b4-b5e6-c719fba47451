@extends('layouts.app')

@section('title', $lesson->title . ' - ' . $course->title . ' - Ngambiskuy')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/coursera-learning.css') }}">
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
<style>
/* Additional lesson-specific styles to complement coursera-learning.css */
.lesson-description {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    background: var(--background-white);
}

.description-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.description-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

.lesson-progress-section {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    background: var(--background-white);
}

.progress-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.progress-info {
    flex: 1;
}

.progress-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.progress-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
}

.progress-actions {
    flex-shrink: 0;
}

.completion-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--success-light);
    color: var(--success-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
}

.completion-icon {
    width: 16px;
    height: 16px;
}

.complete-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.complete-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.complete-btn-icon {
    width: 16px;
    height: 16px;
}

/* Lesson Navigation Section */
.lesson-navigation-section {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    background: var(--background-white);
}

.navigation-content {
    max-width: 100%;
}

.navigation-buttons {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    align-items: stretch;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
    background: var(--background-white);
    color: var(--text-primary);
    flex: 1;
    max-width: 300px;
    min-height: 80px;
}

.nav-btn:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-btn-previous {
    justify-content: flex-start;
}

.nav-btn-next {
    justify-content: flex-end;
    margin-left: auto;
}

.nav-btn-icon {
    width: 20px;
    height: 20px;
    color: var(--text-secondary);
    transition: var(--transition-fast);
}

.nav-btn:hover .nav-btn-icon {
    color: var(--primary-color);
}

.nav-btn-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    text-align: left;
}

.nav-btn-next .nav-btn-content {
    text-align: right;
}

.nav-btn-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-btn-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .progress-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .lesson-description,
    .lesson-progress-section,
    .lesson-navigation-section {
        padding: 16px;
    }

    .navigation-buttons {
        flex-direction: column;
        gap: 12px;
    }

    .nav-btn {
        max-width: none;
        min-height: 60px;
    }

    .nav-btn-next {
        margin-left: 0;
    }

    .nav-btn-content {
        text-align: left;
    }

    .nav-btn-next .nav-btn-content {
        text-align: left;
    }
}
</style>
@endpush

@section('content')
@php
    // Calculate progress for both desktop and mobile sidebars
    $totalLessons = $course->chapters->flatMap->lessons->count();
    $completedLessons = $course->chapters->flatMap->lessons->filter(function($l) {
        return $l->progress->where('user_id', auth()->id())->where('status', 'completed')->isNotEmpty();
    })->count();
    $progressPercentage = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;
@endphp

<!-- Coursera-style Learning Interface -->
<div class="coursera-learning-layout">
    <!-- Top Navigation Bar -->
    <div class="top-nav-bar">
        <div class="nav-container">
            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb-nav">
                <a href="{{ route('home') }}" class="breadcrumb-link">Beranda</a>
                <svg class="breadcrumb-separator" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <a href="{{ route('course.show', $course) }}" class="breadcrumb-link">{{ $course->title }}</a>
                <svg class="breadcrumb-separator" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <a href="{{ route('course.learn', $course) }}" class="breadcrumb-link">Belajar</a>
                <svg class="breadcrumb-separator" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
                </svg>
                <span class="breadcrumb-current">{{ $lesson->title }}</span>
            </nav>

            <!-- Navigation Actions -->
            <div class="nav-actions">
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Layout Container -->
    <div class="main-layout">
        <!-- Fixed Left Sidebar -->
        <aside class="course-sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <button class="menu-toggle" id="menuToggle">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                    Hide menu
                </button>
            </div>

            <!-- Course Navigation -->
            <div class="course-navigation">
                <div class="course-info-compact">
                    <h2 class="course-title-compact">{{ $course->title }}</h2>
                    <div class="progress-indicator">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ $progressPercentage }}%"></div>
                        </div>
                        <span class="progress-text">{{ round($progressPercentage) }}% complete</span>
                    </div>
                </div>

                <!-- Course Curriculum -->
                <div class="curriculum-list">
                    @foreach($course->chapters as $chapterIndex => $chapter)
                        <div class="curriculum-section">
                            <div class="section-header">
                                <h3 class="section-title">{{ $chapter->title }}</h3>
                                <span class="section-meta">{{ $chapter->lessons->count() }} items</span>
                            </div>

                            <div class="section-items">
                                @foreach($chapter->lessons as $lessonIndex => $chapterLesson)
                                    @php
                                        $lessonProgress = $chapterLesson->progress->where('user_id', auth()->id())->first();
                                        $isCompleted = $lessonProgress && $lessonProgress->status === 'completed';
                                        $isInProgress = $lessonProgress && $lessonProgress->status === 'in_progress';
                                        $isCurrentLesson = $chapterLesson->id === $lesson->id;
                                    @endphp
                                    <a href="{{ route('course.lesson', [$course, $chapterLesson]) }}"
                                       class="curriculum-item {{ $isCompleted ? 'completed' : ($isInProgress ? 'in-progress' : '') }} {{ $isCurrentLesson ? 'current' : '' }}"
                                       data-lesson-id="{{ $chapterLesson->id }}"
                                       data-lesson-type="{{ $chapterLesson->type }}"
                                       title="{{ $chapterLesson->title }}"
                                       aria-label="Lesson: {{ $chapterLesson->title }} - {{ $isCompleted ? 'Completed' : ($isInProgress ? 'In Progress' : 'Not Started') }}">
                                        <div class="item-status">
                                            @if($isCompleted)
                                                <svg class="status-check" viewBox="0 0 24 24" fill="currentColor">
                                                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                                </svg>
                                            @else
                                                <div class="status-dot"></div>
                                            @endif
                                        </div>
                                        <div class="item-content">
                                            <div class="item-type">
                                                @if($chapterLesson->type === 'video')
                                                    📹 Video
                                                @elseif($chapterLesson->type === 'text')
                                                    📖 Reading
                                                @elseif($chapterLesson->type === 'quiz')
                                                    🧠 Quiz
                                                @else
                                                    📝 Lesson
                                                @endif
                                                @if($chapterLesson->duration)
                                                    • {{ $chapterLesson->duration }} min
                                                @endif
                                            </div>
                                            <div class="item-title">{{ $chapterLesson->title }}</div>
                                        </div>
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="content-area">
            <!-- Content Header -->
            <div class="content-header">
                <div class="lesson-navigation">
                    <h1 class="lesson-title">{{ $lesson->title }}</h1>
                    <div class="lesson-meta">
                        <span class="instructor-name">{{ $course->tutor->name }}</span>
                        <span class="lesson-chapter">{{ $lesson->chapter->title }}</span>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <!-- Lesson Content Player Area -->
                <div class="content-player">
                @if($lesson->type === 'video')
                    <!-- Video Content -->
                    <div class="aspect-video bg-black">
                        @if($lesson->video_url)
                            <!-- External Video (YouTube, Vimeo, etc.) -->
                            <iframe
                                src="{{ $lesson->video_url }}"
                                class="w-full h-full"
                                frameborder="0"
                                allowfullscreen>
                            </iframe>
                        @elseif($lesson->video_file)
                            <!-- Uploaded Video -->
                            <video
                                class="w-full h-full"
                                controls
                                preload="metadata"
                                id="lesson-video">
                                <source src="{{ $lesson->secure_video_url }}" type="video/mp4">
                                Browser Anda tidak mendukung pemutar video.
                            </video>
                        @else
                            <div class="flex items-center justify-center h-full text-white">
                                <div class="text-center">
                                    <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    <p class="opacity-75">Video tidak tersedia</p>
                                </div>
                            </div>
                        @endif
                    </div>
                @elseif($lesson->type === 'text')
                    <!-- Text Content -->
                    <div class="p-6">
                        <div class="prose max-w-none">
                            {!! $lesson->content ?: '<p>Konten teks tidak tersedia.</p>' !!}
                        </div>
                    </div>
                @elseif($lesson->type === 'quiz')
                    <!-- Quiz Content -->
                    <div class="p-6">
                        @if($lesson->quiz)
                            <div class="mb-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->quiz->title }}</h2>
                                @if($lesson->quiz->description)
                                    <p class="text-gray-600 mb-4">{{ $lesson->quiz->description }}</p>
                                @endif

                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <h3 class="font-semibold text-blue-900 mb-2">Informasi Kuis</h3>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="text-blue-700">Jumlah Soal:</span>
                                            <div class="font-semibold">{{ $lesson->quiz->total_questions }}</div>
                                        </div>
                                        <div>
                                            <span class="text-blue-700">Total Poin:</span>
                                            <div class="font-semibold">{{ $lesson->quiz->total_points }}</div>
                                        </div>
                                        @if($lesson->quiz->time_limit)
                                            <div>
                                                <span class="text-blue-700">Waktu:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->time_limit }} menit</div>
                                            </div>
                                        @endif
                                        @if($lesson->quiz->passing_score)
                                            <div>
                                                <span class="text-blue-700">Nilai Lulus:</span>
                                                <div class="font-semibold">{{ $lesson->quiz->passing_score }}%</div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="text-center">
                                <button class="btn btn-primary btn-lg" onclick="startQuiz()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Mulai Kuis
                                </button>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <p class="text-gray-500">Kuis tidak tersedia.</p>
                            </div>
                        @endif
                    </div>
                @elseif($lesson->type === 'assignment')
                    <!-- Assignment Content -->
                    <div class="p-6">
                        @if($lesson->assignment)
                            <div class="mb-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ $lesson->assignment->title }}</h2>
                                @if($lesson->assignment->description)
                                    <div class="prose max-w-none mb-6">
                                        {!! $lesson->assignment->description !!}
                                    </div>
                                @endif

                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                                    <h3 class="font-semibold text-orange-900 mb-2">Informasi Tugas</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                        <div>
                                            <span class="text-orange-700">Maksimal Poin:</span>
                                            <div class="font-semibold">{{ $lesson->assignment->max_points }}</div>
                                        </div>
                                        @if($lesson->assignment->due_date)
                                            <div>
                                                <span class="text-orange-700">Deadline:</span>
                                                <div class="font-semibold">{{ $lesson->assignment->formatted_due_date }}</div>
                                            </div>
                                        @endif
                                        <div>
                                            <span class="text-orange-700">File Maksimal:</span>
                                            <div class="font-semibold">{{ $lesson->assignment->max_files }} file</div>
                                        </div>
                                    </div>
                                </div>

                                @if($lesson->assignment->requirements)
                                    <div class="mb-6">
                                        <h3 class="font-semibold text-gray-900 mb-2">Persyaratan Tugas</h3>
                                        <div class="prose max-w-none">
                                            {!! $lesson->assignment->requirements !!}
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="text-center">
                                <button class="btn btn-primary btn-lg" onclick="startAssignment()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Mulai Mengerjakan
                                </button>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <p class="text-gray-500">Tugas tidak tersedia.</p>
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Lesson Description -->
                @if($lesson->description)
                    <div class="lesson-description">
                        <h3 class="description-title">Deskripsi Materi</h3>
                        <div class="description-content">
                            <p>{{ $lesson->description }}</p>
                        </div>
                    </div>
                @endif

                <!-- Mark as Complete -->
                <div class="lesson-progress-section">
                    <div class="progress-content">
                        <div class="progress-info">
                            <h3 class="progress-title">Progress Materi</h3>
                            <p class="progress-subtitle">Tandai sebagai selesai setelah Anda menyelesaikan materi ini</p>
                        </div>
                        <div class="progress-actions">
                            @if($progress->status === 'completed')
                                <span class="completion-badge">
                                    <svg class="completion-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Selesai
                                </span>
                            @else
                                <button onclick="markAsComplete()" class="complete-btn" id="complete-btn">
                                    <svg class="complete-btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Tandai Selesai
                                </button>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Lesson Navigation -->
                <div class="lesson-navigation-section">
                    <div class="navigation-content">
                        <div class="navigation-buttons">
                            @if($navigation['previous'])
                                <a href="{{ route('course.lesson', [$course, $navigation['previous']]) }}" class="nav-btn nav-btn-previous">
                                    <svg class="nav-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                                    </svg>
                                    <div class="nav-btn-content">
                                        <span class="nav-btn-label">Sebelumnya</span>
                                        <span class="nav-btn-title">{{ $navigation['previous']->title }}</span>
                                    </div>
                                </a>
                            @endif

                            @if($navigation['next'])
                                <a href="{{ route('course.lesson', [$course, $navigation['next']]) }}" class="nav-btn nav-btn-next">
                                    <div class="nav-btn-content">
                                        <span class="nav-btn-label">Selanjutnya</span>
                                        <span class="nav-btn-title">{{ $navigation['next']->title }}</span>
                                    </div>
                                    <svg class="nav-btn-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                                    </svg>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div class="mobile-sidebar-overlay" id="mobileSidebarOverlay"></div>

    <!-- Mobile Course Navigation -->
    <div class="mobile-sidebar" id="mobileSidebar">
        <!-- Mobile Sidebar Header -->
        <div class="mobile-sidebar-header">
            <h3 class="mobile-sidebar-title">Course Navigation</h3>
            <button class="mobile-sidebar-close" aria-label="Close navigation">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        </div>

        <!-- Mobile Course Navigation (cloned from desktop) -->
        <div class="course-navigation">
            <div class="course-info-compact">
                <h2 class="course-title-compact">{{ $course->title }}</h2>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ $progressPercentage }}%"></div>
                    </div>
                    <span class="progress-text">{{ round($progressPercentage) }}% complete</span>
                </div>
            </div>

            <!-- Mobile Curriculum -->
            <div class="curriculum-list">
                @foreach($course->chapters as $chapterIndex => $chapter)
                    <div class="curriculum-section">
                        <div class="section-header">
                            <h3 class="section-title">{{ $chapter->title }}</h3>
                            <span class="section-meta">{{ $chapter->lessons->count() }} items</span>
                        </div>

                        <div class="section-items">
                            @foreach($chapter->lessons as $lessonIndex => $chapterLesson)
                                @php
                                    $lessonProgress = $chapterLesson->progress->where('user_id', auth()->id())->first();
                                    $isCompleted = $lessonProgress && $lessonProgress->status === 'completed';
                                    $isInProgress = $lessonProgress && $lessonProgress->status === 'in_progress';
                                    $isCurrentLesson = $chapterLesson->id === $lesson->id;
                                @endphp
                                <a href="{{ route('course.lesson', [$course, $chapterLesson]) }}"
                                   class="curriculum-item {{ $isCompleted ? 'completed' : ($isInProgress ? 'in-progress' : '') }} {{ $isCurrentLesson ? 'current' : '' }}"
                                   data-lesson-id="{{ $chapterLesson->id }}"
                                   data-lesson-type="{{ $chapterLesson->type }}"
                                   title="{{ $chapterLesson->title }}"
                                   aria-label="Lesson: {{ $chapterLesson->title }} - {{ $isCompleted ? 'Completed' : ($isInProgress ? 'In Progress' : 'Not Started') }}"
                                   onclick="closeMobileSidebar()">
                                    <div class="item-status">
                                        @if($isCompleted)
                                            <svg class="status-check" viewBox="0 0 24 24" fill="currentColor">
                                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                                            </svg>
                                        @else
                                            <div class="status-dot"></div>
                                        @endif
                                    </div>
                                    <div class="item-content">
                                        <div class="item-type">
                                            @if($chapterLesson->type === 'video')
                                                📹 Video
                                            @elseif($chapterLesson->type === 'text')
                                                📖 Reading
                                            @elseif($chapterLesson->type === 'quiz')
                                                🧠 Quiz
                                            @else
                                                📝 Lesson
                                            @endif
                                            @if($chapterLesson->duration)
                                                • {{ $chapterLesson->duration }} min
                                            @endif
                                        </div>
                                        <div class="item-title">{{ $chapterLesson->title }}</div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>



@push('scripts')
<script>
// Enhanced Course Learning Interface - Unified with Main Course Page
(function() {
    'use strict';

    class CourseraLearningInterface {
        constructor() {
            this.sidebar = document.querySelector('.course-sidebar');
            this.mobileSidebar = document.getElementById('mobileSidebar');
            this.mobileOverlay = document.getElementById('mobileSidebarOverlay');
            this.menuToggle = document.getElementById('menuToggle');
            this.mobileMenuToggle = document.getElementById('mobileMenuToggle');
            this.mobileSidebarClose = document.querySelector('.mobile-sidebar-close');

            this.init();
        }

        init() {
            this.bindEvents();
            this.handleInitialState();
            this.cloneSidebarToMobile();
        }

        bindEvents() {
            // Desktop menu toggle
            if (this.menuToggle) {
                this.menuToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggleDesktopSidebar();
                });
            }

            // Mobile menu toggle
            if (this.mobileMenuToggle) {
                this.mobileMenuToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggleMobileSidebar();
                });
            }

            // Mobile overlay click
            if (this.mobileOverlay) {
                this.mobileOverlay.addEventListener('click', () => {
                    this.closeMobileSidebar();
                });
            }

            // Mobile sidebar close button
            if (this.mobileSidebarClose) {
                this.mobileSidebarClose.addEventListener('click', () => {
                    this.closeMobileSidebar();
                });
            }

            // Handle window resize
            window.addEventListener('resize', () => {
                this.handleResize();
            });

            // Escape key to close mobile sidebar
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.mobileSidebar?.classList.contains('show')) {
                    this.closeMobileSidebar();
                }
            });
        }

        toggleDesktopSidebar() {
            if (this.sidebar) {
                this.sidebar.classList.toggle('collapsed');

                // Store preference
                const isCollapsed = this.sidebar.classList.contains('collapsed');
                localStorage.setItem('sidebar-collapsed', isCollapsed);
            }
        }

        toggleMobileSidebar() {
            if (this.mobileSidebar) {
                const isOpen = this.mobileSidebar.classList.contains('show');
                if (isOpen) {
                    this.closeMobileSidebar();
                } else {
                    this.openMobileSidebar();
                }
            }
        }

        openMobileSidebar() {
            if (this.mobileSidebar && this.mobileOverlay) {
                this.mobileSidebar.classList.add('show');
                this.mobileOverlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        closeMobileSidebar() {
            if (this.mobileSidebar && this.mobileOverlay) {
                this.mobileSidebar.classList.remove('show');
                this.mobileOverlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }

        handleResize() {
            // Close mobile sidebar on desktop
            if (window.innerWidth >= 1024) {
                this.closeMobileSidebar();
            }
        }

        handleInitialState() {
            // Restore sidebar state from localStorage
            const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
            if (isCollapsed && this.sidebar) {
                this.sidebar.classList.add('collapsed');
            }

            // Ensure mobile sidebar is closed initially
            this.closeMobileSidebar();
        }

        cloneSidebarToMobile() {
            // This method would clone sidebar content to mobile if needed
            // For now, we're using the same content structure
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            new CourseraLearningInterface();
        });
    } else {
        new CourseraLearningInterface();
    }

    // Global function for mobile sidebar close (used in onclick handlers)
    window.closeMobileSidebar = function() {
        const mobileSidebar = document.getElementById('mobileSidebar');
        const mobileOverlay = document.getElementById('mobileSidebarOverlay');

        if (mobileSidebar && mobileOverlay) {
            mobileSidebar.classList.remove('show');
            mobileOverlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    };
})();

function markAsComplete() {
    const btn = document.getElementById('complete-btn');
    if (!btn) return;

    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Memproses...';

    fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            status: 'completed',
            progress_percentage: 100
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Gagal memperbarui progress. Silakan coba lagi.');
            btn.disabled = false;
            btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Terjadi kesalahan. Silakan coba lagi.');
        btn.disabled = false;
        btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Tandai Selesai';
    });
}

function startQuiz() {
    alert('Fitur kuis akan segera tersedia!');
}

function startAssignment() {
    alert('Fitur tugas akan segera tersedia!');
}

// Auto-mark video lessons as in progress when video starts playing
@if($lesson->type === 'video')
document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('lesson-video');
    if (video) {
        video.addEventListener('play', function() {
            fetch('{{ route("course.lesson.progress", [$course, $lesson]) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    status: 'in_progress',
                    progress_percentage: 50
                })
            });
        });
    }
});
@endif

// Initialize page enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for curriculum items
    const curriculumItems = document.querySelectorAll('.curriculum-item');
    curriculumItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add loading state
            this.style.opacity = '0.7';
        });
    });

    // Enhanced progress tracking
    const progressBar = document.querySelector('.progress-fill');
    if (progressBar) {
        const targetWidth = progressBar.style.width;
        progressBar.style.width = '0%';
        setTimeout(() => {
            progressBar.style.transition = 'width 1s ease-out';
            progressBar.style.width = targetWidth;
        }, 500);
    }
});
</script>
<script src="{{ asset('js/coursera-learning.js') }}"></script>
@endpush
@endsection
