/**
 * Modern Course Learning Interface JavaScript
 * Enhanced interactions with smooth animations and better UX
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeTabNavigation();
    initializeSidebarToggle();
    initializeNavigationButtons();
    initializeProgressTracking();
    initializeAnimations();
    initializeKeyboardNavigation();
    initializeResponsiveHandling();
    initializeSmoothScrolling();

    console.log('🚀 Modern Course Learning Interface initialized successfully');
});

/**
 * Enhanced Tab Navigation with Smooth Transitions
 */
function initializeTabNavigation() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const targetTab = this.getAttribute('data-tab');

            // Add loading state
            this.classList.add('loading');

            // Remove active class from all buttons and panes with animation
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.classList.remove('loading');
            });

            tabPanes.forEach(pane => {
                pane.style.opacity = '0';
                pane.style.transform = 'translateY(10px)';
                setTimeout(() => {
                    pane.classList.remove('active');
                }, 150);
            });

            // Add active class to clicked button and corresponding pane
            setTimeout(() => {
                this.classList.add('active');
                this.classList.remove('loading');

                const targetPane = document.getElementById(targetTab);
                if (targetPane) {
                    targetPane.classList.add('active');
                    targetPane.style.opacity = '1';
                    targetPane.style.transform = 'translateY(0)';
                    targetPane.classList.add('fade-in');
                }
            }, 200);
        });
    });
}

/**
 * Modern Mobile Sidebar with Slide-out Overlay and Desktop Sidebar Hide/Show
 */
function initializeSidebarToggle() {
    // Create mobile sidebar elements if they don't exist
    createMobileSidebarElements();

    // Initialize mobile sidebar functionality
    initializeMobileSidebar();

    // Initialize desktop sidebar functionality
    initializeDesktopSidebar();
}

/**
 * Initialize Mobile Sidebar Functionality
 */
function initializeMobileSidebar() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const mobileSidebarOverlay = document.querySelector('.mobile-sidebar-overlay');
    const mobileSidebarClose = document.querySelector('.mobile-sidebar-close');

    if (mobileMenuToggle && mobileSidebar && mobileSidebarOverlay) {
        // Open mobile sidebar
        mobileMenuToggle.addEventListener('click', function() {
            openMobileSidebar();
        });

        // Close mobile sidebar
        if (mobileSidebarClose) {
            mobileSidebarClose.addEventListener('click', function() {
                closeMobileSidebar();
            });
        }

        // Close on overlay click
        mobileSidebarOverlay.addEventListener('click', function() {
            closeMobileSidebar();
        });

        // Close on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileSidebar.classList.contains('active')) {
                closeMobileSidebar();
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMobileSidebar();
            }
        });
    }
}

/**
 * Initialize Desktop Sidebar Hide/Show Functionality (1024px+)
 */
function initializeDesktopSidebar() {
    const desktopMenuToggle = document.getElementById('menuToggle');
    const desktopSidebar = document.querySelector('.course-sidebar');

    if (desktopMenuToggle && desktopSidebar) {
        // Add click handler for desktop hide menu button
        desktopMenuToggle.addEventListener('click', function() {
            if (window.innerWidth >= 1024) {
                hideDesktopSidebar();
            }
        });

        // Handle escape key for desktop sidebar
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && window.innerWidth >= 1024 && desktopSidebar.classList.contains('desktop-hidden')) {
                showDesktopSidebar();
            }
        });
    }
}

/**
 * Create Mobile Sidebar Elements
 */
function createMobileSidebarElements() {
    // Check if mobile elements already exist
    if (document.querySelector('.mobile-sidebar')) {
        return;
    }

    // Create mobile menu toggle button
    const mobileMenuToggle = document.createElement('button');
    mobileMenuToggle.className = 'mobile-menu-toggle';
    mobileMenuToggle.innerHTML = `
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
    `;

    // Add to navigation
    const navControls = document.querySelector('.nav-controls');
    if (navControls) {
        navControls.parentNode.insertBefore(mobileMenuToggle, navControls);
    }

    // Create mobile sidebar overlay
    const mobileSidebarOverlay = document.createElement('div');
    mobileSidebarOverlay.className = 'mobile-sidebar-overlay';
    document.body.appendChild(mobileSidebarOverlay);

    // Create mobile sidebar
    const mobileSidebar = document.createElement('div');
    mobileSidebar.className = 'mobile-sidebar';

    // Clone desktop sidebar content
    const desktopSidebar = document.querySelector('.course-sidebar');
    if (desktopSidebar) {
        // Create mobile sidebar header
        const mobileSidebarHeader = document.createElement('div');
        mobileSidebarHeader.className = 'mobile-sidebar-header';
        mobileSidebarHeader.innerHTML = `
            <h3 class="mobile-sidebar-title">Course Navigation</h3>
            <button class="mobile-sidebar-close">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        `;

        // Clone course navigation content
        const courseNavigation = desktopSidebar.querySelector('.course-navigation');
        const clonedNavigation = courseNavigation ? courseNavigation.cloneNode(true) : null;

        mobileSidebar.appendChild(mobileSidebarHeader);
        if (clonedNavigation) {
            mobileSidebar.appendChild(clonedNavigation);
        }
    }

    document.body.appendChild(mobileSidebar);
}

/**
 * Open Mobile Sidebar
 */
function openMobileSidebar() {
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const mobileSidebarOverlay = document.querySelector('.mobile-sidebar-overlay');

    if (mobileSidebar && mobileSidebarOverlay) {
        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Show overlay
        mobileSidebarOverlay.classList.add('active');

        // Show sidebar with delay for smooth animation
        setTimeout(() => {
            mobileSidebar.classList.add('active');
        }, 50);

        // Focus management
        const firstFocusable = mobileSidebar.querySelector('button, a, [tabindex]');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }
}

/**
 * Close Mobile Sidebar
 */
function closeMobileSidebar() {
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const mobileSidebarOverlay = document.querySelector('.mobile-sidebar-overlay');

    if (mobileSidebar && mobileSidebarOverlay) {
        // Hide sidebar
        mobileSidebar.classList.remove('active');

        // Hide overlay with delay
        setTimeout(() => {
            mobileSidebarOverlay.classList.remove('active');
            // Restore body scroll
            document.body.style.overflow = '';
        }, 300);

        // Return focus to menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileMenuToggle) {
            mobileMenuToggle.focus();
        }
    }
}

/**
 * Hide Desktop Sidebar (1024px+)
 */
function hideDesktopSidebar() {
    const desktopSidebar = document.querySelector('.course-sidebar');
    const mainContent = document.querySelector('.content-area');

    if (desktopSidebar && window.innerWidth >= 1024) {
        // Add hidden class to sidebar
        desktopSidebar.classList.add('desktop-hidden');

        // Expand main content area
        if (mainContent) {
            mainContent.classList.add('sidebar-hidden');
        }

        // Create and show hamburger menu toggle
        createDesktopHamburgerToggle();

        // Focus management
        const hamburgerToggle = document.querySelector('.desktop-hamburger-toggle');
        if (hamburgerToggle) {
            hamburgerToggle.focus();
        }
    }
}

/**
 * Show Desktop Sidebar (1024px+)
 */
function showDesktopSidebar() {
    const desktopSidebar = document.querySelector('.course-sidebar');
    const mainContent = document.querySelector('.content-area');
    const hamburgerToggle = document.querySelector('.desktop-hamburger-toggle');

    if (desktopSidebar && window.innerWidth >= 1024) {
        // Remove hidden class from sidebar
        desktopSidebar.classList.remove('desktop-hidden');

        // Restore main content area
        if (mainContent) {
            mainContent.classList.remove('sidebar-hidden');
        }

        // Remove hamburger toggle
        if (hamburgerToggle) {
            hamburgerToggle.remove();
        }

        // Return focus to hide menu button
        const menuToggle = document.getElementById('menuToggle');
        if (menuToggle) {
            menuToggle.focus();
        }
    }
}

/**
 * Create Desktop Hamburger Toggle Button
 */
function createDesktopHamburgerToggle() {
    // Remove existing hamburger toggle if present
    const existingToggle = document.querySelector('.desktop-hamburger-toggle');
    if (existingToggle) {
        existingToggle.remove();
    }

    // Create hamburger toggle button
    const hamburgerToggle = document.createElement('button');
    hamburgerToggle.className = 'desktop-hamburger-toggle';
    hamburgerToggle.setAttribute('aria-label', 'Show course navigation');
    hamburgerToggle.innerHTML = `
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
        <span>Menu</span>
    `;

    // Add click handler
    hamburgerToggle.addEventListener('click', function() {
        if (window.innerWidth >= 1024) {
            showDesktopSidebar();
        }
    });

    // Insert into top navigation area, before breadcrumb
    const breadcrumbNav = document.querySelector('.breadcrumb-nav');
    if (breadcrumbNav && breadcrumbNav.parentNode) {
        breadcrumbNav.parentNode.insertBefore(hamburgerToggle, breadcrumbNav);
    }
}

/**
 * Navigation Buttons (Previous/Next)
 */
function initializeNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    // Get current lesson info from the page
    const currentLessonId = getCurrentLessonId();
    const allLessons = getAllLessons();
    
    if (prevBtn && nextBtn && allLessons.length > 0) {
        updateNavigationButtons(currentLessonId, allLessons, prevBtn, nextBtn);
        
        // Add click handlers
        prevBtn.addEventListener('click', function() {
            if (!this.disabled) {
                navigateToPreviousLesson(currentLessonId, allLessons);
            }
        });
        
        nextBtn.addEventListener('click', function() {
            if (!this.disabled) {
                navigateToNextLesson(currentLessonId, allLessons);
            }
        });
    }
}

/**
 * Progress Tracking
 */
function initializeProgressTracking() {
    // Animate progress bars on load
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.width = targetWidth;
        }, 300);
    });
    
    // Track lesson completion
    trackLessonProgress();
}

/**
 * Helper Functions
 */
function getCurrentLessonId() {
    // Extract lesson ID from URL or data attribute
    const urlParts = window.location.pathname.split('/');
    const lessonIndex = urlParts.indexOf('lesson');
    return lessonIndex !== -1 && urlParts[lessonIndex + 1] ? urlParts[lessonIndex + 1] : null;
}

function getAllLessons() {
    // Get all lesson links from the sidebar
    const lessonLinks = document.querySelectorAll('.curriculum-item');
    return Array.from(lessonLinks).map(link => {
        const href = link.getAttribute('href');
        const id = href ? href.split('/').pop() : null;
        return {
            id: id,
            title: link.querySelector('.item-title')?.textContent || '',
            url: href,
            element: link
        };
    });
}

function updateNavigationButtons(currentLessonId, allLessons, prevBtn, nextBtn) {
    const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
    
    // Update previous button
    if (currentIndex <= 0) {
        prevBtn.disabled = true;
    } else {
        prevBtn.disabled = false;
        const prevLesson = allLessons[currentIndex - 1];
        prevBtn.setAttribute('data-url', prevLesson.url);
    }
    
    // Update next button
    if (currentIndex >= allLessons.length - 1) {
        nextBtn.disabled = true;
    } else {
        nextBtn.disabled = false;
        const nextLesson = allLessons[currentIndex + 1];
        nextBtn.setAttribute('data-url', nextLesson.url);
    }
}

function navigateToPreviousLesson(currentLessonId, allLessons) {
    const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
    if (currentIndex > 0) {
        const prevLesson = allLessons[currentIndex - 1];
        window.location.href = prevLesson.url;
    }
}

function navigateToNextLesson(currentLessonId, allLessons) {
    const currentIndex = allLessons.findIndex(lesson => lesson.id === currentLessonId);
    if (currentIndex < allLessons.length - 1) {
        const nextLesson = allLessons[currentIndex + 1];
        window.location.href = nextLesson.url;
    }
}

function trackLessonProgress() {
    // Mark current lesson as in progress
    const currentLessonLink = document.querySelector('.curriculum-item.current');
    if (currentLessonLink && !currentLessonLink.classList.contains('completed')) {
        currentLessonLink.classList.add('in-progress');
    }
    
    // Auto-save progress (could be enhanced with AJAX calls)
    const saveProgress = debounce(() => {
        // Implementation for saving progress to backend
        console.log('Progress saved');
    }, 2000);
    
    // Track time spent on page
    let startTime = Date.now();
    window.addEventListener('beforeunload', () => {
        const timeSpent = Date.now() - startTime;
        // Save time spent data
        localStorage.setItem('lastLessonTime', timeSpent);
    });
}

/**
 * Utility Functions
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Keyboard Navigation
 */
document.addEventListener('keydown', function(e) {
    // Arrow key navigation
    if (e.key === 'ArrowLeft' && e.ctrlKey) {
        const prevBtn = document.getElementById('prevBtn');
        if (prevBtn && !prevBtn.disabled) {
            prevBtn.click();
        }
    } else if (e.key === 'ArrowRight' && e.ctrlKey) {
        const nextBtn = document.getElementById('nextBtn');
        if (nextBtn && !nextBtn.disabled) {
            nextBtn.click();
        }
    }
});

/**
 * Responsive Sidebar Behavior
 */
function handleResponsiveSidebar() {
    const sidebar = document.querySelector('.course-sidebar');
    const mediaQuery = window.matchMedia('(max-width: 1024px)');
    
    function handleMediaQueryChange(e) {
        if (e.matches) {
            // Mobile view
            sidebar?.classList.add('mobile');
        } else {
            // Desktop view
            sidebar?.classList.remove('mobile', 'collapsed');
        }
    }
    
    mediaQuery.addListener(handleMediaQueryChange);
    handleMediaQueryChange(mediaQuery);
}

// Initialize responsive behavior
handleResponsiveSidebar();

/**
 * Smooth Scrolling for Internal Links
 */
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

/**
 * Loading States
 */
function showLoadingState(element) {
    element.classList.add('loading');
    element.disabled = true;
}

function hideLoadingState(element) {
    element.classList.remove('loading');
    element.disabled = false;
}

/**
 * Error Handling
 */
window.addEventListener('error', function(e) {
    console.error('Learning interface error:', e.error);
    // Could implement user-friendly error notifications here
});

/**
 * Performance Monitoring
 */
if ('performance' in window) {
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }, 0);
    });
}

/**
 * Initialize Animations and Visual Effects
 */
function initializeAnimations() {
    // Add fade-in animation to content elements
    const animatedElements = document.querySelectorAll('.curriculum-item, .preview-content, .stat-item');

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    animatedElements.forEach(el => {
        observer.observe(el);
    });

    // Add hover effects to interactive elements
    const interactiveElements = document.querySelectorAll('.curriculum-item, .nav-btn, .tab-btn');
    interactiveElements.forEach(el => {
        el.classList.add('interactive-hover');
    });
}

/**
 * Enhanced Keyboard Navigation
 */
function initializeKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // ESC key to close sidebar on mobile
        if (e.key === 'Escape') {
            const sidebar = document.querySelector('.course-sidebar');
            if (sidebar && sidebar.classList.contains('mobile') && !sidebar.classList.contains('collapsed')) {
                const menuToggle = document.getElementById('menuToggle');
                if (menuToggle) {
                    menuToggle.click();
                }
            }
        }

        // Arrow keys for tab navigation
        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            const activeTab = document.querySelector('.tab-btn.active');
            if (activeTab) {
                const tabs = Array.from(document.querySelectorAll('.tab-btn'));
                const currentIndex = tabs.indexOf(activeTab);
                let nextIndex;

                if (e.key === 'ArrowLeft') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : tabs.length - 1;
                } else {
                    nextIndex = currentIndex < tabs.length - 1 ? currentIndex + 1 : 0;
                }

                tabs[nextIndex].click();
                tabs[nextIndex].focus();
                e.preventDefault();
            }
        }
    });
}

/**
 * Enhanced Responsive Handling
 */
function initializeResponsiveHandling() {
    const handleResize = debounce(() => {
        const isMobile = window.innerWidth <= 768; // Changed from 1024 to 768
        const isDesktop = window.innerWidth >= 1024;
        const mobileSidebar = document.querySelector('.mobile-sidebar');
        const desktopSidebar = document.querySelector('.course-sidebar');

        // Close mobile sidebar when switching to tablet/desktop
        if (!isMobile && mobileSidebar && mobileSidebar.classList.contains('active')) {
            closeMobileSidebar();
        }

        // Handle desktop sidebar state changes during resize
        if (isDesktop) {
            // If switching to desktop from smaller screen, ensure sidebar is visible
            if (desktopSidebar && !desktopSidebar.classList.contains('desktop-hidden')) {
                showDesktopSidebar();
            }
        } else {
            // If switching from desktop to smaller screen, reset desktop sidebar state
            if (desktopSidebar && desktopSidebar.classList.contains('desktop-hidden')) {
                // Reset desktop sidebar state but don't show it (mobile/tablet will use mobile sidebar)
                desktopSidebar.classList.remove('desktop-hidden');
                const mainContent = document.querySelector('.content-area');
                if (mainContent) {
                    mainContent.classList.remove('sidebar-hidden');
                }
                const hamburgerToggle = document.querySelector('.desktop-hamburger-toggle');
                if (hamburgerToggle) {
                    hamburgerToggle.remove();
                }
            }
        }

        // Update mobile sidebar content if needed (only for mobile, not tablet/laptop)
        if (isMobile) {
            updateMobileSidebarContent();
        }

        // Update spacing and layout based on screen size
        updateResponsiveLayout();
    }, 250);

    window.addEventListener('resize', handleResize);
    handleResize(); // Call once on init
}

/**
 * Update Mobile Sidebar Content
 */
function updateMobileSidebarContent() {
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const desktopSidebar = document.querySelector('.course-sidebar');

    if (mobileSidebar && desktopSidebar) {
        const mobileNavigation = mobileSidebar.querySelector('.course-navigation');
        const desktopNavigation = desktopSidebar.querySelector('.course-navigation');

        if (mobileNavigation && desktopNavigation) {
            // Update mobile sidebar with latest content from desktop sidebar
            const newNavigation = desktopNavigation.cloneNode(true);
            mobileNavigation.replaceWith(newNavigation);
        }
    }
}

/**
 * Update Responsive Layout
 */
function updateResponsiveLayout() {
    const screenWidth = window.innerWidth;

    // Update curriculum item spacing (only for desktop sidebar, mobile sidebar uses CSS)
    const desktopCurriculumItems = document.querySelectorAll('.course-sidebar .curriculum-item');
    desktopCurriculumItems.forEach(item => {
        if (screenWidth <= 1024 && screenWidth > 768) {
            // Laptop view - keep desktop sidebar but optimize spacing
            item.style.padding = '14px 20px';
        } else if (screenWidth > 1024) {
            // Desktop view
            item.style.padding = '16px 24px';
        }
        // Mobile view (≤768px) uses mobile sidebar, so no changes needed here
    });

    // Update mobile sidebar width (only affects mobile ≤768px)
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    if (mobileSidebar && screenWidth <= 768) {
        if (screenWidth <= 480) {
            mobileSidebar.style.width = '260px';
        } else {
            mobileSidebar.style.width = '280px';
        }
    }

    // Update content spacing based on layout type
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        if (screenWidth <= 480) {
            // Small mobile
            mainContent.style.padding = '0 12px 20px';
        } else if (screenWidth <= 768) {
            // Mobile with mobile sidebar
            mainContent.style.padding = '0 16px 24px';
        } else if (screenWidth <= 1024) {
            // Laptop with desktop sidebar
            mainContent.style.padding = '0 24px 32px';
        } else {
            // Desktop with desktop sidebar
            mainContent.style.padding = '0 40px 40px';
        }
    }
}

/**
 * Enhanced Smooth Scrolling
 */
function initializeSmoothScrolling() {
    // Smooth scroll for curriculum items
    const curriculumItems = document.querySelectorAll('.curriculum-item');
    curriculumItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Add loading state
            this.classList.add('loading');

            setTimeout(() => {
                this.classList.remove('loading');
            }, 1000);
        });
    });

    // Smooth scroll to active curriculum item
    const activeCurriculumItem = document.querySelector('.curriculum-item.current');
    if (activeCurriculumItem) {
        setTimeout(() => {
            activeCurriculumItem.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }, 500);
    }
}

// Global function to close mobile sidebar (called from view)
window.closeMobileSidebar = function() {
    const mobileSidebar = document.querySelector('.mobile-sidebar');
    const mobileSidebarOverlay = document.querySelector('.mobile-sidebar-overlay');

    if (mobileSidebar && mobileSidebarOverlay) {
        mobileSidebar.classList.remove('active');
        setTimeout(() => {
            mobileSidebarOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }, 300);
    }
};
