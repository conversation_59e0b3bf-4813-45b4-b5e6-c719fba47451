@extends('layouts.app')

@section('title', 'Daftar Sebagai Tutor - Buat Profil')

@section('content')
<div class="min-h-screen bg-gray-50 py-4">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <!-- Compact Header with Progress Steps -->
            <div class="px-6 py-4 border-b border-gray-200">
                <!-- Progress Steps - Compact Version -->
                <div class="flex items-center justify-center mb-4">
                    <div class="flex items-center space-x-4">
                        <!-- Step 1 - Completed -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-xs font-medium text-green-600 hidden sm:inline">Persetujuan</span>
                        </div>

                        <!-- Connector -->
                        <div class="w-8 h-0.5 bg-green-500"></div>

                        <!-- Step 2 - Active -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-primary text-white rounded-full font-semibold text-sm">
                                2
                            </div>
                            <span class="ml-2 text-xs font-medium text-primary hidden sm:inline">Profil</span>
                        </div>

                        <!-- Connector -->
                        <div class="w-8 h-0.5 bg-gray-300"></div>

                        <!-- Step 3 - Inactive -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-gray-200 text-gray-500 rounded-full font-semibold text-sm">
                                3
                            </div>
                            <span class="ml-2 text-xs font-medium text-gray-400 hidden sm:inline">Selesai</span>
                        </div>
                    </div>
                </div>

                <!-- Compact Header Text -->
                <div class="text-center">
                    <h1 class="text-xl font-bold text-gray-900 mb-1">Buat Profil Anda</h1>
                    <p class="text-sm text-gray-600">Ceritakan tentang diri Anda sebagai pengajar</p>
                </div>
            </div>

            <!-- Content -->
            <div class="px-6 py-6">

                <!-- Error Summary -->
                @if ($errors->any())
                    <div class="bg-red-50 border-l-4 border-red-400 rounded-lg p-3 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    Mohon perbaiki {{ $errors->count() }} kesalahan berikut:
                                </h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <form action="{{ route('tutor.register.profile.process') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                    @csrf

                    <!-- Essential Information Section -->
                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <div class="flex items-center mb-4">
                            <div class="w-6 h-6 bg-blue-600 rounded-lg flex items-center justify-center mr-2">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">Informasi Dasar</h2>
                                <p class="text-xs text-gray-600">Data wajib untuk memulai sebagai tutor</p>
                            </div>
                        </div>

                        <!-- Profile Picture Upload -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-3">
                                Foto Profil
                            </label>
                            <div class="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4">
                                <div class="shrink-0">
                                    <div id="profile-preview-container">
                                        @if(auth()->user()->profile_picture)
                                            <img id="profile-preview" class="h-20 w-20 object-cover rounded-full border-2 border-white shadow-md"
                                                 src="{{ auth()->user()->getProfilePictureUrl() }}"
                                                 alt="Profile picture">
                                        @else
                                            <div id="profile-placeholder" class="h-20 w-20 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border-2 border-white shadow-md">
                                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1 w-full">
                                    <div id="profile-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-3 text-center hover:border-blue-400 transition-colors">
                                        <input type="file"
                                               id="profile_picture"
                                               name="profile_picture"
                                               accept="image/jpeg,image/png,image/jpg"
                                               class="hidden"
                                               onchange="handleProfilePictureChange(this)">
                                        <label for="profile_picture" class="cursor-pointer">
                                            <div id="profile-upload-content">
                                                <svg class="mx-auto h-6 w-6 text-gray-400 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                <span class="text-xs text-gray-600">Klik untuk upload foto</span>
                                                <p class="text-xs text-gray-500 mt-1">JPG, PNG max. 2MB</p>
                                            </div>
                                        </label>
                                    </div>
                                    <div id="profile-file-info" class="hidden mt-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                                        <div class="flex items-center text-xs text-green-700">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span id="profile-file-name"></span>
                                            <button type="button" onclick="removeProfilePicture()" class="ml-auto text-red-500 hover:text-red-700">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    @error('profile_picture')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Full Name -->
                            <div>
                                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nama Lengkap <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="full_name"
                                       name="full_name"
                                       value="{{ old('full_name', $profile->full_name ?? '') }}"
                                       placeholder="Masukkan nama lengkap Anda"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('full_name') border-red-500 @enderror">
                                @error('full_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Public Name -->
                            <div>
                                <label for="public_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nama Publik <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="public_name"
                                       name="public_name"
                                       value="{{ old('public_name', $profile->public_name ?? '') }}"
                                       placeholder="Nama yang akan ditampilkan di kursus"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('public_name') border-red-500 @enderror">
                                <div class="flex items-center justify-between mt-1">
                                    <p class="text-xs text-gray-500">Nama untuk profil publik Anda</p>
                                    <button type="button"
                                            id="copy-full-name"
                                            class="text-xs text-primary hover:text-primary-dark font-medium px-1 py-0.5 rounded hover:bg-primary/10 transition-colors">
                                        Salin nama lengkap
                                    </button>
                                </div>
                                @error('public_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Phone Number -->
                            <div>
                                <label for="phone_number" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nomor WhatsApp <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="phone_number"
                                       name="phone_number"
                                       value="{{ old('phone_number', $profile->phone_number ?? '') }}"
                                       placeholder="08xxxxxxxxxx"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('phone_number') border-red-500 @enderror">
                                @error('phone_number')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Education Level -->
                            <div>
                                <label for="education_level" class="block text-sm font-medium text-gray-700 mb-1">
                                    Pendidikan Terakhir <span class="text-red-500">*</span>
                                </label>
                                <select id="education_level"
                                        name="education_level"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('education_level') border-red-500 @enderror">
                                    <option value="">Pilih pendidikan terakhir</option>
                                    @foreach($educationLevels as $key => $label)
                                        <option value="{{ $key }}" {{ old('education_level', $profile->education_level ?? '') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('education_level')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Legal & Identity Information Section -->
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-4">
                            <div class="w-6 h-6 bg-gray-600 rounded-lg flex items-center justify-center mr-2">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">Dokumen Identitas</h2>
                                <p class="text-xs text-gray-600">Informasi untuk verifikasi identitas</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Identity Type -->
                            <div>
                                <label for="identity_type" class="block text-sm font-medium text-gray-700 mb-1">
                                    Jenis Identitas <span class="text-red-500">*</span>
                                </label>
                                <select id="identity_type"
                                        name="identity_type"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('identity_type') border-red-500 @enderror">
                                    <option value="">Pilih jenis identitas</option>
                                    @foreach($identityTypes as $key => $label)
                                        <option value="{{ $key }}" {{ old('identity_type', $profile->identity_type ?? '') == $key ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('identity_type')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Identity Number -->
                            <div>
                                <label for="identity_number" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nomor Identitas <span class="text-red-500">*</span>
                                </label>
                                <input type="text"
                                       id="identity_number"
                                       name="identity_number"
                                       value="{{ old('identity_number', $profile->identity_number ?? '') }}"
                                       placeholder="Nomor KTP/SIM/Passport"
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors @error('identity_number') border-red-500 @enderror">
                                @error('identity_number')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>


                        </div>

                        <!-- Identity Photo Upload -->
                        <div class="mt-4">
                            <label for="identity_photo" class="block text-sm font-medium text-gray-700 mb-2">
                                Foto Identitas (KTP/SIM/Passport) <span class="text-red-500">*</span>
                            </label>
                            <div id="identity-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                                <input type="file"
                                       id="identity_photo"
                                       name="identity_photo"
                                       accept="image/jpeg,image/png,image/jpg"
                                       class="hidden"
                                       onchange="handleIdentityPhotoChange(this)">
                                <label for="identity_photo" class="cursor-pointer">
                                    <div id="identity-upload-content">
                                        <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600 font-medium">Upload foto identitas</span>
                                        <p class="text-xs text-gray-500 mt-1">JPG, PNG max. 2MB</p>
                                    </div>
                                </label>
                            </div>
                            <div id="identity-file-info" class="hidden mt-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center text-xs text-green-700">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span id="identity-file-name"></span>
                                    <button type="button" onclick="removeIdentityPhoto()" class="ml-auto text-red-500 hover:text-red-700">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div id="identity-preview" class="mt-2 hidden">
                                    <img class="w-full max-w-xs mx-auto rounded-lg shadow-sm" alt="Identity preview">
                                </div>
                            </div>
                            @if($profile && $profile->identity_photo_path)
                                <div class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center text-xs text-blue-700">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>File identitas sudah diunggah sebelumnya</span>
                                    </div>
                                </div>
                            @endif
                            @error('identity_photo')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Optional Information Section -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center mb-4">
                            <div class="w-6 h-6 bg-gray-600 rounded-lg flex items-center justify-center mr-2">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">Informasi Tambahan</h2>
                                <p class="text-xs text-gray-600">Opsional - untuk memperkuat profil Anda</p>
                            </div>
                        </div>

                        <!-- Portfolio Upload -->
                        <div class="mb-4">
                            <label for="portfolio" class="block text-sm font-medium text-gray-700 mb-2">
                                Portofolio atau Resume (PDF)
                            </label>
                            <div id="portfolio-upload-area" class="border-2 border-dashed border-gray-300 rounded-lg p-3 text-center hover:border-blue-400 transition-colors">
                                <input type="file"
                                       id="portfolio"
                                       name="portfolio"
                                       accept="application/pdf"
                                       class="hidden"
                                       onchange="handlePortfolioChange(this)">
                                <label for="portfolio" class="cursor-pointer">
                                    <div id="portfolio-upload-content">
                                        <svg class="mx-auto h-6 w-6 text-gray-400 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-600">Upload portofolio/resume</span>
                                        <p class="text-xs text-gray-500 mt-1">PDF max. 2MB</p>
                                    </div>
                                </label>
                            </div>
                            <div id="portfolio-file-info" class="hidden mt-2 p-2 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center text-xs text-green-700">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span id="portfolio-file-name"></span>
                                    <button type="button" onclick="removePortfolio()" class="ml-auto text-red-500 hover:text-red-700">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            @if($profile && $profile->portfolio_path)
                                <div class="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-center text-xs text-blue-700">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span>Portofolio sudah diunggah sebelumnya</span>
                                    </div>
                                </div>
                            @endif
                            @error('portfolio')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>



                        <!-- Description Section -->
                        <div class="mt-4">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                                Deskripsi Singkat
                            </label>
                            <textarea id="description"
                                      name="description"
                                      rows="3"
                                      placeholder="Ceritakan sedikit tentang diri Anda dan keahlian yang dimiliki..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none @error('description') border-red-500 @enderror">{{ old('description', $profile->description ?? '') }}</textarea>
                            <p class="text-xs text-gray-500 mt-1">Deskripsi singkat untuk kartu profil Anda (maksimal 1000 karakter)</p>
                            @error('description')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Long Description -->
                        <div class="mt-4">
                            <label for="long_description" class="block text-sm font-medium text-gray-700 mb-1">
                                Tentang Saya (Deskripsi Lengkap)
                            </label>
                            <textarea id="long_description"
                                      name="long_description"
                                      rows="4"
                                      placeholder="Ceritakan secara detail tentang latar belakang, pengalaman mengajar, metodologi pembelajaran, dan keahlian khusus Anda..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none @error('long_description') border-red-500 @enderror">{{ old('long_description', $profile->long_description ?? '') }}</textarea>
                            <p class="text-xs text-gray-500 mt-1">Deskripsi lengkap untuk halaman profil publik Anda (maksimal 10.000 karakter)</p>
                            @error('long_description')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="bg-white pb-4 pt-6 border-t border-gray-200">
                        <div class="px-4 sm:px-6">
                            <div class="flex flex-col sm:flex-row justify-between items-center gap-3">
                                <a href="{{ route('tutor.register.terms') }}"
                                class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 bg-white rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium text-sm">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                                    </svg>
                                    Kembali
                                </a>

                                <button type="submit"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors font-medium shadow-sm text-sm">
                                    Lanjutkan ke Review
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy full name to public name
    const copyButton = document.getElementById('copy-full-name');
    const fullNameInput = document.getElementById('full_name');
    const publicNameInput = document.getElementById('public_name');

    if (copyButton && fullNameInput && publicNameInput) {
        copyButton.addEventListener('click', function() {
            const fullName = fullNameInput.value.trim();
            if (fullName) {
                publicNameInput.value = fullName;
                publicNameInput.focus();
                // Add visual feedback
                copyButton.textContent = 'Tersalin!';
                copyButton.classList.add('text-green-600');
                setTimeout(() => {
                    copyButton.textContent = 'Salin nama lengkap';
                    copyButton.classList.remove('text-green-600');
                }, 2000);
            } else {
                showNotification('Silakan isi nama lengkap terlebih dahulu', 'warning');
                fullNameInput.focus();
            }
        });
    }

    // Auto-focus on first error field if there are validation errors
    @if ($errors->any())
        const firstErrorField = document.querySelector('.border-red-500');
        if (firstErrorField) {
            firstErrorField.focus();
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    @endif

    // Form validation before submit
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('input[required], select[required]');
            let hasErrors = false;
            let firstErrorField = null;

            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    hasErrors = true;
                    if (!firstErrorField) {
                        firstErrorField = field;
                    }
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            if (hasErrors) {
                e.preventDefault();
                if (firstErrorField) {
                    firstErrorField.focus();
                    firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
                showNotification('Mohon lengkapi semua field yang wajib diisi', 'error');
            }
        });
    }
});

// File upload handlers with preview and validation
function handleProfilePictureChange(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file size (2MB max)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Ukuran file terlalu besar. Maksimal 2MB.', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
            showNotification('Format file tidak didukung. Gunakan JPG atau PNG.', 'error');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // Update preview image
            const preview = document.getElementById('profile-preview');
            const placeholder = document.getElementById('profile-placeholder');

            if (preview) {
                preview.src = e.target.result;
            } else if (placeholder) {
                placeholder.innerHTML = `<img id="profile-preview" class="h-20 w-20 object-cover rounded-full border-2 border-white shadow-md" src="${e.target.result}" alt="Profile preview">`;
            }

            // Show file info
            document.getElementById('profile-file-name').textContent = file.name;
            document.getElementById('profile-file-info').classList.remove('hidden');

            showNotification('Foto profil berhasil dipilih', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function removeProfilePicture() {
    document.getElementById('profile_picture').value = '';
    document.getElementById('profile-file-info').classList.add('hidden');

    // Reset to placeholder
    const container = document.getElementById('profile-preview-container');
    container.innerHTML = `
        <div id="profile-placeholder" class="h-20 w-20 rounded-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border-2 border-white shadow-md">
            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
        </div>
    `;
}

function handleIdentityPhotoChange(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file size (2MB max)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Ukuran file terlalu besar. Maksimal 2MB.', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
            showNotification('Format file tidak didukung. Gunakan JPG atau PNG.', 'error');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            // Show preview
            const preview = document.getElementById('identity-preview');
            const previewImg = preview.querySelector('img');
            previewImg.src = e.target.result;
            preview.classList.remove('hidden');

            // Show file info
            document.getElementById('identity-file-name').textContent = file.name;
            document.getElementById('identity-file-info').classList.remove('hidden');

            showNotification('Foto identitas berhasil dipilih', 'success');
        };
        reader.readAsDataURL(file);
    }
}

function removeIdentityPhoto() {
    document.getElementById('identity_photo').value = '';
    document.getElementById('identity-file-info').classList.add('hidden');
    document.getElementById('identity-preview').classList.add('hidden');
}

function handlePortfolioChange(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file size (2MB max)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Ukuran file terlalu besar. Maksimal 2MB.', 'error');
            input.value = '';
            return;
        }

        // Validate file type
        if (file.type !== 'application/pdf') {
            showNotification('Format file tidak didukung. Gunakan PDF.', 'error');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('portfolio-file-name').textContent = file.name;
        document.getElementById('portfolio-file-info').classList.remove('hidden');

        showNotification('Portofolio berhasil dipilih', 'success');
    }
}

function removePortfolio() {
    document.getElementById('portfolio').value = '';
    document.getElementById('portfolio-file-info').classList.add('hidden');
}



// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
                   type === 'warning' ? 'bg-yellow-100 border-yellow-400 text-yellow-700' :
                   type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
                   'bg-blue-100 border-blue-400 text-blue-700';

    notification.className = `fixed top-4 right-4 ${bgColor} px-4 py-3 rounded border z-50 shadow-lg`;
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button type="button" class="ml-3 text-current hover:opacity-75" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    document.body.appendChild(notification);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 4000);
}
</script>
@endpush

@endsection
