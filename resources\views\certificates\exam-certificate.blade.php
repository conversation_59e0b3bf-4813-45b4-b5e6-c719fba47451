<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sertifikat <PERSON> - {{ $exam->title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: white;
            margin: 0;
            padding: 0;
        }

        .certificate-container {
            background: white;
            width: 297mm;
            height: 210mm;
            position: relative;
            overflow: hidden;
            padding: 40px;
            margin: 0 auto;
            border: 1px solid #e5e7eb;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
        }

        .brand-text {
            font-size: 24px;
            font-weight: 800;
            color: #000000;
            letter-spacing: -0.5px;
        }

        .certificate-id {
            text-align: right;
            font-size: 11px;
            color: #6b7280;
            line-height: 1.4;
        }

        .certificate-title {
            text-align: center;
            margin-bottom: 50px;
        }

        .title-main {
            font-size: 42px;
            font-weight: 800;
            color: #111827;
            margin-bottom: 8px;
            letter-spacing: -1px;
        }

        .title-sub {
            font-size: 18px;
            color: #6b7280;
            font-weight: 400;
        }

        .recipient-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .recipient-name {
            font-size: 36px;
            font-weight: 700;
            color: #059669;
            margin-bottom: 20px;
            text-decoration: underline;
            text-decoration-color: #059669;
            text-decoration-thickness: 3px;
            text-underline-offset: 8px;
        }

        .completion-text {
            font-size: 16px;
            color: #374151;
            line-height: 1.6;
            max-width: 600px;
            margin: 0 auto;
        }

        .exam-details {
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border: 2px solid #059669;
            border-radius: 16px;
            padding: 30px;
            margin: 40px 0;
            text-align: center;
        }

        .exam-title {
            font-size: 28px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 15px;
        }

        .exam-meta {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .meta-item {
            text-align: center;
        }

        .meta-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .meta-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .score-highlight {
            color: #059669;
            font-size: 18px;
            font-weight: 700;
        }

        .instructor-section {
            margin: 40px 0;
            text-align: center;
        }

        .instructor-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .instructor-name {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .footer {
            position: absolute;
            bottom: 40px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
        }

        .completion-date {
            font-size: 14px;
            color: #6b7280;
        }

        .verification-info {
            text-align: right;
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .decorative-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 3px solid #059669;
            border-radius: 20px;
            pointer-events: none;
        }

        .decorative-corner {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 3px solid #059669;
        }

        .corner-tl {
            top: 10px;
            left: 10px;
            border-right: none;
            border-bottom: none;
        }

        .corner-tr {
            top: 10px;
            right: 10px;
            border-left: none;
            border-bottom: none;
        }

        .corner-bl {
            bottom: 10px;
            left: 10px;
            border-right: none;
            border-top: none;
        }

        .corner-br {
            bottom: 10px;
            right: 10px;
            border-left: none;
            border-top: none;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            font-weight: 100;
            color: rgba(5, 150, 105, 0.03);
            z-index: 0;
            pointer-events: none;
        }

        .content {
            position: relative;
            z-index: 1;
        }



        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .certificate-container {
                border: none;
                margin: 0;
                padding: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        <!-- Decorative Elements -->
        <div class="decorative-border"></div>
        <div class="decorative-corner corner-tl"></div>
        <div class="decorative-corner corner-tr"></div>
        <div class="decorative-corner corner-bl"></div>
        <div class="decorative-corner corner-br"></div>
        <div class="watermark">NGAMBISKUY</div>

        <div class="content">
            <!-- Header with Brand and Certificate ID -->
            <div class="header">
                <div class="brand-text">Ngambiskuy</div>
                <div class="certificate-id">
                    Certificate ID: {{ $certificate_id }}<br>
                    Reference Number: {{ strtoupper(substr(md5($user->id . $exam->id), 0, 8)) }}
                </div>
            </div>

            <!-- Certificate Title -->
            <div class="certificate-title">
                <h1 class="title-main">Certificate of Achievement</h1>
                <p class="title-sub">This is to certify that</p>
            </div>

            <!-- Recipient Name -->
            <div class="recipient-section">
                <div class="recipient-name">{{ $user->name }}</div>
                <p class="completion-text">
                    has successfully completed the examination and demonstrated proficiency in the subject matter, 
                    achieving a passing score and meeting all requirements for certification.
                </p>
            </div>

            <!-- Exam Details -->
            <div class="exam-details">
                <div class="exam-title">{{ $exam->title }}</div>
                <div class="exam-meta">
                    <div class="meta-item">
                        <div class="meta-label">Score Achieved</div>
                        <div class="meta-value score-highlight">{{ number_format($score_percentage, 1) }}%</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Questions Answered</div>
                        <div class="meta-value">{{ $correct_answers }}/{{ $total_questions }}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Difficulty Level</div>
                        <div class="meta-value">{{ $exam->difficulty_label }}</div>
                    </div>
                </div>
            </div>

            <!-- Instructor Section -->
            <div class="instructor-section">
                <div class="instructor-label">Exam Created By</div>
                <div class="instructor-name">{{ $exam->tutor->name }}</div>
            </div>

            <!-- Footer -->
            <div class="footer">
                <div class="completion-date">
                    Completed on {{ $completion_date->format('F d, Y') }}
                </div>
                <div class="verification-info">
                    Issued on {{ $issue_date->format('F d, Y') }}<br>
                    Verify at {{ url('/verify/' . $certificate_id) }}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
