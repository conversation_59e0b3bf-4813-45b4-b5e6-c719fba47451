<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Schema;
use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\ExamAttempt;
use App\Models\LessonProgress;
use App\Traits\CourseProgressTrait;

class UserController extends Controller
{
    use CourseProgressTrait;
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the user dashboard.
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Get enrolled courses from database (same logic as courses method)
        $enrolledCourses = CourseEnrollment::with(['course.tutor', 'course.category'])
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->get()
            ->map(function($enrollment) use ($user) {
                $course = $enrollment->course;
                $progressData = $this->calculateCourseProgress($course, $user->id);
                return [
                    'enrollment' => $enrollment,
                    'course' => $course,
                    'progress' => $progressData['progress_percentage'],
                    'is_completed' => $progressData['progress_percentage'] >= 100,
                    'is_free_course' => false
                ];
            });
        
        // Get free courses with progress (same logic as courses method)
        $freeCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('is_free', true)
            ->whereNotIn('id', $enrolledCourses->pluck('course.id'))
            ->get()
            ->map(function($course) use ($user) {
                $progressData = $this->calculateCourseProgress($course, $user->id);
                return [
                    'enrollment' => null,
                    'course' => $course,
                    'progress' => $progressData['progress_percentage'],
                    'is_completed' => $progressData['progress_percentage'] >= 100,
                    'is_free_course' => true
                ];
            });
        
        // Combine all courses (same logic as courses method)
        $allCourses = $enrolledCourses->concat($freeCourses);
        
        // Count total courses and completed courses (matching courses page logic)
        $totalCourses = $allCourses->count();
        $totalCompletedCourses = $allCourses->where('is_completed', true)->count();
        
        // Get completed enrolled courses count for other calculations
        $completedEnrolledCoursesCount = $enrolledCourses->where('is_completed', true)->count();

        // Get exam data (consistent with exams method)
        $examEnrollments = \App\Models\ExamEnrollment::where('user_id', $user->id)
            ->where('payment_status', 'paid')
            ->count();
        $examAttempts = ExamAttempt::where('user_id', $user->id)->count();
        $passedExams = ExamAttempt::where('user_id', $user->id)
            ->where('is_passed', true)
            ->count();

        // Calculate total learning hours from lesson progress
        $totalMinutes = LessonProgress::where('user_id', $user->id)
            ->sum('time_spent_seconds') / 60;
        $totalHours = round($totalMinutes / 60, 1);

        // Calculate learning streak (simplified - days with activity in last 30 days)
        $learningStreak = LessonProgress::where('user_id', $user->id)
            ->where('last_accessed_at', '>=', now()->subDays(30))
            ->distinct('user_id')
            ->count();

        // Calculate XP points (simplified calculation)
        $xpPoints = ($totalCompletedCourses * 100) + ($passedExams * 50) + (int)($totalHours * 10);

        $stats = [
            'enrolled_courses' => $totalCourses, // Include both enrolled and free courses
            'completed_courses' => $totalCompletedCourses, // Include both enrolled and free completed courses
            'certificates' => $passedExams, // Use passed exams as certificates
            'xp_points' => $xpPoints, // Use calculated XP points
            'learning_streak' => min($learningStreak, 30), // Cap at 30 days
            'total_hours' => $totalHours, // Use calculated total hours
            'exam_enrollments' => $examEnrollments, // Add exam enrollments for consistency
            'exam_attempts' => $examAttempts,
            'passed_exams' => $passedExams
        ];

        // Get recent activities (enrollments, exam attempts, lesson progress)
        $recentActivities = collect();

        // Recent course enrollments
        $recentEnrollments = CourseEnrollment::with('course')
            ->where('user_id', $user->id)
            ->orderBy('enrolled_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($enrollment) {
                return [
                    'type' => 'course_enrollment',
                    'title' => 'Mendaftar kursus: ' . $enrollment->course->title,
                    'date' => $enrollment->enrolled_at,
                    'icon' => 'book',
                    'color' => 'blue',
                    'url' => route('course.show', $enrollment->course)
                ];
            });

        // Recent exam attempts
        $recentExamAttempts = ExamAttempt::with('exam')
            ->where('user_id', $user->id)
            ->orderBy('submitted_at', 'desc')
            ->limit(3)
            ->get()
            ->map(function($attempt) {
                return [
                    'type' => 'exam_attempt',
                    'title' => 'Mengikuti ujian: ' . $attempt->exam->title,
                    'date' => $attempt->submitted_at,
                    'icon' => 'clipboard',
                    'color' => $attempt->is_passed ? 'green' : 'red',
                    'url' => route('exams.result', [$attempt->exam, $attempt]),
                    'score' => $attempt->score_percentage
                ];
            });

        $recentActivities = $recentActivities->merge($recentEnrollments)
            ->merge($recentExamAttempts)
            ->sortByDesc('date')
            ->take(5);

        // Get in-progress courses for "Lanjutkan Belajar" section (only courses with progress > 0 and < 100)
        $inProgressCourses = $allCourses->filter(function($courseData) {
            return $courseData['progress'] > 0 && $courseData['progress'] < 100;
        });
        
        // Prepare courses with detailed progress data for "Lanjutkan Belajar" section
        $allCoursesWithProgress = $inProgressCourses->map(function($courseData) use ($user) {
            $course = $courseData['course'];
            $progressData = $this->calculateCourseProgress($course, $user->id);
            
            return [
                'enrollment' => $courseData['enrollment'],
                'course' => $course,
                'progress' => $progressData['progress_percentage'],
                'completed_lessons' => $progressData['completed_lessons'],
                'total_lessons' => $progressData['total_lessons'],
                'last_accessed' => $progressData['last_accessed'],
                'status' => $courseData['enrollment'] ? $courseData['enrollment']->status : 'free',
                'is_completed' => $progressData['progress_percentage'] >= 100,
                'is_free_course' => $courseData['is_free_course'],
                'url' => route('course.learn', $course)
            ];
        })->sortByDesc('last_accessed')
        ->take(3);

        // Get recommended courses that user hasn't enrolled in or started yet
        $excludedCourseIds = $allCourses->pluck('course.id');
        
        $recommendedCourses = Course::with(['tutor', 'category'])
            ->published()
            ->whereNotIn('id', $excludedCourseIds) // Exclude all courses user has enrolled in or started
            ->where(function($query) {
                $query->where('is_featured', true)
                      ->orWhere('is_free', true) // Include free courses in recommendations
                      ->orWhere('price', '>', 0); // Include paid courses
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('is_free', 'desc') // Prioritize free courses
            ->orderBy('average_rating', 'desc')
            ->orderBy('total_students', 'desc')
            ->limit(6) // Show more courses
            ->get()
            ->map(function($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'description' => $course->description,
                    'tutor' => $course->tutor->name,
                    'category' => $course->category->name ?? 'Umum',
                    'price' => $course->is_free ? 'Gratis' : 'Rp ' . number_format($course->price, 0, ',', '.'),
                    'level' => ucfirst($course->level),
                    'rating' => $course->average_rating ?: 0,
                    'students' => $course->total_students,
                    'is_free' => $course->is_free,
                    'url' => route('course.show', $course),
                    'thumbnail' => $course->thumbnail ? asset('storage/' . $course->thumbnail) : null
                ];
            });

        return view('user.dashboard', compact('user', 'stats', 'recentActivities', 'recommendedCourses', 'allCoursesWithProgress'));
    }

    /**
     * Show the user profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('user.profile', compact('user'));
    }

    /**
     * Show the user courses page.
     */
    public function courses()
    {
        $user = Auth::user();

        // Get real enrolled courses from database with consistent progress calculation
        $enrolledCourses = CourseEnrollment::with(['course.tutor', 'course.category'])
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) use ($user) {
                $course = $enrollment->course;
                
                // Use the trait method for consistent progress calculation
                $progressData = $this->calculateCourseProgress($course, $user->id);
                
                $progressPercentage = $progressData['progress_percentage'];
                $completedLessons = $progressData['completed_lessons'];
                $totalLessons = $progressData['total_lessons'];
                $lastAccessed = $progressData['last_accessed'];

                return [
                    'enrollment' => $enrollment,
                    'course' => $course,
                    'progress' => $progressPercentage,
                    'last_accessed' => $lastAccessed,
                    'status' => $enrollment->status,
                    'is_completed' => $progressPercentage >= 100,
                    'is_free_course' => false
                ];
            });

        // Get free courses with progress tracking
        $freeCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('is_free', true)
            ->whereNotIn('id', $enrolledCourses->pluck('course.id'))
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function($course) use ($user) {
                // Use the trait method for consistent progress calculation
                $progressData = $this->calculateCourseProgress($course, $user->id);
                
                $progressPercentage = $progressData['progress_percentage'];
                $completedLessons = $progressData['completed_lessons'];
                $totalLessons = $progressData['total_lessons'];
                $lastAccessed = $progressData['last_accessed'];

                return [
                    'enrollment' => null,
                    'course' => $course,
                    'progress' => $progressPercentage,
                    'last_accessed' => $lastAccessed,
                    'status' => 'free',
                    'is_completed' => $progressPercentage >= 100,
                    'is_free_course' => true
                ];
            });

        // Combine enrolled and free courses
        $allCourses = $enrolledCourses->concat($freeCourses);

        // Separate completed and in-progress courses
        $completedCourses = $allCourses->where('is_completed', true);
        $inProgressCourses = $allCourses->where('is_completed', false);

        // Get available courses from database
        $availableCourses = Course::with(['tutor', 'category'])
            ->published()
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return view('user.courses', compact('user', 'enrolledCourses', 'inProgressCourses', 'completedCourses', 'availableCourses', 'freeCourses', 'allCourses'));
    }

    /*
     * Show the user progress page.
     * COMMENTED OUT: Functionality overlaps with user dashboard and courses page
     * This method provides detailed progress tracking, learning roadmap, and achievements
     * Similar functionality exists in:
     * - user.dashboard: Shows overall progress stats and recent activity
     * - user.courses: Shows enrolled courses with progress indicators
     * - UserProfileController: Provides progress data for profile context
     */
    /*
    public function progress()
    {
        $user = Auth::user();

        // Get user's enrolled courses
        $enrolledCourses = CourseEnrollment::where('user_id', $user->id)
            ->with(['course.chapters.lessons'])
            ->get();

        $coursesInProgress = [];
        $totalLessons = 0;
        $completedLessons = 0;

        foreach ($enrolledCourses as $enrollment) {
            $course = $enrollment->course;

            // Get all lesson IDs for this course
            $lessonIds = $course->chapters->flatMap(function ($chapter) {
                return $chapter->lessons->pluck('id');
            });

            $totalLessons += $lessonIds->count();

            // Get user's progress for this course
            $courseProgress = LessonProgress::where('user_id', $user->id)
                ->whereIn('lesson_id', $lessonIds)
                ->get();

            $courseCompletedLessons = $courseProgress->where('status', 'completed')->count();
            $completedLessons += $courseCompletedLessons;

            // Calculate course progress percentage
            $courseProgressPercentage = $lessonIds->count() > 0
                ? round(($courseCompletedLessons / $lessonIds->count()) * 100)
                : 0;

            // Include courses that have any progress (> 0%)
            if ($courseProgressPercentage > 0) {
                $lastAccessed = $courseProgress->max('last_accessed_at');
                $totalTimeSpent = $courseProgress->sum('time_spent_seconds');

                $coursesInProgress[] = [
                    'id' => $course->id,
                    'title' => $course->title,
                    'instructor' => $course->tutor->name,
                    'progress' => $courseProgressPercentage,
                    'completed_modules' => $courseCompletedLessons,
                    'total_modules' => $lessonIds->count(),
                    'time_spent' => $this->formatTimeSpent($totalTimeSpent),
                    'last_accessed' => $lastAccessed ? $lastAccessed->diffForHumans() : 'Belum pernah',
                    'slug' => $course->slug
                ];
            }
        }

        // Calculate overall progress
        $overallProgress = $totalLessons > 0 ? round(($completedLessons / $totalLessons) * 100) : 0;

        // Create learning path based on enrolled courses (ordered by difficulty/level)
        $learningPath = [];
        if ($enrolledCourses->count() > 0) {
            // Sort courses by level (beginner -> intermediate -> advanced) and enrollment date
            $sortedEnrollments = $enrolledCourses->sortBy(function ($enrollment) {
                $levelOrder = ['beginner' => 1, 'intermediate' => 2, 'advanced' => 3];
                return ($levelOrder[$enrollment->course->level] ?? 4) . '_' . $enrollment->enrolled_at;
            });

            foreach ($sortedEnrollments as $index => $enrollment) {
                $course = $enrollment->course;
                $lessonIds = $course->chapters->flatMap(function ($chapter) {
                    return $chapter->lessons->pluck('id');
                });

                $courseProgress = LessonProgress::where('user_id', $user->id)
                    ->whereIn('lesson_id', $lessonIds)
                    ->get();

                $courseCompletedLessons = $courseProgress->where('status', 'completed')->count();
                $courseProgressPercentage = $lessonIds->count() > 0
                    ? round(($courseCompletedLessons / $lessonIds->count()) * 100)
                    : 0;

                $isCompleted = $courseProgressPercentage >= 100;
                $isCurrent = $courseProgressPercentage > 0 && $courseProgressPercentage < 100;

                // Only mark one course as current (the first one that's in progress)
                $shouldBeCurrent = $isCurrent && count(array_filter($learningPath, fn($step) => $step['current'])) === 0;

                $learningPath[] = [
                    'title' => $course->title,
                    'description' => $course->description,
                    'completed' => $isCompleted,
                    'current' => $shouldBeCurrent,
                    'progress' => $courseProgressPercentage,
                    'level' => ucfirst($course->level)
                ];
            }
        }

        $progressData = [
            'overall_progress' => $overallProgress,
            'courses_in_progress' => $coursesInProgress,
            'completed_modules' => $completedLessons,
            'total_modules' => $totalLessons,
            'learning_path' => $learningPath
        ];

        return view('user.progress', compact('user', 'progressData'));
    }
    */

    /**
     * Format time spent in seconds to human readable format.
     */
    private function formatTimeSpent($seconds)
    {
        if ($seconds < 60) {
            return $seconds . ' detik';
        } elseif ($seconds < 3600) {
            return round($seconds / 60) . ' menit';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = round(($seconds % 3600) / 60);
            return $hours . ' jam ' . ($minutes > 0 ? $minutes . ' menit' : '');
        }
    }

    /**
     * Show the user certificates page.
     */
    public function certificates()
    {
        $user = Auth::user();

        // Get enrolled courses with completion status
        $enrolledCourses = CourseEnrollment::with(['course.tutor', 'course.category'])
            ->where('user_id', $user->id)
            ->where('status', 'active')
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function($enrollment) use ($user) {
                $course = $enrollment->course;
                
                // Use the trait method for consistent progress calculation
                $progressData = $this->calculateCourseProgress($course, $user->id);
                
                return [
                    'enrollment' => $enrollment,
                    'course' => $course,
                    'progress' => $progressData['progress_percentage'],
                    'last_accessed' => $progressData['last_accessed'],
                    'status' => $enrollment->status,
                    'is_completed' => $progressData['progress_percentage'] >= 100,
                    'is_free_course' => false
                ];
            });

        // Get all free courses with progress tracking (don't exclude enrolled ones)
        $freeCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('is_free', true)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function($course) use ($user) {
                // Use the trait method for consistent progress calculation
                $progressData = $this->calculateCourseProgress($course, $user->id);
                
                return [
                    'enrollment' => null,
                    'course' => $course,
                    'progress' => $progressData['progress_percentage'],
                    'last_accessed' => $progressData['last_accessed'],
                    'status' => 'free',
                    'is_completed' => $progressData['progress_percentage'] >= 100,
                    'is_free_course' => true
                ];
            });

        // Combine enrolled and free courses, removing duplicates by course ID
        $allCourses = $enrolledCourses->concat($freeCourses)
            ->unique(function($item) {
                return $item['course']->id;
            });

        // Filter for completed courses only
        $completedCourses = $allCourses->where('is_completed', true);

        // Convert to certificate format
        $courseCertificates = $completedCourses->map(function($courseData) {
            $course = $courseData['course'];
            $enrollment = $courseData['enrollment'];

            // For completion date, use the latest completed lesson or enrollment date
            $completedAt = $courseData['last_accessed'] ?? ($enrollment ? $enrollment->updated_at : $course->updated_at);

            return [
                'type' => 'course',
                'title' => $course->title,
                'tutor' => $course->tutor->name,
                'completed_at' => $completedAt,
                'certificate_id' => 'COURSE-' . strtoupper(substr($course->id, 0, 8)),
                'course_id' => $course->id,
                'level' => $course->level,
                'duration' => $course->duration_hours . ' jam'
            ];
        });

        // Get exam certificates (passed exams)
        $examCertificates = ExamAttempt::with(['exam.tutor'])
            ->where('user_id', $user->id)
            ->where('is_passed', true)
            ->orderBy('completed_at', 'desc')
            ->get()
            ->map(function($attempt) {
                return [
                    'type' => 'exam',
                    'title' => $attempt->exam->title,
                    'tutor' => $attempt->exam->tutor->name,
                    'completed_at' => $attempt->completed_at,
                    'certificate_id' => 'EXAM-' . strtoupper(substr($attempt->id, 0, 8)),
                    'score' => $attempt->score_percentage,
                    'exam_id' => $attempt->exam->id
                ];
            });

        // Combine and sort all certificates
        $allCertificates = $courseCertificates->concat($examCertificates)
            ->sortByDesc('completed_at');

        // Calculate stats
        $stats = [
            'total_certificates' => $allCertificates->count(),
            'course_certificates' => $courseCertificates->count(),
            'exam_certificates' => $examCertificates->count(),
            'average_rating' => 4.8 // You can calculate this from actual ratings later
        ];

        return view('user.certificates', compact('user', 'allCertificates', 'stats'));
    }

    /**
     * Show the user settings page.
     */
    public function settings()
    {
        $user = Auth::user();
        
        // Check which columns exist in the users table
        $hasTermsAgreed = Schema::hasColumn('users', 'terms_agreed');
        $hasPrivacyAgreed = Schema::hasColumn('users', 'privacy_agreed');
        $hasLearningPreferences = Schema::hasColumn('users', 'learning_preferences');
        $hasMinatBelajar = Schema::hasColumn('users', 'minat_belajar');
        $hasWorkPreferences = Schema::hasColumn('users', 'work_preferences');
        $hasIndustryInterests = Schema::hasColumn('users', 'industry_interests');
        
        return view('user.settings', compact(
            'user', 
            'hasTermsAgreed', 
            'hasPrivacyAgreed', 
            'hasLearningPreferences', 
            'hasMinatBelajar', 
            'hasWorkPreferences', 
            'hasIndustryInterests'
        ));
    }

    /**
     * Update user settings.
     */
    public function updateSettings(Request $request)
    {
        $user = Auth::user();
        
        // Define which fields are allowed to be updated in settings
        $allowedFields = [
            'learning_preferences',
            'minat_belajar',
            'work_preferences',
            'industry_interests',
            'terms_agreed',
            'privacy_agreed'
        ];
        
        // Get only the fields that exist in the request and are allowed
        $data = [];
        foreach ($allowedFields as $field) {
            if ($request->has($field)) {
                $data[$field] = $request->input($field);
            }
        }
        
        // Update user with the filtered data
        $user->update($data);
        
        return back()->with('success', 'Pengaturan berhasil disimpan!');
    }

    /**
     * Show the user exams page.
     */
    public function exams()
    {
        $user = Auth::user();

        // Get user's exam enrollments with attempts
        $examEnrollments = \App\Models\ExamEnrollment::with(['exam.tutor', 'exam.category', 'exam.questions'])
            ->where('user_id', $user->id)
            ->where('payment_status', 'paid')
            ->orderBy('enrolled_at', 'desc')
            ->get();

        // Get user's exam attempts with attempt count per exam
        $examAttempts = \App\Models\ExamAttempt::with(['exam.tutor'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function($attempt) use ($user) {
                // Use the attempt_number field which represents the sequential attempt number
                $attempt->user_attempt_count = $attempt->attempt_number;
                $attempt->max_attempts = $attempt->exam->max_attempts;
                
                return $attempt;
            });

        // Calculate stats
        $stats = [
            'total_enrollments' => $examEnrollments->count(),
            'total_attempts' => \App\Models\ExamAttempt::where('user_id', $user->id)->count(),
            'passed_exams' => \App\Models\ExamAttempt::where('user_id', $user->id)
                ->where('is_passed', true)
                ->distinct('exam_id')
                ->count('exam_id'),
            'average_score' => \App\Models\ExamAttempt::where('user_id', $user->id)->avg('score_percentage') ?: 0,
        ];

        return view('user.exams', compact('user', 'examEnrollments', 'examAttempts', 'stats'));
    }

    /**
     * Show the user blog reading history page.
     */
    public function blog()
    {
        $user = Auth::user();

        // Get user's saved articles with blog post data
        $savedArticles = $user->savedBlogPosts()
            ->with(['author', 'category'])
            ->get();

        // Get recent posts for discovery
        $recentPosts = \App\Models\BlogPost::with(['author', 'category'])
            ->published()
            ->orderBy('published_at', 'desc')
            ->limit(6)
            ->get();

        // Get featured posts for recommendations
        $featuredPosts = \App\Models\BlogPost::with(['author', 'category'])
            ->published()
            ->featured()
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        // Calculate stats
        $uniqueCategories = $savedArticles->pluck('category.id')->filter()->unique();
        $totalReadTime = $savedArticles->sum('read_time') ?? 0;

        $stats = [
            'total_saved' => $savedArticles->count(),
            'categories_saved' => $uniqueCategories->count(),
            'total_read_time' => $totalReadTime,
        ];

        return view('user.blog', compact('user', 'savedArticles', 'recentPosts', 'featuredPosts', 'stats'));
    }
}
