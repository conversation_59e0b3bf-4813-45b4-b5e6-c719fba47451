@extends('layouts.app')

@section('title', 'Daftar Sebagai Tutor - Review & Submit')

@section('content')
<div class="min-h-screen bg-gray-50 py-4">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Main Content -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Compact Header with Progress Steps -->
            <div class="px-6 py-4 border-b border-gray-200">
                <!-- Progress Steps - Compact Version -->
                <div class="flex items-center justify-center mb-4">
                    <div class="flex items-center space-x-4">
                        <!-- Step 1 - Completed -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-xs font-medium text-green-600 hidden sm:inline">Persetujuan</span>
                        </div>

                        <!-- Connector -->
                        <div class="w-8 h-0.5 bg-green-500"></div>

                        <!-- Step 2 - Completed -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-xs font-medium text-green-600 hidden sm:inline">Profil</span>
                        </div>

                        <!-- Connector -->
                        <div class="w-8 h-0.5 bg-green-500"></div>

                        <!-- Step 3 - Active -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 bg-orange-500 text-white rounded-full font-semibold text-sm">
                                3
                            </div>
                            <span class="ml-2 text-xs font-medium text-orange-600 hidden sm:inline">Selesai</span>
                        </div>
                    </div>
                </div>

                <!-- Compact Header Text -->
                <div class="text-center">
                    <h1 class="text-xl font-bold text-gray-900 mb-1">Review Profil Anda</h1>
                    <p class="text-sm text-gray-600">Pastikan semua informasi sudah benar sebelum mengirim aplikasi</p>
                </div>
            </div>

            <!-- Content -->
            <div class="px-8 py-8">

                <!-- Profile Review -->
                <div class="space-y-8">
                    <!-- Personal Information Card -->
                    <div class="bg-gradient-to-r from-orange-50 to-gray-50 rounded-xl p-6 border border-orange-200">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900">Informasi Dasar</h2>
                            </div>
                            <a href="{{ route('tutor.register.profile') }}"
                               class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-orange-600 bg-white rounded-lg border border-orange-300 hover:bg-orange-50 transition-colors">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit
                            </a>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-white rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Nama Lengkap</label>
                                <p class="text-gray-900 font-medium">{{ $profile->full_name }}</p>
                            </div>

                            <div class="bg-white rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Nama Publik</label>
                                <p class="text-gray-900 font-medium">{{ $profile->public_name }}</p>
                                @if($profile->public_name_slug)
                                    <p class="text-xs text-gray-500 mt-1">URL: /tutor/{{ $profile->public_name_slug }}</p>
                                @endif
                            </div>

                            <div class="bg-white rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Nomor WhatsApp</label>
                                <p class="text-gray-900 font-medium">{{ $profile->phone_number }}</p>
                            </div>

                            <div class="bg-white rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Pendidikan Terakhir</label>
                                <p class="text-gray-900 font-medium">{{ $profile->education_level }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Identity Information Card -->
                    <div class="bg-white border border-gray-200 rounded-xl p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <h2 class="text-xl font-semibold text-gray-900">Informasi Identitas</h2>
                            </div>
                            <a href="{{ route('tutor.register.profile') }}"
                               class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 bg-gray-100 rounded-lg border border-gray-300 hover:bg-gray-200 transition-colors">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                Edit
                            </a>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Jenis Identitas</label>
                                <p class="text-gray-900 font-medium">{{ $profile->identity_type }}</p>
                            </div>

                            <div class="bg-gray-50 rounded-lg p-4">
                                <label class="block text-sm font-medium text-gray-500 mb-1">Nomor Identitas</label>
                                <p class="text-gray-900 font-medium">{{ $profile->identity_number }}</p>
                            </div>
                        </div>

                        <!-- Payment Settings Notice -->
                        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900 mb-1">Pengaturan Pembayaran</h4>
                                    <p class="text-sm text-blue-800">Setelah aplikasi disetujui, Anda dapat mengatur informasi pembayaran (rekening bank, e-wallet, dll.) di halaman pengaturan tutor.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Status Card -->
                    <div class="bg-gray-50 border border-gray-200 rounded-xl p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900">Status Dokumen</h2>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white rounded-lg p-4 border">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Foto Identitas</span>
                                    @if($profile->identity_photo_path)
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span class="text-sm text-green-600 font-medium">Tersedia</span>
                                        </div>
                                    @else
                                        <span class="text-sm text-red-500 font-medium">Belum upload</span>
                                    @endif
                                </div>
                            </div>

                            <div class="bg-white rounded-lg p-4 border">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700">Portfolio/Resume</span>
                                    @if($profile->portfolio_path)
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span class="text-sm text-green-600 font-medium">Tersedia</span>
                                        </div>
                                    @else
                                        <span class="text-sm text-gray-500">Opsional</span>
                                    @endif
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Description Card -->
                    @if($profile->description || $profile->long_description)
                    <div class="bg-white border border-gray-200 rounded-xl p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900">Tentang Anda</h2>
                        </div>

                        @if($profile->description)
                        <div class="mb-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Deskripsi Singkat</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-gray-700">{{ $profile->description }}</p>
                            </div>
                        </div>
                        @endif

                        @if($profile->long_description)
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Deskripsi Lengkap</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-gray-700">{{ $profile->long_description }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                    @endif

                    <!-- Agreement Status Card -->
                    <div class="bg-green-50 border border-green-200 rounded-xl p-6">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900">Status Persetujuan</h2>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 bg-white rounded-lg p-3">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-green-700 font-medium">Syarat dan Ketentuan telah disetujui</span>
                            </div>
                            <div class="flex items-center space-x-3 bg-white rounded-lg p-3">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-green-700 font-medium">Kebijakan Privasi telah disetujui</span>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notice -->
                    {{-- <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                        <div class="flex items-start space-x-3">
                            <svg class="w-6 h-6 text-blue-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <h3 class="text-lg font-semibold text-blue-900 mb-3">Langkah Selanjutnya</h3>
                                <div class="space-y-2 text-blue-800 text-sm">
                                    <div class="flex items-center space-x-2">
                                        <span class="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                                        <span>Aplikasi akan ditinjau dalam 1-3 hari kerja</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                                        <span>Notifikasi status akan dikirim via email</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="w-1.5 h-1.5 bg-blue-500 rounded-full"></span>
                                        <span>Setelah disetujui, Anda dapat mulai membuat kursus</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> --}}

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 mt-8 border-t border-gray-200">
                        <a href="{{ route('tutor.register.profile') }}"
                           class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 bg-white rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors font-medium">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Profil
                        </a>

                        <form action="{{ route('tutor.register.submit') }}" method="POST" class="w-full sm:w-auto">
                            @csrf
                            <button type="submit"
                                    id="submit-application"
                                    class="w-full sm:w-auto inline-flex items-center justify-center px-8 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors font-medium shadow-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Kirim Aplikasi
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const submitButton = document.getElementById('submit-application');

    if (submitButton) {
        submitButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Create custom confirmation modal
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-xl p-6 max-w-md mx-4 shadow-2xl">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Konfirmasi Pengiriman</h3>
                        <p class="text-gray-600 mb-6">Apakah Anda yakin ingin mengirim aplikasi? Data tidak dapat diubah setelah dikirim.</p>
                        <div class="flex space-x-3">
                            <button type="button" id="cancel-submit" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                Batal
                            </button>
                            <button type="button" id="confirm-submit" class="flex-1 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors">
                                Ya, Kirim
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle modal actions
            document.getElementById('cancel-submit').addEventListener('click', function() {
                document.body.removeChild(modal);
            });

            document.getElementById('confirm-submit').addEventListener('click', function() {
                document.body.removeChild(modal);
                submitButton.closest('form').submit();
            });

            // Close modal on backdrop click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        });
    }
});
</script>
@endpush

@endsection
