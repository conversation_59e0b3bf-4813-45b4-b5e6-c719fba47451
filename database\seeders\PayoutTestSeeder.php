<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\TutorPaymentSettings;
use App\Models\PayoutRequest;
use App\Models\Payment;
use App\Models\Course;

class PayoutTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the test tutor user
        $tutor = User::where('email', '<EMAIL>')->first();
        
        if (!$tutor) {
            $this->command->error('Test tutor user not found. Please run UserSeeder first.');
            return;
        }

        // Create payment settings for the tutor
        $paymentSettings = TutorPaymentSettings::updateOrCreate(
            ['tutor_id' => $tutor->id],
            [
                'bank_name' => 'Bank Central Asia (BCA)',
                'bank_account_number' => '**********',
                'bank_account_holder' => 'Sari <PERSON><PERSON>',
                'bank_branch' => 'Jakarta Pusat',
                'npwp_number' => '12.345.678.9-012.000',
                'npwp_name' => 'Sari <PERSON>',
                'npwp_address' => 'Jakarta, Indonesia',
                'tax_status' => 'non_pkp',
                'preferred_method' => 'bank_transfer',
                'minimum_payout' => 100000,
                'payout_frequency' => 'manual',
                'auto_payout_enabled' => false,
                'bank_verified' => true,
                'tax_verified' => true,
                'bank_verified_at' => now(),
                'tax_verified_at' => now(),
            ]
        );

        $this->command->info('Created payment settings for tutor: ' . $tutor->name);

        // Get first available category
        $category = \App\Models\Category::first();
        if (!$category) {
            $this->command->error('No categories found. Please run CategorySeeder first.');
            return;
        }

        // Create some test courses for the tutor if they don't exist
        $course = Course::firstOrCreate(
            [
                'tutor_id' => $tutor->id,
                'title' => 'Data Science Fundamentals'
            ],
            [
                'slug' => 'data-science-fundamentals',
                'description' => 'Learn the basics of data science with Python',
                'price' => 500000,
                'status' => 'published',
                'category_id' => $category->id,
                'level' => 'beginner',
                'duration' => '40 jam',
                'thumbnail' => 'course/thumbnail/default.jpg',
                'published_at' => now(),
            ]
        );

        // Create some test payments to simulate earnings
        $payments = [
            [
                'amount' => 500000,
                'tutor_earnings' => 300000, // 60% of amount
                'status' => 'completed',
                'paid_at' => now()->subDays(10),
            ],
            [
                'amount' => 750000,
                'tutor_earnings' => 450000, // 60% of amount
                'status' => 'completed',
                'paid_at' => now()->subDays(5),
            ],
            [
                'amount' => 300000,
                'tutor_earnings' => 180000, // 60% of amount
                'status' => 'completed',
                'paid_at' => now()->subDays(2),
            ],
        ];

        foreach ($payments as $paymentData) {
            Payment::create([
                'user_id' => $tutor->id, // Simulating student purchase
                'payment_type' => 'course',
                'payable_type' => Course::class,
                'payable_id' => $course->id,
                'amount' => $paymentData['amount'],
                'platform_fee' => $paymentData['amount'] * 0.05, // 5% platform fee
                'tutor_earnings' => $paymentData['tutor_earnings'],
                'currency' => 'IDR',
                'status' => $paymentData['status'],
                'payment_method' => 'bank_transfer',
                'payment_gateway' => 'manual',
                'transaction_id' => 'TXN' . now()->timestamp . rand(1000, 9999),
                'external_transaction_id' => 'GTW' . now()->timestamp . rand(1000, 9999),
                'paid_at' => $paymentData['paid_at'],
                'gateway_response' => ['status' => 'success'],
                'metadata' => ['test_data' => true],
            ]);
        }

        $this->command->info('Created test payments for tutor earnings');

        // Create some test payout requests with different statuses
        $payoutRequests = [
            [
                'amount' => 200000,
                'status' => 'pending',
                'tutor_notes' => 'Payout request untuk bulan ini',
                'requested_at' => now()->subDays(3),
            ],
            [
                'amount' => 150000,
                'status' => 'processing',
                'tutor_notes' => 'Urgent payout request',
                'requested_at' => now()->subDays(7),
                'processed_at' => now()->subDays(6),
                'notes' => 'Sedang diproses oleh tim finance',
            ],
            [
                'amount' => 300000,
                'status' => 'approved',
                'tutor_notes' => 'Payout untuk pembayaran kursus',
                'requested_at' => now()->subDays(10),
                'processed_at' => now()->subDays(8),
                'notes' => 'Disetujui, menunggu transfer',
            ],
            [
                'amount' => 100000,
                'status' => 'paid',
                'tutor_notes' => 'Payout bulanan',
                'requested_at' => now()->subDays(15),
                'processed_at' => now()->subDays(12),
                'paid_at' => now()->subDays(10),
                'notes' => 'Transfer berhasil dilakukan',
            ],
            [
                'amount' => 50000,
                'status' => 'rejected',
                'tutor_notes' => 'Payout kecil untuk testing',
                'requested_at' => now()->subDays(20),
                'processed_at' => now()->subDays(18),
                'rejected_at' => now()->subDays(18),
                'notes' => 'Jumlah di bawah minimum payout',
            ],
        ];

        foreach ($payoutRequests as $payoutData) {
            $platformFee = PayoutRequest::calculatePlatformFee($payoutData['amount']);
            $netAmount = $payoutData['amount'] - $platformFee;

            PayoutRequest::create([
                'tutor_id' => $tutor->id,
                'request_id' => PayoutRequest::generateRequestId(),
                'amount' => $payoutData['amount'],
                'platform_fee' => $platformFee,
                'net_amount' => $netAmount,
                'currency' => 'IDR',
                'status' => $payoutData['status'],
                'notes' => $payoutData['notes'] ?? null,
                'tutor_notes' => $payoutData['tutor_notes'],
                'payment_method' => $paymentSettings->preferred_method,
                'payment_details' => $paymentSettings->getPreferredPaymentDetails(),
                'requested_at' => $payoutData['requested_at'],
                'processed_at' => $payoutData['processed_at'] ?? null,
                'paid_at' => $payoutData['paid_at'] ?? null,
                'rejected_at' => $payoutData['rejected_at'] ?? null,
                'processed_by' => isset($payoutData['processed_at']) ? $tutor->id : null, // Simulating admin processing
            ]);
        }

        $this->command->info('Created test payout requests with various statuses');
        $this->command->info('Payout test data seeding completed successfully!');
        
        // Display summary
        $totalEarnings = Payment::where('payable_type', Course::class)
            ->whereHas('payable', function($query) use ($tutor) {
                $query->where('tutor_id', $tutor->id);
            })
            ->where('status', 'completed')
            ->sum('tutor_earnings');

        $requestedPayouts = PayoutRequest::where('tutor_id', $tutor->id)
            ->whereIn('status', ['pending', 'processing', 'approved'])
            ->sum('amount');

        $availableAmount = max(0, $totalEarnings - $requestedPayouts);

        $this->command->info("Summary for tutor {$tutor->name}:");
        $this->command->info("- Total earnings: IDR " . number_format($totalEarnings, 0, ',', '.'));
        $this->command->info("- Requested payouts: IDR " . number_format($requestedPayouts, 0, ',', '.'));
        $this->command->info("- Available for payout: IDR " . number_format($availableAmount, 0, ',', '.'));
    }
}
