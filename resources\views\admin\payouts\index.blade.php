@extends('layouts.admin')

@section('title', 'Manajemen Payout')

@section('content')
<div class="admin-dashboard-container p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="admin-welcome-header mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">Manajemen Payout</h1>
                <p class="text-gray-600">Kelola permintaan payout dari tutor</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{{ route('admin.payouts.export', request()->query()) }}" 
                   class="btn border-emerald-300 text-emerald-600 hover:bg-emerald-50">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export CSV
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Requests</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['pending']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Paid</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['paid']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Paid</p>
                    <p class="text-2xl font-bold text-gray-900">IDR {{ number_format($stats['total_amount_paid'], 0, ',', '.') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
        <form method="GET" action="{{ route('admin.payouts.index') }}" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select name="status" id="status" class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500">
                    <option value="">Semua Status</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                    <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                    <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Paid</option>
                    <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                </select>
            </div>

            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">Dari Tanggal</label>
                <input type="date" name="date_from" id="date_from" value="{{ request('date_from') }}" 
                       class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500">
            </div>

            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">Sampai Tanggal</label>
                <input type="date" name="date_to" id="date_to" value="{{ request('date_to') }}" 
                       class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500">
            </div>

            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Cari</label>
                <input type="text" name="search" id="search" value="{{ request('search') }}" 
                       placeholder="Request ID atau nama tutor..."
                       class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500">
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="btn btn-primary flex-1">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Filter
                </button>
                <a href="{{ route('admin.payouts.index') }}" class="btn border-gray-300 text-gray-700 hover:bg-gray-50">
                    Reset
                </a>
            </div>
        </form>
    </div>

    <!-- Payout Requests Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tutor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($payoutRequests as $payout)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $payout->request_id }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-emerald-100 flex items-center justify-center">
                                        <span class="text-sm font-medium text-emerald-600">
                                            {{ substr($payout->tutor->name, 0, 1) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $payout->tutor->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $payout->tutor->email }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $payout->formatted_amount }}</div>
                            <div class="text-sm text-gray-500">Net: {{ $payout->formatted_net_amount }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {{ $payout->status_color }}">
                                {{ $payout->status_name }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $payout->requested_at->format('d M Y H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-2">
                                <button onclick="viewPayout('{{ $payout->id }}')" 
                                        class="text-emerald-600 hover:text-emerald-900">
                                    View
                                </button>
                                @if(in_array($payout->status, ['pending', 'processing']))
                                <button onclick="updatePayoutStatus('{{ $payout->id }}')" 
                                        class="text-blue-600 hover:text-blue-900">
                                    Update
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p class="mt-2 text-sm">Tidak ada permintaan payout ditemukan.</p>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($payoutRequests->hasPages())
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $payoutRequests->links() }}
        </div>
        @endif
    </div>
</div>

<!-- View Payout Modal -->
<div id="viewPayoutModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Detail Payout Request</h3>
            <button id="closeViewModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="viewPayoutContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div id="updateStatusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Update Status Payout</h3>
            <button id="closeUpdateModal" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="updateStatusContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

@include('components.toast')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal elements
    const viewPayoutModal = document.getElementById('viewPayoutModal');
    const updateStatusModal = document.getElementById('updateStatusModal');
    const closeViewModal = document.getElementById('closeViewModal');
    const closeUpdateModal = document.getElementById('closeUpdateModal');

    // Close modals
    closeViewModal.addEventListener('click', function() {
        viewPayoutModal.classList.add('hidden');
    });

    closeUpdateModal.addEventListener('click', function() {
        updateStatusModal.classList.add('hidden');
    });

    // Close modals when clicking outside
    viewPayoutModal.addEventListener('click', function(e) {
        if (e.target === viewPayoutModal) {
            viewPayoutModal.classList.add('hidden');
        }
    });

    updateStatusModal.addEventListener('click', function(e) {
        if (e.target === updateStatusModal) {
            updateStatusModal.classList.add('hidden');
        }
    });
});

// View payout details
function viewPayout(payoutId) {
    fetch(`/admin/payouts/${payoutId}/data`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const payout = data.payout_request;
                const content = document.getElementById('viewPayoutContent');

                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Informasi Request</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Request ID:</span>
                                        <span class="text-sm font-medium">${payout.request_id}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Status:</span>
                                        <span class="text-sm font-medium">${payout.status_name}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Tanggal Request:</span>
                                        <span class="text-sm font-medium">${payout.requested_at}</span>
                                    </div>
                                    ${payout.processed_at ? `
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Tanggal Proses:</span>
                                        <span class="text-sm font-medium">${payout.processed_at}</span>
                                    </div>
                                    ` : ''}
                                    ${payout.processed_by ? `
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Diproses oleh:</span>
                                        <span class="text-sm font-medium">${payout.processed_by}</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-900 mb-3">Informasi Tutor</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Nama:</span>
                                        <span class="text-sm font-medium">${payout.tutor_name}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">Email:</span>
                                        <span class="text-sm font-medium">${payout.tutor_email}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Detail Pembayaran</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Jumlah Request</p>
                                        <p class="text-lg font-semibold text-gray-900">${payout.amount}</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Platform Fee (2%)</p>
                                        <p class="text-lg font-semibold text-red-600">${payout.platform_fee}</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm text-gray-600">Jumlah Bersih</p>
                                        <p class="text-lg font-semibold text-green-600">${payout.net_amount}</p>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <p class="text-sm text-gray-600 mb-2">Metode Pembayaran: <span class="font-medium">${payout.payment_method}</span></p>
                                    <div class="text-sm text-gray-600">
                                        <p class="font-medium mb-1">Detail Pembayaran:</p>
                                        <pre class="bg-white p-2 rounded border text-xs">${JSON.stringify(payout.payment_details, null, 2)}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>

                        ${payout.tutor_notes ? `
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Catatan Tutor</h4>
                            <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${payout.tutor_notes}</p>
                        </div>
                        ` : ''}

                        ${payout.admin_notes ? `
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Catatan Admin</h4>
                            <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">${payout.admin_notes}</p>
                        </div>
                        ` : ''}
                    </div>
                `;

                viewPayoutModal.classList.remove('hidden');
            } else {
                showToast('Gagal memuat detail payout', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat memuat detail payout', 'error');
        });
}

// Update payout status
function updatePayoutStatus(payoutId) {
    const content = document.getElementById('updateStatusContent');

    content.innerHTML = `
        <form id="updateStatusForm" data-payout-id="${payoutId}">
            <div class="space-y-4">
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status Baru</label>
                    <select name="status" id="status" required class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500">
                        <option value="">Pilih Status</option>
                        <option value="processing">Processing</option>
                        <option value="approved">Approved</option>
                        <option value="paid">Paid</option>
                        <option value="rejected">Rejected</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Catatan (Opsional)</label>
                    <textarea name="notes" id="notes" rows="3"
                              class="w-full rounded-lg border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
                              placeholder="Tambahkan catatan untuk perubahan status..."></textarea>
                </div>

                <div class="flex gap-3 pt-4">
                    <button type="button" onclick="document.getElementById('updateStatusModal').classList.add('hidden')"
                            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                        Batal
                    </button>
                    <button type="submit" class="flex-1 px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">
                        Update Status
                    </button>
                </div>
            </div>
        </form>
    `;

    updateStatusModal.classList.remove('hidden');

    // Handle form submission
    document.getElementById('updateStatusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        submitStatusUpdate(this);
    });
}

// Submit status update
function submitStatusUpdate(form) {
    const formData = new FormData(form);
    const payoutId = form.dataset.payoutId;
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.disabled = true;
    submitBtn.textContent = 'Memproses...';

    fetch(`/admin/payouts/${payoutId}/status`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatusModal.classList.add('hidden');
            showToast(data.message, 'success');
            // Refresh the page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Terjadi kesalahan saat memperbarui status', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}
</script>
@endsection
