<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verifikasi Sertifikat - {{ $certificate->certificate_id }}</title>
    <meta name="description" content="Verifikasi sertifikat Ngambiskuy untuk {{ $certificate->user_name }} - {{ $certificate->title }}">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .verification-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 12px 20px;
            border-radius: 50px;
            margin-top: 20px;
            font-weight: 600;
        }

        .verification-badge svg {
            width: 20px;
            height: 20px;
        }

        .content {
            padding: 40px;
        }

        .certificate-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: #f9fafb;
            border-radius: 12px;
            padding: 24px;
            border-left: 4px solid #10b981;
        }

        .info-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .info-value {
            font-size: 18px;
            color: #1f2937;
            font-weight: 600;
            line-height: 1.4;
        }

        .certificate-details {
            background: #f0fdf4;
            border-radius: 12px;
            padding: 30px;
            border: 1px solid #bbf7d0;
            margin-bottom: 30px;
        }

        .certificate-details h3 {
            color: #166534;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-label {
            font-size: 12px;
            color: #166534;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 16px;
            color: #1f2937;
            font-weight: 500;
        }

        .actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .footer {
            background: #f9fafb;
            padding: 30px 40px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer p {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
        }

        .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .logo {
            width: 60px;
            height: 60px;
        }

        .brand-text {
            font-size: 24px;
            font-weight: 800;
            color: #ffffff;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }

            .header {
                padding: 30px 20px;
            }

            .content {
                padding: 30px 20px;
            }

            .footer {
                padding: 20px;
            }

            .certificate-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .actions {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-section">
                <img src="{{ asset('images/Artboard 5.png') }}" alt="Ngambiskuy Logo" class="logo">
                <div class="brand-text">Ngambiskuy</div>
            </div>
            
            <h1>Sertifikat Terverifikasi</h1>
            <p>Sertifikat ini telah diverifikasi dan sah</p>
            
            <div class="verification-badge">
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                Sertifikat Valid
            </div>
        </div>

        <div class="content">
            <div class="certificate-info">
                <div class="info-card">
                    <div class="info-label">ID Sertifikat</div>
                    <div class="info-value">{{ $certificate->certificate_id }}</div>
                </div>
                
                <div class="info-card">
                    <div class="info-label">Penerima</div>
                    <div class="info-value">{{ $certificate->user_name }}</div>
                </div>
                
                <div class="info-card">
                    <div class="info-label">{{ $certificate->type === 'course' ? 'Kursus' : 'Ujian' }}</div>
                    <div class="info-value">{{ $certificate->title }}</div>
                </div>
                
                <div class="info-card">
                    <div class="info-label">Instruktur</div>
                    <div class="info-value">{{ $certificate->instructor_name }}</div>
                </div>
            </div>

            <div class="certificate-details">
                <h3>
                    <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Detail Sertifikat
                </h3>
                
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-label">Tanggal Selesai</div>
                        <div class="detail-value">{{ $certificate->completion_date->format('d F Y') }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Tanggal Diterbitkan</div>
                        <div class="detail-value">{{ $certificate->issue_date->format('d F Y') }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Jenis Sertifikat</div>
                        <div class="detail-value">{{ $certificate->type === 'course' ? 'Sertifikat Kursus' : 'Sertifikat Ujian' }}</div>
                    </div>
                    
                    @if($certificate->certificate_data)
                        @if($certificate->type === 'course')
                            @if(isset($certificate->certificate_data['total_lessons']))
                                <div class="detail-item">
                                    <div class="detail-label">Total Pelajaran</div>
                                    <div class="detail-value">{{ $certificate->certificate_data['total_lessons'] }} pelajaran</div>
                                </div>
                            @endif
                            
                            @if(isset($certificate->certificate_data['total_duration']))
                                <div class="detail-item">
                                    <div class="detail-label">Durasi Total</div>
                                    <div class="detail-value">{{ number_format($certificate->certificate_data['total_duration'] / 60, 1) }} jam</div>
                                </div>
                            @endif
                        @elseif($certificate->type === 'exam')
                            @if(isset($certificate->certificate_data['score_percentage']))
                                <div class="detail-item">
                                    <div class="detail-label">Skor</div>
                                    <div class="detail-value">{{ $certificate->certificate_data['score_percentage'] }}%</div>
                                </div>
                            @endif
                            
                            @if(isset($certificate->certificate_data['correct_answers']) && isset($certificate->certificate_data['total_questions']))
                                <div class="detail-item">
                                    <div class="detail-label">Jawaban Benar</div>
                                    <div class="detail-value">{{ $certificate->certificate_data['correct_answers'] }}/{{ $certificate->certificate_data['total_questions'] }}</div>
                                </div>
                            @endif
                        @endif
                    @endif
                </div>
            </div>

            <div class="actions">
                <a href="{{ url('/') }}" class="btn btn-primary">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Kembali ke Beranda
                </a>

                @auth
                    @if($certificate->type === 'course')
                        <a href="{{ route('course.certificate.download', $certificate->certifiable) }}" class="btn btn-success">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Download PDF
                        </a>
                    @else
                        <a href="{{ route('exam.certificate.download', $certificate->certifiable) }}" class="btn btn-success">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                            Download PDF
                        </a>
                    @endif
                @endauth

                <button onclick="window.print()" class="btn btn-secondary">
                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"/>
                    </svg>
                    Cetak Halaman
                </button>
            </div>
        </div>

        <div class="footer">
            <p>
                Sertifikat ini diterbitkan oleh <strong>Ngambiskuy</strong> dan dapat diverifikasi secara online.<br>
                Untuk informasi lebih lanjut, kunjungi <a href="{{ url('/') }}" style="color: #10b981;">ngambiskuy.com</a>
            </p>
        </div>
    </div>
</body>
</html>
