/* Course Learning Page - Professional Design */
/* Following Udemy/Coursera patterns with Ngambiskuy color palette */

/* CSS Variables for consistency */
:root {
    --primary-color: #FF6B35;
    --primary-dark: #E55A2B;
    --primary-light: #FF8C42;
    --secondary-color: #2563eb;
    --secondary-dark: #1d4ed8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.2s ease;
}

/* Base Layout */
.course-learning-container {
    min-height: 100vh;
    background: var(--gray-50);
    font-family: 'Inter', sans-serif;
}

/* Course Header */
.course-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.course-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>');
    opacity: 0.1;
}

.course-header-content {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: center;
}

@media (min-width: 1024px) {
    .course-header-content {
        grid-template-columns: 1fr auto;
        gap: 3rem;
    }
}

/* Breadcrumb */
.course-breadcrumb {
    margin-bottom: 1.5rem;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.25rem 0;
}

.breadcrumb-link:hover {
    color: var(--white);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 0.25rem;
}

.breadcrumb-current {
    color: var(--white);
    font-weight: 500;
}

/* Course Info */
.course-info h1.course-title {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .course-info h1.course-title {
        font-size: 2.5rem;
    }
}

.course-info .instructor-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.course-info .instructor-link:hover {
    color: var(--white);
}

/* Progress Stats */
.course-progress-stats {
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-circle {
    position: relative;
    width: 60px;
    height: 60px;
}

.progress-ring {
    width: 60px;
    height: 60px;
    transform: rotate(-90deg);
}

.progress-ring-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 3;
}

.progress-ring-fill {
    fill: none;
    stroke: var(--white);
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--white);
}

.progress-label {
    color: var(--white);
}

.progress-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    display: block;
}

.progress-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Learning Interface */
.learning-interface {
    display: flex;
    min-height: calc(100vh - 200px);
    position: relative;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: fixed;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    z-index: 60;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    cursor: pointer;
}

.sidebar-toggle:hover {
    background: var(--gray-50);
    transform: translateY(-50%) scale(1.05);
}

.sidebar-toggle-icon {
    width: 20px;
    height: 20px;
    color: var(--gray-600);
}

.sidebar-toggle-text {
    display: none;
}

@media (min-width: 1024px) {
    .sidebar-toggle {
        display: none;
    }
}

/* Sidebar Overlay */
.sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 50;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

@media (min-width: 1024px) {
    .sidebar-overlay {
        display: none;
    }
}

/* Curriculum Sidebar */
.curriculum-sidebar {
    width: 320px;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    z-index: 55;
}

/* Mobile Sidebar */
@media (max-width: 1023px) {
    .curriculum-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        transform: translateX(-100%);
        box-shadow: var(--shadow-xl);
    }

    .curriculum-sidebar.show {
        transform: translateX(0);
    }
}

/* Desktop Sidebar */
@media (min-width: 1024px) {
    .curriculum-sidebar {
        position: sticky;
        top: 0;
        height: 100vh;
        flex-shrink: 0;
    }
}

/* Sidebar Header */
.sidebar-header {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: var(--white);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.sidebar-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: var(--border-radius);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: var(--transition);
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
}

.sidebar-close svg {
    width: 20px;
    height: 20px;
}

@media (min-width: 1024px) {
    .sidebar-close {
        display: none;
    }
}

/* Sidebar Content */
.sidebar-content {
    flex: 1;
    overflow-y: auto;
    background: var(--gray-50);
}

.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: var(--gray-100);
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Course Progress Summary */
.course-progress-summary {
    padding: 1rem;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--gray-600);
    text-align: center;
}

/* Curriculum Chapters */
.curriculum-chapters {
    padding: 0;
}

.chapter-section {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
}

.chapter-header {
    padding: 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chapter-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chapter-number {
    color: var(--primary-color);
    font-weight: 700;
}

.chapter-duration {
    font-size: 0.75rem;
    color: var(--gray-500);
    background: var(--gray-100);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

/* Chapter Lessons */
.chapter-lessons {
    padding: 0;
}

.lesson-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--gray-700);
    transition: var(--transition);
    border-bottom: 1px solid var(--gray-100);
}

.lesson-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.lesson-item.completed {
    background: rgba(16, 185, 129, 0.05);
    border-left: 3px solid var(--success-color);
}

.lesson-item.in-progress {
    background: rgba(245, 158, 11, 0.05);
    border-left: 3px solid var(--warning-color);
}

.lesson-icon {
    width: 20px;
    height: 20px;
    color: var(--gray-400);
    flex-shrink: 0;
}

.lesson-item.completed .lesson-icon {
    color: var(--success-color);
}

.lesson-item.in-progress .lesson-icon {
    color: var(--warning-color);
}

.lesson-icon svg {
    width: 100%;
    height: 100%;
}

.lesson-content {
    flex: 1;
    min-width: 0;
}

.lesson-title {
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lesson-duration {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.lesson-status {
    flex-shrink: 0;
}

.status-icon {
    width: 20px;
    height: 20px;
}

.status-icon.completed {
    color: var(--success-color);
}

.status-icon.in-progress {
    color: var(--warning-color);
}

.status-icon.not-started {
    width: 8px;
    height: 8px;
    background: var(--gray-300);
    border-radius: 50%;
    margin: 6px;
}

/* Main Content Area */
.main-content {
    flex: 1;
    background: var(--white);
    overflow-y: auto;
}

@media (min-width: 1024px) {
    .main-content {
        margin-left: 0;
    }
}

.content-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

@media (max-width: 768px) {
    .content-container {
        padding: 1rem;
    }
}

/* Welcome Section */
.welcome-section {
    margin-bottom: 3rem;
}

.welcome-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .welcome-title {
        font-size: 1.5rem;
    }
}

.welcome-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* Next Lesson Card */
.next-lesson-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.next-lesson-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.next-lesson-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
}

.next-lesson-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    position: relative;
}

@media (max-width: 640px) {
    .next-lesson-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }
}

.lesson-info h4.lesson-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.lesson-info .lesson-chapter {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.continue-btn {
    background: var(--white);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    flex-shrink: 0;
}

.continue-btn:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.continue-btn svg {
    width: 16px;
    height: 16px;
}

/* Completion Card */
.completion-card {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
}

.completion-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.completion-icon svg {
    width: 32px;
    height: 32px;
}

.completion-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.completion-description {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.explore-btn {
    background: var(--white);
    color: var(--success-color);
    padding: 0.75rem 2rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    transition: var(--transition);
}

.explore-btn:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Course Overview */
.course-overview {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    border: 1px solid var(--gray-200);
}

.overview-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 640px) {
    .overview-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .overview-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.overview-card {
    background: var(--gray-50);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.overview-card:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.overview-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    flex-shrink: 0;
}

.overview-icon svg {
    width: 24px;
    height: 24px;
}

.overview-content {
    flex: 1;
    min-width: 0;
}

.overview-card-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    margin: 0 0 0.25rem 0;
}

.overview-card-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 1023px) {
    .learning-interface {
        flex-direction: column;
    }

    .main-content {
        margin-left: 0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-out-left {
    animation: slideOutLeft 0.3s ease-in;
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

/* Focus States for Accessibility */
.lesson-item:focus,
.continue-btn:focus,
.explore-btn:focus,
.sidebar-toggle:focus,
.sidebar-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .sidebar-toggle,
    .sidebar-overlay,
    .curriculum-sidebar {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .course-header {
        background: var(--gray-800) !important;
        -webkit-print-color-adjust: exact;
    }
}
