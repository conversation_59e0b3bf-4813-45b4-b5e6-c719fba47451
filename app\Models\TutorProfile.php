<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class TutorProfile extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'full_name',
        'public_name',
        'public_name_slug',
        'identity_number',
        'identity_type',
        'phone_number',
        'education_level',
        'identity_photo_path',
        'portfolio_path',
        'description',
        'long_description',
        'terms_agreed',
        'privacy_agreed',
        'status',
        'rejection_reason',
        'submitted_at',
        'reviewed_at',
        'reviewed_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'terms_agreed' => 'boolean',
        'privacy_agreed' => 'boolean',
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the tutor profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the admin who reviewed this profile.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Check if the profile is complete.
     */
    public function isComplete(): bool
    {
        return !empty($this->full_name) &&
               !empty($this->public_name) &&
               !empty($this->identity_number) &&
               !empty($this->phone_number) &&
               !empty($this->education_level) &&
               $this->terms_agreed &&
               $this->privacy_agreed;
    }

    /**
     * Check if the profile is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the profile is pending review.
     */
    public function isPending(): bool
    {
        return in_array($this->status, ['submitted', 'under_review']);
    }

    /**
     * Check if the profile is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get status badge color for UI.
     */
    public function getStatusBadgeColor(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'submitted' => 'blue',
            'under_review' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get status label in Indonesian.
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'draft' => 'Draft',
            'submitted' => 'Diajukan',
            'under_review' => 'Sedang Ditinjau',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak',
            default => 'Tidak Diketahui'
        };
    }

    /**
     * Get education level options.
     */
    public static function getEducationLevels(): array
    {
        return [
            'SMA/SMK' => 'SMA/SMK',
            'D3' => 'Diploma 3 (D3)',
            'S1' => 'Sarjana (S1)',
            'S2' => 'Magister (S2)',
            'S3' => 'Doktor (S3)',
        ];
    }

    /**
     * Get identity type options.
     */
    public static function getIdentityTypes(): array
    {
        return [
            'KTP' => 'KTP',
            'SIM' => 'SIM',
            'Passport' => 'Passport',
        ];
    }

    /**
     * Generate a unique slug from public name.
     */
    public function generateSlug(string $publicName): string
    {
        $baseSlug = Str::slug($publicName);
        $slug = $baseSlug;
        $counter = 1;

        // Check if slug already exists (excluding current record)
        while (static::where('public_name_slug', $slug)
                     ->where('id', '!=', $this->id ?? '')
                     ->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Set the public name and automatically generate slug.
     */
    public function setPublicNameAttribute($value)
    {
        $this->attributes['public_name'] = $value;
        if (!empty($value)) {
            $this->attributes['public_name_slug'] = $this->generateSlug($value);
        }
    }

    /**
     * Get the public profile URL.
     */
    public function getPublicUrlAttribute(): string
    {
        return url('/tutor/' . $this->public_name_slug);
    }

    /**
     * Scope to find by slug.
     */
    public function scopeBySlug($query, $slug)
    {
        return $query->where('public_name_slug', $slug);
    }
}
