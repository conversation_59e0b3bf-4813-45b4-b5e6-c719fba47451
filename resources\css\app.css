@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 14 100% 57%;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 14 100% 57%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --radius: 0.5rem;
}

.dark {
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
}

/* Custom Component Classes */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply hover:bg-accent hover:text-accent-foreground;
  }

  .btn-success {
    @apply bg-green-500 text-white hover:bg-green-600 focus:ring-green-500;
    background-color: #10b981 !important;
  }

  .btn-lg {
    @apply h-11 rounded-md px-8;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .select {
    @apply flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border;
  }

  /* Primary color utilities */
  .text-primary {
    color: var(--primary);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .border-primary {
    border-color: var(--primary);
  }

  .bg-primary\/10 {
    background-color: rgba(255, 107, 53, 0.1);
  }

  .bg-primary\/20 {
    background-color: rgba(255, 107, 53, 0.2);
  }

  /* Gradient text */
  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--primary), #ef4444);
  }

  /* Custom animations */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Responsive utilities */
  @media (max-width: 768px) {
    .mobile-menu.hidden {
      display: none;
    }
    
    .mobile-menu {
      display: block;
    }
  }

  /* Hover effects */
  .hover\:shadow-lg:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Focus states */
  .focus\:ring-primary:focus {
    --tw-ring-color: var(--primary);
  }

  .focus\:border-primary:focus {
    border-color: var(--primary);
  }

  .course-tab.active {
    @apply bg-primary text-primary-foreground;
  }

  .course-tab {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
  }

  /* Sidebar styles */
  .sidebar-link {
    @apply flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-150 ease-in-out;
  }

  .sidebar-link.active {
    @apply bg-primary text-white;
  }

  .sidebar-link svg {
    @apply mr-3 flex-shrink-0;
  }

  .sidebar-link.active svg {
    @apply text-white;
  }

  /* Tutor Sidebar styles */
  .tutor-sidebar-link {
    @apply flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-emerald-50 hover:text-emerald-900 transition-colors duration-150 ease-in-out;
  }

  .tutor-sidebar-link.active {
    @apply bg-gradient-to-r from-emerald-600 to-teal-600 text-white shadow-md;
  }

  .tutor-sidebar-link svg {
    @apply mr-3 flex-shrink-0;
  }

  .tutor-sidebar-link.active svg {
    @apply text-white;
  }

  /* Button improvements */
  .btn {
    @apply h-10 px-4 py-2;
  }

  .btn-sm {
    @apply h-8 px-3 py-1 text-sm;
  }

  .btn-lg {
    @apply h-12 px-6 py-3 text-base;
  }

  /* User Dropdown Styles - Enhanced for minimalist design */
  .user-dropdown-toggle {
    @apply cursor-pointer transition-all duration-200 ease-in-out;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .user-dropdown-toggle:hover {
    @apply shadow-md;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .dropdown-chevron {
    @apply transition-transform duration-200 ease-in-out;
  }

  .user-dropdown-menu {
    transform-origin: top right;
    min-width: 16rem;
    z-index: 9999 !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(8px);
  }

  /* Clean hover effects for dropdown items */
  .user-dropdown-menu a:hover,
  .user-dropdown-menu button:hover {
    @apply bg-gray-50;
    transition: background-color 150ms ease-in-out;
  }

  /* Mobile Menu Styles - Enhanced minimalist design */
  .mobile-menu {
    @apply transition-all duration-300 ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .mobile-nav-link {
    @apply transition-all duration-200 ease-in-out;
  }

  .mobile-nav-link:hover {
    @apply transform translate-x-1;
  }

  .mobile-nav-icon {
    @apply transition-all duration-200 ease-in-out;
  }

  .mobile-action-btn {
    @apply transition-all duration-200 ease-in-out transform;
  }

  .mobile-action-btn:hover {
    @apply scale-105;
  }

  .mobile-action-btn:active {
    @apply scale-95;
  }

  .mobile-logout-btn {
    @apply transition-all duration-200 ease-in-out;
  }

  .mobile-logout-btn:hover {
    @apply bg-red-50 text-red-700;
  }

  /* Tablet responsive improvements for header */
  @media (min-width: 768px) and (max-width: 1024px) {
    /* Header optimizations - now with hamburger menu */
    .header-container {
      @apply px-4;
    }

    /* Logo and brand name optimization - more space available */
    .header-logo {
      @apply space-x-3;
    }

    .header-logo img {
      @apply w-9 h-9;
    }

    .header-logo span {
      @apply text-xl font-bold;
    }

    /* User dropdown optimization */
    .user-dropdown-toggle {
      @apply px-3 py-2 space-x-2;
    }

    .user-dropdown-toggle .user-avatar {
      @apply w-7 h-7 text-xs;
    }

    .user-dropdown-toggle .user-info {
      @apply hidden;
    }

    .user-dropdown-toggle .user-name-tablet {
      @apply block text-sm font-medium max-w-24 truncate;
    }

    /* Auth buttons optimization */
    .auth-buttons {
      @apply space-x-3;
    }

    .auth-buttons a {
      @apply px-3 py-2 text-sm;
    }

    /* Dropdown menu optimization */
    .user-dropdown-menu {
      @apply w-60;
    }

    /* Mobile menu styling for tablet - enhanced */
    .mobile-menu {
      @apply bg-white shadow-lg;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Hamburger button styling - enhanced */
    .mobile-menu-toggle {
      @apply p-2 rounded-lg hover:bg-gray-100 transition-all duration-200;
    }

    .mobile-menu-toggle:hover {
      @apply bg-gray-100 shadow-sm;
    }

    /* Tablet mobile menu improvements - grid layout for navigation */
    .mobile-menu nav {
      @apply md:grid md:grid-cols-2 md:gap-2;
    }

    .mobile-nav-link {
      @apply md:text-center md:py-4;
    }

    /* Tablet user section improvements */
    .mobile-menu .space-y-2 {
      @apply md:grid md:grid-cols-2 md:gap-2 md:space-y-0;
    }

    .mobile-action-btn {
      @apply md:text-center;
    }
  }

  /* Smaller tablet optimizations (768px-900px) */
  @media (min-width: 768px) and (max-width: 900px) {
    /* Make auth buttons more compact on smaller tablets */
    .auth-buttons {
      @apply space-x-2;
    }

    .auth-buttons a {
      @apply px-2 py-1.5 text-sm;
    }

    /* Hide user name on smaller tablets */
    .user-dropdown-toggle .user-name-tablet {
      @apply hidden;
    }

    /* Slightly reduce logo size */
    .header-logo img {
      @apply w-8 h-8;
    }

    .header-logo span {
      @apply text-lg;
    }
  }

  /* Mobile responsive improvements */
  @media (max-width: 640px) {
    .course-tab {
      @apply text-sm px-3 py-2;
    }

    .course-tab span {
      @apply hidden;
    }

    .course-tab svg {
      @apply w-5 h-5;
    }

    /* Mobile header improvements */
    .header-container {
      @apply px-3;
    }

    .header-logo img {
      @apply w-8 h-8;
    }

    .header-logo span {
      @apply text-lg;
    }

    /* Mobile menu full width */
    .mobile-menu {
      @apply mx-0;
    }

    .mobile-nav-link {
      @apply px-3 py-3;
    }

    .mobile-action-btn {
      @apply py-3;
    }

    /* Hamburger button mobile optimization */
    .mobile-menu-toggle {
      @apply p-1.5;
    }

    /* User dropdown mobile adjustments */
    .user-dropdown-menu {
      @apply w-72 right-0;
      max-width: calc(100vw - 2rem);
    }
  }

  /* Extra small mobile devices */
  @media (max-width: 480px) {
    .header-logo span {
      @apply text-base;
    }

    .mobile-menu {
      @apply px-3;
    }

    .user-dropdown-menu {
      @apply w-64;
      max-width: calc(100vw - 1rem);
    }
  }

  /* Ensure proper container behavior */
  .max-w-7xl {
    @apply w-full;
  }

  /* Additional smooth animations */
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateY(0);
    }
    to {
      opacity: 0;
      transform: translateY(-10px);
    }
  }

  .mobile-menu:not(.hidden) {
    animation: slideDown 0.3s ease-out;
  }

  /* Focus states for accessibility */
  .mobile-nav-link:focus,
  .mobile-action-btn:focus,
  .user-dropdown-toggle:focus {
    @apply outline-none ring-2 ring-primary ring-opacity-50;
  }
}

/* Utility Classes */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}