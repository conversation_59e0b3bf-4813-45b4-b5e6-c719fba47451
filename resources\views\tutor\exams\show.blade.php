@extends('layouts.tutor')

@section('title', $exam->title . ' - <PERSON><PERSON>')

@section('content')
<div class="tutor-dashboard-container tutor-exam-show-mobile p-3 md:p-6 bg-gradient-to-br from-emerald-50/30 via-white to-teal-50/30 min-h-screen">
    <!-- <PERSON> Header -->
    <div class="tutor-welcome-header mb-6 md:mb-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <div class="flex items-center space-x-2 md:space-x-3 mb-2">
                    <a href="{{ route('tutor.exams') }}" class="text-gray-500 hover:text-gray-700 tutor-touch-friendly p-1">
                        <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-lg md:text-2xl font-bold text-gray-900 truncate">{{ $exam->title }}</h1>
                    <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 md:py-1 rounded-full text-xs font-medium {{ $exam->is_published ? 'bg-green-100 text-green-800 border-green-200' : 'bg-yellow-100 text-yellow-800 border-yellow-200' }} border">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            @if($exam->is_published)
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            @else
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            @endif
                        </svg>
                        {{ $exam->is_published ? 'Terpublikasi' : 'Draft' }}
                    </span>
                </div>
                <p class="text-gray-600 mt-1 text-sm md:text-base">{{ $exam->description }}</p>
            </div>

            <!-- Mobile: Vertical Button Layout -->
            <div class="tutor-header-actions flex flex-col md:flex-row gap-2 md:gap-3">
                <a href="{{ route('tutor.exams.edit', $exam) }}" class="btn bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all tutor-touch-friendly order-1 text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Ujian
                </a>
                <form action="{{ route('tutor.exams.toggle-publish', $exam) }}" method="POST" class="order-2">
                    @csrf
                    <button type="submit" class="btn {{ $exam->is_published ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-emerald-600 hover:bg-emerald-700' }} text-white shadow-lg hover:shadow-xl transition-all tutor-touch-friendly w-full text-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($exam->is_published)
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            @endif
                        </svg>
                        {{ $exam->is_published ? 'Sembunyikan' : 'Publikasikan' }}
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="tutor-stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-emerald-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Soal</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">{{ $stats['total_questions'] }}</p>
                    <div class="flex items-center mt-1 text-xs text-emerald-600">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span>Soal tersedia</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-teal-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Poin</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">{{ $stats['total_points'] }}</p>
                    <div class="flex items-center mt-1 text-xs text-teal-600">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span>Maksimal skor</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-blue-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Total Peserta</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">{{ $stats['total_enrollments'] }}</p>
                    <div class="flex items-center mt-1 text-xs text-blue-600">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span>Terdaftar ujian</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="tutor-stats-card bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200 hover:shadow-md transition-shadow border-l-4 border-l-green-500">
            <div class="flex items-center">
                <div class="w-8 h-8 md:w-12 md:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 md:w-6 md:h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <div class="ml-3 md:ml-4 flex-1">
                    <p class="text-xs md:text-sm font-medium text-gray-600">Tingkat Kelulusan</p>
                    <p class="stat-number text-lg md:text-2xl font-bold text-gray-900">{{ number_format($stats['pass_rate'], 1) }}%</p>
                    <div class="flex items-center mt-1 text-xs text-green-600">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        <span>Peserta lulus</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-4 md:space-y-6">
            <!-- Exam Information -->
            <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                <h2 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Informasi Ujian</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->category->name ?? 'Tidak ada kategori' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tingkat Kesulitan</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->difficulty_label }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Harga</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->formatted_price }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Batas Waktu</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->time_limit }} menit</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Maksimal Percobaan</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->max_attempts }} kali</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nilai Lulus</label>
                        <p class="text-xs md:text-sm text-gray-900">{{ $exam->passing_score }}%</p>
                    </div>
                </div>

                @if($exam->instructions)
                    <div class="mt-4 md:mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instruksi Ujian</label>
                        <p class="text-xs md:text-sm text-gray-900 bg-gray-50 p-3 rounded-lg">{{ $exam->instructions }}</p>
                    </div>
                @endif

                <div class="mt-4 md:mt-6 flex flex-wrap gap-2">
                    @if($exam->shuffle_questions)
                        <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                            </svg>
                            Soal Diacak
                        </span>
                    @endif
                    @if($exam->show_results_immediately)
                        <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Hasil Langsung
                        </span>
                    @endif
                    @if($exam->certificate_enabled)
                        <span class="inline-flex items-center px-2 py-0.5 md:px-2.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                            Sertifikat
                        </span>
                    @endif
                </div>
            </div>

            <!-- Questions List -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Daftar Soal ({{ $exam->questions->count() }})</h2>
                </div>

                @if($exam->questions->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($exam->questions as $index => $question)
                            <div class="p-6">
                                <div class="flex items-start justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="w-8 h-8 bg-emerald-100 text-emerald-700 rounded-full flex items-center justify-center text-sm font-medium">
                                            {{ $index + 1 }}
                                        </span>
                                        <div>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                {{ ucfirst(str_replace('_', ' ', $question->type)) }}
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                                                {{ $question->points }} poin
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <p class="text-gray-900 font-medium">{{ $question->question }}</p>
                                </div>

                                @if($question->type === 'multiple_choice' || $question->type === 'true_false')
                                    <div class="space-y-2 mb-4">
                                        @foreach($question->options as $optionIndex => $option)
                                            <div class="flex items-center space-x-2">
                                                <span class="w-6 h-6 {{ $option->is_correct ? 'bg-green-100 text-green-700 border-green-300' : 'bg-gray-100 text-gray-700 border-gray-300' }} border rounded-full flex items-center justify-center text-xs font-medium">
                                                    {{ chr(65 + $optionIndex) }}
                                                </span>
                                                <span class="text-sm {{ $option->is_correct ? 'text-green-900 font-medium' : 'text-gray-700' }}">
                                                    {{ $option->option_text }}
                                                    @if($option->is_correct)
                                                        <svg class="w-4 h-4 text-green-600 inline ml-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    @endif
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif

                                @if($question->explanation)
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                        <div class="flex items-start">
                                            <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <div>
                                                <p class="text-sm font-medium text-blue-900 mb-1">Penjelasan:</p>
                                                <p class="text-sm text-blue-800">{{ $question->explanation }}</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum ada soal</h3>
                        <p class="text-gray-600 mb-6">Tambahkan soal untuk ujian ini</p>
                        <a href="{{ route('tutor.exams.edit', $exam) }}" class="btn bg-emerald-600 hover:bg-emerald-700 text-white">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Tambah Soal
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4 md:space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Aksi Cepat</h3>
                <div class="flex flex-col gap-3">
                    <a href="{{ route('tutor.exams.edit', $exam) }}" class="w-full btn bg-blue-600 hover:bg-blue-700 text-white tutor-touch-friendly">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Ujian
                    </a>

                    <form action="{{ route('tutor.exams.toggle-publish', $exam) }}" method="POST">
                        @csrf
                        <button type="submit" class="w-full btn {{ $exam->is_published ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-emerald-600 hover:bg-emerald-700' }} text-white tutor-touch-friendly">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @if($exam->is_published)
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                                @else
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                @endif
                            </svg>
                            {{ $exam->is_published ? 'Sembunyikan' : 'Publikasikan' }}
                        </button>
                    </form>

                    <form action="{{ route('tutor.exams.destroy', $exam) }}" method="POST" onsubmit="return confirm('Apakah Anda yakin ingin menghapus ujian ini? Tindakan ini tidak dapat dibatalkan.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full btn bg-red-600 hover:bg-red-700 text-white tutor-touch-friendly">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Hapus Ujian
                        </button>
                    </form>
                </div>
            </div>

            <!-- Recent Activity -->
            @if($stats['total_attempts'] > 0)
                <div class="bg-white rounded-lg shadow-sm p-4 md:p-6 border border-gray-200">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Statistik Ujian</h3>
                    <div class="space-y-3 md:space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-xs md:text-sm text-gray-600">Total Percobaan</span>
                            <span class="text-xs md:text-sm font-medium text-gray-900">{{ $stats['total_attempts'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs md:text-sm text-gray-600">Rata-rata Skor</span>
                            <span class="text-xs md:text-sm font-medium text-gray-900">{{ number_format($stats['average_score'], 1) }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs md:text-sm text-gray-600">Tingkat Kelulusan</span>
                            <span class="text-xs md:text-sm font-medium text-gray-900">{{ number_format($stats['pass_rate'], 1) }}%</span>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Help -->
            <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg shadow-sm p-4 md:p-6 border border-emerald-200">
                <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Tips Ujian</h3>
                <ul class="space-y-2 text-xs md:text-sm text-gray-600">
                    <li class="flex items-start">
                        <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Publikasikan ujian agar dapat diakses siswa
                    </li>
                    <li class="flex items-start">
                        <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Monitor statistik untuk evaluasi
                    </li>
                    <li class="flex items-start">
                        <svg class="w-3 h-3 md:w-4 md:h-4 text-emerald-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Berikan penjelasan untuk setiap jawaban
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection