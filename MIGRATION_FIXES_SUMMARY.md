# Migration Fixes Summary

## Issue
The `php artisan migrate:refresh --seed` command was failing with multiple `SQLSTATE[42S21]: Column already exists` and `SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP COLUMN` errors.

## Root Cause
Several migrations were attempting to:
1. Add columns that already existed
2. Drop columns that didn't exist
3. Add/drop indexes without checking if they already exist

## Fixed Migrations

### 1. `2025_06_24_012112_remove_role_columns_from_users_table.php`
**Issue**: Trying to add `is_tutor`, `is_admin`, `is_superadmin` columns that already existed
**Fix**: Added `Schema::hasColumn()` checks before adding/dropping columns

### 2. `2025_06_09_080101_add_agreement_fields_to_users_table.php`
**Issue**: Trying to drop `terms_agreed`, `privacy_agreed`, `agreement_date` columns that didn't exist
**Fix**: Added `Schema::hasColumn()` checks before adding/dropping columns

### 3. `2025_06_02_081932_add_question_order_to_exam_attempts_table.php`
**Issue**: Trying to drop `question_order` column that didn't exist
**Fix**: Added `Schema::hasColumn()` checks before adding/dropping the column

### 4. `2014_10_12_200000_add_two_factor_columns_to_users_table.php`
**Issue**: Trying to drop two-factor authentication columns that didn't exist
**Fix**: Added `Schema::hasColumn()` checks before adding/dropping columns

### 5. `2025_01_27_150000_add_performance_indexes_to_nala_chat_tables.php`
**Issue**: Trying to drop indexes that didn't exist, and Doctrine DBAL dependency missing
**Fix**: Replaced complex index checking with try-catch blocks to handle existing/missing indexes gracefully

## Solution Pattern

For **column operations**:
```php
// Adding columns
if (!Schema::hasColumn('table_name', 'column_name')) {
    $table->columnType('column_name')->options();
}

// Dropping columns
$columnsToRemove = [];
if (Schema::hasColumn('table_name', 'column_name')) {
    $columnsToRemove[] = 'column_name';
}
if (!empty($columnsToRemove)) {
    $table->dropColumn($columnsToRemove);
}
```

For **index operations**:
```php
// Adding/dropping indexes with try-catch
try {
    $table->index(['column1', 'column2'], 'index_name');
} catch (\Exception $e) {
    // Index might already exist, ignore
}

try {
    $table->dropIndex('index_name');
} catch (\Exception $e) {
    // Index might not exist, ignore
}
```

## Result
- ✅ `php artisan migrate:refresh --seed` now runs successfully
- ✅ All course progress consistency tests pass
- ✅ Database is properly seeded with test data
- ✅ Application is ready for testing

## Verification
```bash
php artisan migrate:refresh --seed
php artisan test tests/Feature/CourseProgressConsistencyTest.php
```

Both commands now complete successfully without errors.