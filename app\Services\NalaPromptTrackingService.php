<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class NalaPromptTrackingService
{
    /**
     * Daily prompt limits by membership level
     */
    const PROMPT_LIMITS = [
        'free' => 10,
        'basic' => 100,
        'standard' => 300,
        'pro' => 400
    ];

    /**
     * Check if user can use Nala prompts
     */
    public function canUsePrompts(User $user, int $count = 1): bool
    {
        // Check if user has active membership
        $activeMembership = $user->activeMembership;
        
        if ($activeMembership) {
            return $activeMembership->canUseNalaPrompts($count);
        }

        // For free users, check against free limit
        return $this->canFreeUserUsePrompts($user, $count);
    }

    /**
     * Use Nala prompts and track usage
     */
    public function usePrompts(User $user, int $count = 1): bool
    {
        // Check if user has active membership
        $activeMembership = $user->activeMembership;
        
        if ($activeMembership) {
            $success = $activeMembership->useNalaPrompts($count);
            
            if ($success) {
                // Update user's cached remaining prompts
                $user->update([
                    'nala_prompts_remaining' => $activeMembership->nala_prompts_remaining
                ]);
                
                Log::info("Nala prompts used", [
                    'user_id' => $user->id,
                    'count' => $count,
                    'membership_level' => $this->getUserMembershipLevel($user),
                    'remaining' => $activeMembership->nala_prompts_remaining
                ]);
            }
            
            return $success;
        }

        // For free users, track usage differently
        return $this->useFreeUserPrompts($user, $count);
    }

    /**
     * Get user's membership level
     */
    public function getUserMembershipLevel(User $user): string
    {
        $activeMembership = $user->activeMembership;

        if (!$activeMembership) {
            return 'free';
        }

        $activeMembership->load('membershipPlan');
        return $activeMembership->membershipPlan->slug ?? 'free';
    }

    /**
     * Get user's remaining prompts for today
     */
    public function getRemainingPrompts(User $user): int
    {
        $activeMembership = $user->activeMembership;
        
        if ($activeMembership) {
            $activeMembership->resetDailyNalaPromptsIfNeeded();
            return $activeMembership->nala_prompts_remaining;
        }

        // For free users, calculate from daily usage
        return $this->getFreeUserRemainingPrompts($user);
    }

    /**
     * Get membership limit response for when user hits daily limit
     */
    public function getMembershipLimitResponse(string $membershipLevel): string
    {
        $responses = [
            'free' => 'Anda telah mencapai batas harian untuk pengguna gratis (10 prompt/hari). Upgrade ke membership Basic untuk mendapatkan 100 prompt/hari!',
            'basic' => 'Anda telah mencapai batas harian membership Basic (100 prompt/hari). Upgrade ke Standard untuk mendapatkan 300 prompt/hari!',
            'standard' => 'Anda telah mencapai batas harian membership Standard (300 prompt/hari). Upgrade ke Pro untuk mendapatkan 400 prompt/hari!',
            'pro' => 'Anda telah mencapai batas harian membership Pro (400 prompt/hari). Batas akan direset besok!'
        ];

        return $responses[$membershipLevel] ?? $responses['free'];
    }

    /**
     * Check if free user can use prompts
     */
    private function canFreeUserUsePrompts(User $user, int $count = 1): bool
    {
        $limit = self::PROMPT_LIMITS['free'];
        $todayUsage = $this->getFreeUserTodayUsage($user);
        
        return ($todayUsage + $count) <= $limit;
    }

    /**
     * Use prompts for free user
     */
    private function useFreeUserPrompts(User $user, int $count = 1): bool
    {
        if (!$this->canFreeUserUsePrompts($user, $count)) {
            return false;
        }

        // For free users, we track usage in the nala_chat_messages table
        // This is already handled by the existing chat system
        // We just need to ensure consistency
        
        Log::info("Free user Nala prompts used", [
            'user_id' => $user->id,
            'count' => $count,
            'membership_level' => 'free'
        ]);

        return true;
    }

    /**
     * Get free user's remaining prompts
     */
    private function getFreeUserRemainingPrompts(User $user): int
    {
        $limit = self::PROMPT_LIMITS['free'];
        $todayUsage = $this->getFreeUserTodayUsage($user);
        
        return max(0, $limit - $todayUsage);
    }

    /**
     * Get free user's today usage from chat messages
     */
    private function getFreeUserTodayUsage(User $user): int
    {
        $today = now()->format('Y-m-d');

        return DB::table('nala_chat_messages')
            ->join('nala_chat_conversations', 'nala_chat_messages.conversation_id', '=', 'nala_chat_conversations.id')
            ->where('nala_chat_conversations.user_id', $user->id)
            ->where('nala_chat_messages.sender', 'user')
            ->whereDate('nala_chat_messages.created_at', $today)
            ->count();
    }

    /**
     * Get prompt usage statistics for user
     */
    public function getUsageStatistics(User $user): array
    {
        $membershipLevel = $this->getUserMembershipLevel($user);
        $remaining = $this->getRemainingPrompts($user);
        $limit = self::PROMPT_LIMITS[$membershipLevel] ?? self::PROMPT_LIMITS['free'];
        $used = $limit - $remaining;

        return [
            'membership_level' => $membershipLevel,
            'daily_limit' => $limit,
            'used_today' => $used,
            'remaining_today' => $remaining,
            'can_use_prompts' => $remaining > 0,
            'limit_message' => $remaining <= 0 ? $this->getMembershipLimitResponse($membershipLevel) : null
        ];
    }
}
