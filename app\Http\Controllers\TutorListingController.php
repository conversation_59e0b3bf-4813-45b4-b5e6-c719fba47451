<?php

namespace App\Http\Controllers;

use App\Models\TutorProfile;
use App\Models\Course;
use App\Models\Exam;
use App\Models\BlogPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class TutorListingController extends Controller
{
    /**
     * Display the public tutor listing page.
     */
    public function index(Request $request)
    {
        // Start building the query for approved tutors
        $query = TutorProfile::with(['user'])
            ->where('status', 'approved')
            ->whereHas('user', function ($userQuery) {
                $userQuery->where('tutor_status', 'approved');
            });

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('public_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('long_description', 'like', "%{$search}%")
                  ->orWhere('education_level', 'like', "%{$search}%")
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('job_title', 'like', "%{$search}%")
                               ->orWhere('company', 'like', "%{$search}%")
                               ->orWhere('location', 'like', "%{$search}%");
                  });
            });
        }

        // Apply education level filter
        if ($request->filled('education_level')) {
            $query->where('education_level', $request->education_level);
        }

        // Apply sorting (simplified for now)
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'name':
                $query->orderBy('public_name', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // Get tutors with pagination (12 items per page for better grid layout)
        $tutors = $query->paginate(12)->appends($request->query());

        // Enhance tutors with additional statistics
        foreach ($tutors as $tutor) {
            // Get tutor's published courses count
            $coursesCount = Course::where('tutor_id', $tutor->user_id)
                ->published()
                ->count();

            // Get tutor's published exams count
            $examsCount = Exam::where('tutor_id', $tutor->user_id)
                ->where('is_published', true)
                ->count();

            // Get tutor's published blog posts count
            $blogPostsCount = BlogPost::where('author_id', $tutor->user_id)
                ->published()
                ->count();

            // Get total students across all courses
            $totalStudents = Course::where('tutor_id', $tutor->user_id)
                ->published()
                ->sum('total_students');

            // Get average rating across all courses
            $averageRating = Course::where('tutor_id', $tutor->user_id)
                ->published()
                ->avg('average_rating');

            // Add statistics to tutor object
            $tutor->stats = [
                'total_courses' => $coursesCount,
                'total_exams' => $examsCount,
                'total_blog_posts' => $blogPostsCount,
                'total_students' => $totalStudents ?: 0,
                'average_rating' => $averageRating ? round($averageRating, 1) : 0,
            ];
        }

        // Get unique education levels for filter dropdown
        $educationLevels = TutorProfile::where('status', 'approved')
            ->whereNotNull('education_level')
            ->distinct()
            ->pluck('education_level')
            ->sort()
            ->values();

        // Get platform statistics for hero section
        $platformStats = Cache::remember('tutor_listing_stats', 3600, function () {
            return [
                'total_tutors' => TutorProfile::where('status', 'approved')
                    ->whereHas('user', function ($userQuery) {
                        $userQuery->where('tutor_status', 'approved');
                    })->count(),
                'total_courses' => Course::published()->count(),
                'total_students' => Course::published()->sum('total_students'),
                'average_rating' => round(Course::published()->avg('average_rating'), 1),
            ];
        });

        return view('tutors.index', compact(
            'tutors',
            'educationLevels',
            'platformStats'
        ));
    }
}
