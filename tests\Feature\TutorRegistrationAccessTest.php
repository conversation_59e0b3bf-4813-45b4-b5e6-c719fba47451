<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;

class TutorRegistrationAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Seed roles
        $this->seed(\Database\Seeders\RoleSeeder::class);
    }

    /** @test */
    public function existing_tutor_cannot_access_registration_terms_page()
    {
        // Create a user who is already a tutor
        $user = User::factory()->create([
            'tutor_status' => 'approved',
            'referral_code' => 'TEST123',
        ]);
        
        // Assign tutor role
        $user->syncRoles([Role::USER, Role::TUTOR]);
        
        // Verify the user is a tutor
        $this->assertTrue($user->isTutor());
        
        // Try to access the tutor registration terms page
        $response = $this->actingAs($user)->get('/tutor/register/terms');
        
        // Should be redirected to tutor dashboard
        $response->assertRedirect(route('tutor.dashboard'));
        $response->assertSessionHas('success', 'Anda sudah menjadi tutor yang disetujui.');
    }

    /** @test */
    public function non_tutor_can_access_registration_terms_page()
    {
        // Create a regular user (not a tutor)
        $user = User::factory()->create();
        $user->syncRoles([Role::USER]);
        
        // Verify the user is not a tutor
        $this->assertFalse($user->isTutor());
        
        // Try to access the tutor registration terms page
        $response = $this->actingAs($user)->get('/tutor/register/terms');
        
        // Should be able to access the page
        $response->assertStatus(200);
        $response->assertViewIs('tutor.register.step1-terms');
    }

    /** @test */
    public function guest_user_cannot_access_registration_terms_page()
    {
        // Try to access the tutor registration terms page as guest
        $response = $this->get('/tutor/register/terms');
        
        // Should be redirected to login
        $response->assertRedirect('/login');
    }
}
