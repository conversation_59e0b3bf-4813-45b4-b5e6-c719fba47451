<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Role;

class TutorAccessTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'user']);
        Role::create(['name' => 'tutor']);
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'superadmin']);
    }

    /** @test */
    public function guest_user_accessing_tutor_route_redirects_to_login()
    {
        $response = $this->get('/tutor/dashboard');

        // Guest users are redirected to login by <PERSON><PERSON>'s auth middleware
        $response->assertRedirect('/login');
        // The session message is set by <PERSON><PERSON>'s auth middleware, not our custom middleware
    }

    /** @test */
    public function authenticated_non_tutor_user_accessing_tutor_route_redirects_to_registration()
    {
        // Create a regular user (not a tutor)
        $user = User::factory()->create();
        $user->assignRole('user');
        
        $response = $this->actingAs($user)->get('/tutor/dashboard');
        
        $response->assertRedirect('/tutor/register/terms');
        $response->assertSessionHas('info', 'Untuk mengakses area tutor, Anda perlu mendaftar sebagai tutor terlebih dahulu.');
    }

    /** @test */
    public function tutor_user_can_access_tutor_routes()
    {
        // Create a tutor user
        $user = User::factory()->create();
        $user->assignRole('tutor');
        
        $response = $this->actingAs($user)->get('/tutor/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function admin_user_can_access_tutor_routes()
    {
        // Create an admin user
        $user = User::factory()->create();
        $user->assignRole('admin');
        
        $response = $this->actingAs($user)->get('/tutor/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function superadmin_user_can_access_tutor_routes()
    {
        // Create a superadmin user
        $user = User::factory()->create();
        $user->assignRole('superadmin');
        
        $response = $this->actingAs($user)->get('/tutor/dashboard');
        
        $response->assertStatus(200);
    }

    /** @test */
    public function custom_403_error_page_exists_and_contains_required_elements()
    {
        // Verify the error page exists
        $this->assertTrue(file_exists(resource_path('views/errors/403.blade.php')));

        // Verify the error page contains expected elements
        $errorPageContent = file_get_contents(resource_path('views/errors/403.blade.php'));

        $this->assertStringContainsString('Akses Ditolak', $errorPageContent);
        $this->assertStringContainsString('tutor.register.terms', $errorPageContent);
        $this->assertStringContainsString('user.dashboard', $errorPageContent);
        $this->assertStringContainsString('@auth', $errorPageContent);
        $this->assertStringContainsString('@else', $errorPageContent);
    }

    /** @test */
    public function middleware_redirect_logic_is_implemented()
    {
        // Test that the middleware file contains the expected redirect logic
        $middlewareFile = file_get_contents(app_path('Http/Middleware/IsTutor.php'));

        // Should contain redirect to tutor registration
        $this->assertStringContainsString('tutor.register.terms', $middlewareFile);

        // Should not contain abort(403) anymore
        $this->assertStringNotContainsString('abort(403', $middlewareFile);

        // Should contain the redirect logic
        $this->assertStringContainsString('redirect()', $middlewareFile);
    }
}
