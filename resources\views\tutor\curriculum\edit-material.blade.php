@extends('layouts.tutor')

@section('title', 'Edit Materi - ' . $lesson->title)

@section('content')
<div class="tutor-dashboard-container tutor-material-edit-mobile space-y-4 md:space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm p-4 md:p-6">
        <div class="tutor-welcome-header flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex-1">
                <h1 class="text-xl md:text-2xl font-bold text-gray-900">Edit Materi</h1>
                <p class="text-gray-600 mt-1 text-sm md:text-base">{{ $course->title }} - {{ $chapter->title }}</p>
                <div class="tutor-quick-stats-mobile flex flex-wrap items-center gap-2 md:gap-4 mt-2 text-sm text-gray-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ $course->category->name }}
                    </span>
                    <span class="flex items-center">
                        @if($course->is_free)
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">GRATIS</span>
                        @else
                            <span class="bg-emerald-600 text-white text-xs px-2 py-1 rounded">{{ $course->formatted_price }}</span>
                        @endif
                    </span>
                    <span class="flex items-center">
                        @if($lesson->is_published)
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Published</span>
                        @else
                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">Draft</span>
                        @endif
                    </span>
                </div>
            </div>
            <div class="tutor-header-actions flex">
                <a href="{{ route('tutor.curriculum.index', $course) }}" class="btn btn-outline tutor-touch-friendly w-full md:w-auto">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali ke Kurikulum
                </a>
            </div>
        </div>
    </div>

    <!-- Material Edit Form -->
    <div class="bg-white rounded-lg shadow-sm">
        <form action="{{ route('tutor.curriculum.update-lesson', [$course, $chapter, $lesson]) }}" method="POST" enctype="multipart/form-data" id="materialForm">
            @csrf
            @method('PUT')

            <div class="tutor-form-mobile p-4 md:p-6 space-y-4 md:space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Judul Materi <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="title" name="title" required value="{{ old('title', $lesson->title) }}"
                               class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: 1.1 Pengenalan Konsep Dasar">
                        @error('title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="duration_minutes" class="block text-sm font-medium text-gray-700 mb-2">
                            Estimasi Durasi (menit) <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="duration_minutes" name="duration_minutes" required min="1" max="300" value="{{ old('duration_minutes', $lesson->duration_minutes) }}"
                               class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="Contoh: 15">
                        <p class="text-xs text-gray-500 mt-1">Perkiraan waktu yang dibutuhkan untuk menyelesaikan materi ini</p>
                        @error('duration_minutes')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                    <textarea id="description" name="description" rows="3"
                              class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 resize-none"
                              placeholder="Deskripsi singkat tentang materi ini...">{{ old('description', $lesson->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Material Type Selection -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
                        Tipe Materi <span class="text-red-500">*</span>
                    </label>
                    <select id="type" name="type" required onchange="toggleMaterialFields()"
                            class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200">
                        <option value="">Pilih tipe materi</option>
                        <option value="video" {{ old('type', $lesson->type) == 'video' ? 'selected' : '' }}>Video</option>
                        <option value="text" {{ old('type', $lesson->type) == 'text' ? 'selected' : '' }}>Teks/Artikel</option>
                        <option value="quiz" {{ old('type', $lesson->type) == 'quiz' ? 'selected' : '' }}>Kuis</option>
                        <option value="assignment" {{ old('type', $lesson->type) == 'assignment' ? 'selected' : '' }}>Tugas</option>
                    </select>
                    @error('type')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Dynamic Content Areas -->
            <div class="border-t border-gray-200">
                <!-- Video Content -->
                <div id="video_content" class="hidden p-4 md:p-6 space-y-4 md:space-y-6">
                    <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Konten Video</h3>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Sumber Video</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                            <label class="flex items-center p-3 md:p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors tutor-touch-friendly">
                                <input type="radio" name="video_source" value="url" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300"
                                       onchange="toggleVideoSource()" {{ $lesson->video_url ? 'checked' : '' }}>
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">URL Video</span>
                                    <p class="text-xs text-gray-500">YouTube, Vimeo, dll</p>
                                </div>
                            </label>
                            <label class="flex items-center p-3 md:p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors tutor-touch-friendly">
                                <input type="radio" name="video_source" value="upload" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300"
                                       onchange="toggleVideoSource()" {{ $lesson->video_file ? 'checked' : '' }}>
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Upload Video</span>
                                    <p class="text-xs text-gray-500">File MP4, MOV, AVI</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- URL Input -->
                    <div id="video_url_input" class="{{ $lesson->video_url ? '' : 'hidden' }}">
                        <label for="video_url" class="block text-sm font-medium text-gray-700 mb-2">URL Video</label>
                        <input type="url" id="video_url" name="video_url" value="{{ old('video_url', $lesson->video_url) }}"
                               class="tutor-form-mobile w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                               placeholder="https://www.youtube.com/watch?v=...">
                        <p class="text-xs text-gray-500 mt-1">Mendukung YouTube, Vimeo, dan platform video lainnya</p>
                    </div>

                    <!-- File Upload -->
                    <div id="video_file_input" class="{{ $lesson->video_file ? '' : 'hidden' }}">
                        <label for="video_file" class="block text-sm font-medium text-gray-700 mb-2">Upload Video</label>

                        @if($lesson->video_file)
                            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-green-800">Video saat ini: {{ basename($lesson->video_file) }}</span>
                                </div>
                                <p class="text-xs text-green-600 mt-1">Upload video baru untuk mengganti yang lama</p>
                            </div>
                        @endif

                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary transition-colors">
                            <input type="file" id="video_file" name="video_file" accept="video/*" class="hidden" onchange="handleVideoFileSelect(this)">
                            <input type="hidden" id="uploaded_video_path" name="uploaded_video_path" value="{{ old('uploaded_video_path') }}">

                            <!-- Upload Area -->
                            <div id="video_upload_area" onclick="document.getElementById('video_file').click()" class="cursor-pointer">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-sm text-gray-600 mb-2">Klik untuk upload video baru atau drag & drop</p>
                                <p class="text-xs text-gray-500">MP4, MOV, AVI (Max: 100MB)</p>
                            </div>

                            <!-- Upload Success -->
                            <div id="video_file_info" class="hidden">
                                <div class="flex items-center justify-center space-x-2 mb-2">
                                    <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span id="video_file_name" class="text-sm text-gray-700"></span>
                                </div>
                                <div class="text-xs text-gray-500 mb-2">
                                    <span id="video_file_size"></span> • Upload berhasil
                                </div>
                                <button type="button" onclick="clearVideoFile()" class="text-xs text-red-600 hover:text-red-800">Hapus file</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text/Article Content -->
                <div id="text_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Konten Artikel</h3>

                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Konten</label>
                        <textarea id="content" name="content" rows="20"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200"
                                  placeholder="Tulis konten artikel di sini...">{{ old('content', $lesson->content) }}</textarea>
                        @error('content')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Quiz Content -->
                <div id="quiz_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Kuis</h3>
                    <div id="quiz_builder">
                        <!-- Quiz builder will be loaded here via JavaScript -->
                    </div>
                </div>

                <!-- Assignment Content -->
                <div id="assignment_content" class="hidden p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pengaturan Tugas</h3>
                    <div id="assignment_builder">
                        <!-- Assignment builder will be loaded here via JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="border-t border-gray-200 p-4 md:p-6">
                <h3 class="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">Pengaturan</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                    <div class="bg-blue-50 p-3 md:p-4 rounded-lg">
                        <label class="flex items-start tutor-touch-friendly">
                            <input type="checkbox" name="is_published" value="1" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1"
                                   {{ old('is_published', $lesson->is_published) ? 'checked' : '' }}>
                            <div class="ml-3">
                                <span class="text-sm font-medium text-gray-700">Publikasikan Materi</span>
                                <p class="text-xs text-gray-500 mt-1">Materi akan terlihat oleh siswa setelah dipublikasikan</p>
                            </div>
                        </label>
                    </div>

                    @if(!$course->is_free)
                        <div class="bg-green-50 p-3 md:p-4 rounded-lg">
                            <label class="flex items-start tutor-touch-friendly">
                                <input type="checkbox" name="is_preview" value="1" class="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded mt-1"
                                       {{ old('is_preview', $lesson->is_preview) ? 'checked' : '' }}>
                                <div class="ml-3">
                                    <span class="text-sm font-medium text-gray-700">Preview Materi</span>
                                    <p class="text-xs text-gray-500 mt-1">Dapat dilihat sebelum membeli kursus</p>
                                </div>
                            </label>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Form Actions -->
            <!-- Mobile: Vertical Button Layout (primary action first) -->
            <div class="border-t border-gray-200 px-4 md:px-6 py-3 md:py-4 bg-gray-50 rounded-b-lg">
                <div class="flex flex-col md:flex-row md:justify-end gap-3 md:gap-3 md:space-x-0">
                    <button type="submit" id="submit_btn"
                            class="px-6 py-2 text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors duration-200 flex items-center justify-center tutor-touch-friendly order-1">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span id="submit_text">Update Materi</span>
                    </button>
                    <a href="{{ route('tutor.curriculum.index', $course) }}"
                       class="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 rounded-lg transition-colors duration-200 tutor-touch-friendly order-2">
                        Batal
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- CSV Import Modal -->
<div id="csvImportModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
    <div class="flex items-center justify-center min-h-screen px-4 py-8">
        <div class="relative w-full max-w-2xl bg-white rounded-xl shadow-2xl overflow-hidden">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-white">Import Soal Kuis dari CSV</h3>
                    <button type="button" onclick="closeImportModal()" class="text-white hover:text-gray-200 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <!-- Instructions -->
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <h4 class="font-medium text-blue-900 mb-2">Cara Import Soal Kuis:</h4>
                    <ol class="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                        <li>Download template CSV (sudah berisi panduan dan contoh)</li>
                        <li>Buka dengan Excel/Google Sheets, hapus contoh, isi soal Anda</li>
                        <li>Simpan sebagai CSV dan upload di sini</li>
                        <li>Preview soal, lalu klik Import untuk menambahkan ke kuis</li>
                    </ol>
                    <div class="mt-3 p-2 bg-blue-100 rounded text-xs text-blue-700">
                        <strong>Tips:</strong> Template sudah berisi panduan lengkap dan contoh untuk setiap tipe soal (Pilihan Ganda, Benar/Salah, Jawaban Singkat)
                    </div>
                </div>

                <!-- Download Template -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">1. Download Template CSV</label>
                    <a href="{{ route('tutor.curriculum.download-quiz-template') }}"
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Template
                    </a>
                    <p class="text-xs text-gray-500 mt-1">Template berisi panduan lengkap, format yang benar, dan contoh soal untuk setiap tipe</p>
                </div>

                <!-- Upload CSV -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">2. Upload File CSV</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                        <input type="file" id="csvFile" accept=".csv" class="hidden" onchange="handleCSVFile(this)">
                        <div id="csvUploadArea" onclick="document.getElementById('csvFile').click()" class="cursor-pointer">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="mt-4">
                                <p class="text-sm text-gray-600">
                                    <span class="font-medium text-blue-600 hover:text-blue-500">Klik untuk upload</span>
                                    atau drag & drop file CSV
                                </p>
                                <p class="text-xs text-gray-500">CSV hingga 2MB</p>
                            </div>
                        </div>
                        <div id="csvFileInfo" class="hidden mt-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900" id="csvFileName"></p>
                                        <p class="text-xs text-gray-500" id="csvFileSize"></p>
                                    </div>
                                </div>
                                <button type="button" onclick="clearCSVFile()" class="text-red-600 hover:text-red-800">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div id="csvPreview" class="hidden mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">3. Preview Soal</label>
                    <div class="border border-gray-200 rounded-lg p-4 max-h-60 overflow-y-auto">
                        <div id="csvPreviewContent"></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                <button type="button" onclick="closeImportModal()"
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    Batal
                </button>
                <button type="button" onclick="importQuestions()" id="importBtn" disabled
                        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                    Import Soal
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<!-- TinyMCE CSS -->
<style>
    .tox-tinymce {
        border-radius: 0.5rem !important;
        border-color: #d1d5db !important;
    }
    .tox-editor-header {
        border-radius: 0.5rem 0.5rem 0 0 !important;
    }
    .tox-edit-area {
        border-radius: 0 0 0.5rem 0.5rem !important;
    }
</style>
@endpush

@push('scripts')
<!-- TinyMCE -->
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>

<script>
// Initialize TinyMCE
function initializeTinyMCE() {
    tinymce.init({
        selector: '#content',
        height: 500,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
                'bold italic forecolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
        content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }',
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }
    });
}

// Toggle material type fields
function toggleMaterialFields() {
    const type = document.getElementById('type').value;

    // Hide all content areas
    document.getElementById('video_content').classList.add('hidden');
    document.getElementById('text_content').classList.add('hidden');
    document.getElementById('quiz_content').classList.add('hidden');
    document.getElementById('assignment_content').classList.add('hidden');

    // Show relevant content area
    if (type === 'video') {
        document.getElementById('video_content').classList.remove('hidden');
        toggleVideoSource(); // Show appropriate video input
    } else if (type === 'text') {
        document.getElementById('text_content').classList.remove('hidden');
        // Initialize TinyMCE when text area is shown
        setTimeout(initializeTinyMCE, 100);
    } else if (type === 'quiz') {
        document.getElementById('quiz_content').classList.remove('hidden');
        loadQuizBuilder();
    } else if (type === 'assignment') {
        document.getElementById('assignment_content').classList.remove('hidden');
        loadAssignmentBuilder();
    }
}

// Toggle video source
function toggleVideoSource() {
    const urlRadio = document.querySelector('input[name="video_source"][value="url"]');
    const uploadRadio = document.querySelector('input[name="video_source"][value="upload"]');
    const urlInput = document.getElementById('video_url_input');
    const fileInput = document.getElementById('video_file_input');

    if (urlRadio && urlRadio.checked) {
        urlInput.classList.remove('hidden');
        fileInput.classList.add('hidden');
    } else if (uploadRadio && uploadRadio.checked) {
        urlInput.classList.add('hidden');
        fileInput.classList.remove('hidden');
    }
}

// Load quiz builder
function loadQuizBuilder() {
    // Get existing quiz data if available
    const existingQuiz = @json($lesson->quiz ?? null);

    document.getElementById('quiz_builder').innerHTML = `
        <div class="space-y-6">
            <!-- Quiz Settings -->
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-4">Pengaturan Kuis</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Batas Waktu (menit)</label>
                        <input type="number" name="quiz_time_limit" min="1" max="180"
                               value="${existingQuiz?.time_limit || ''}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="30">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal Percobaan</label>
                        <input type="number" name="quiz_max_attempts" min="1" max="10" value="${existingQuiz?.max_attempts || 3}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Nilai Lulus (%)</label>
                        <input type="number" name="quiz_passing_score" min="0" max="100" value="${existingQuiz?.passing_score || 70}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" name="quiz_shuffle_questions" id="quiz_shuffle"
                               ${existingQuiz?.shuffle_questions ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="quiz_shuffle" class="ml-2 text-sm text-gray-700">Acak urutan soal</label>
                    </div>
                </div>
            </div>

            <!-- Quiz Instructions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Kuis</label>
                <textarea name="quiz_instructions" rows="3"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan instruksi untuk kuis ini...">${existingQuiz?.description || ''}</textarea>
            </div>

            <!-- Questions Section -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium text-gray-900">Soal Kuis</h4>
                    <div class="flex items-center space-x-2">
                        <button type="button" onclick="showImportModal()"
                                class="btn btn-sm btn-outline">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            Import CSV
                        </button>
                        <button type="button" onclick="addQuizQuestion()"
                                class="btn btn-sm btn-primary">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            Tambah Soal
                        </button>
                    </div>
                </div>
                <div id="quiz_questions_container" class="space-y-4">
                    <!-- Questions will be added here -->
                </div>
            </div>
        </div>
    `;

    // Load existing questions if available
    if (existingQuiz && existingQuiz.questions && existingQuiz.questions.length > 0) {
        questionCounter = 0;
        existingQuiz.questions.forEach(question => {
            loadExistingQuizQuestion(question);
        });
    } else {
        // Add first question by default if no existing questions
        addQuizQuestion();
    }
}

// Load assignment builder
function loadAssignmentBuilder() {
    // Get existing assignment data if available
    const existingAssignment = @json($lesson->assignment ?? null);

    // Format due date for datetime-local input
    let formattedDueDate = '';
    if (existingAssignment?.due_date) {
        const date = new Date(existingAssignment.due_date);
        formattedDueDate = date.toISOString().slice(0, 16);
    }

    document.getElementById('assignment_builder').innerHTML = `
        <div class="space-y-6">
            <!-- Assignment Settings -->
            <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-4">Pengaturan Tugas</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Deadline</label>
                        <input type="datetime-local" name="assignment_deadline" required
                               value="${formattedDueDate}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal Poin</label>
                        <input type="number" name="assignment_max_points" min="1" max="1000" value="${existingAssignment?.max_points || 100}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Maksimal File</label>
                        <input type="number" name="assignment_max_files" min="1" max="10" value="${existingAssignment?.max_files || 3}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Ukuran File Max (MB)</label>
                        <input type="number" name="assignment_max_file_size" min="1" max="100" value="${existingAssignment?.max_file_size || 10}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <div class="mt-4 space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" name="assignment_allow_late" id="assignment_allow_late"
                               ${existingAssignment?.allow_late_submission ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="assignment_allow_late" class="ml-2 text-sm text-gray-700">Izinkan pengumpulan terlambat</label>
                    </div>
                    <div id="late_penalty_section" class="${existingAssignment?.allow_late_submission ? '' : 'hidden'} ml-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Penalti per hari (%)</label>
                        <input type="number" name="assignment_late_penalty" min="0" max="100" value="${existingAssignment?.late_penalty_percent || 10}"
                               class="w-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Assignment Instructions -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Instruksi Tugas</label>
                <textarea name="assignment_instructions" rows="4" required
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Jelaskan tugas yang harus dikerjakan siswa...">${existingAssignment?.description || ''}</textarea>
            </div>

            <!-- Assignment Requirements -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Persyaratan Tugas</label>
                <textarea name="assignment_requirements" rows="3"
                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Sebutkan persyaratan khusus untuk tugas ini...">${existingAssignment?.requirements || ''}</textarea>
            </div>

            <!-- File Types -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipe File yang Diizinkan</label>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="pdf"
                               ${existingAssignment?.allowed_file_types?.includes('pdf') !== false ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">PDF</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="doc"
                               ${existingAssignment?.allowed_file_types?.includes('doc') !== false ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">DOC</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="docx"
                               ${existingAssignment?.allowed_file_types?.includes('docx') !== false ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">DOCX</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="txt"
                               ${existingAssignment?.allowed_file_types?.includes('txt') ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">TXT</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="zip"
                               ${existingAssignment?.allowed_file_types?.includes('zip') ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">ZIP</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="jpg"
                               ${existingAssignment?.allowed_file_types?.includes('jpg') ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">JPG</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="png"
                               ${existingAssignment?.allowed_file_types?.includes('png') ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">PNG</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="assignment_file_types[]" value="mp4"
                               ${existingAssignment?.allowed_file_types?.includes('mp4') ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-700">MP4</span>
                    </label>
                </div>
            </div>
        </div>
    `;

    // Add event listener for late submission toggle
    document.getElementById('assignment_allow_late').addEventListener('change', function() {
        const latePenaltySection = document.getElementById('late_penalty_section');
        if (this.checked) {
            latePenaltySection.classList.remove('hidden');
        } else {
            latePenaltySection.classList.add('hidden');
        }
    });
}

// Quiz question management
let questionCounter = 0;

function addQuizQuestion() {
    questionCounter++;
    const container = document.getElementById('quiz_questions_container');
    const questionDiv = document.createElement('div');
    questionDiv.className = 'border border-gray-200 rounded-lg p-4';
    questionDiv.id = `question_${questionCounter}`;

    questionDiv.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h5 class="font-medium text-gray-900">Soal ${questionCounter}</h5>
            <button type="button" onclick="removeQuizQuestion(${questionCounter})"
                    class="text-red-600 hover:text-red-800">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan</label>
                <textarea name="questions[${questionCounter}][question]" rows="3" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Masukkan pertanyaan..."></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                    <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="multiple_choice">Pilihan Ganda</option>
                        <option value="true_false">Benar/Salah</option>
                        <option value="short_answer">Jawaban Singkat</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                    <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="10"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
            </div>

            <!-- Options for multiple choice -->
            <div id="options_${questionCounter}" class="space-y-2">
                <label class="block text-sm font-medium text-gray-700">Pilihan Jawaban</label>
                <div class="space-y-2">
                    ${generateQuestionOptions(questionCounter)}
                </div>
                <button type="button" onclick="addQuestionOption(${questionCounter})"
                        class="text-sm text-primary hover:text-primary-dark">
                    + Tambah Pilihan
                </button>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                <textarea name="questions[${questionCounter}][explanation]" rows="2"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="Penjelasan jawaban yang benar..."></textarea>
            </div>
        </div>
    `;

    container.appendChild(questionDiv);
}

function removeQuizQuestion(questionId) {
    const questionDiv = document.getElementById(`question_${questionId}`);
    if (questionDiv) {
        questionDiv.remove();
    }
}

function generateQuestionOptions(questionId) {
    return `
        <div class="flex items-center space-x-2">
            <input type="radio" name="questions[${questionId}][correct_answer]" value="0"
                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
            <input type="text" name="questions[${questionId}][options][]"
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                   placeholder="Pilihan A" required>
        </div>
        <div class="flex items-center space-x-2">
            <input type="radio" name="questions[${questionId}][correct_answer]" value="1"
                   class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
            <input type="text" name="questions[${questionId}][options][]"
                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                   placeholder="Pilihan B" required>
        </div>
    `;
}

function addQuestionOption(questionId) {
    const optionsContainer = document.querySelector(`#options_${questionId} .space-y-2`);
    const optionCount = optionsContainer.children.length;
    const optionDiv = document.createElement('div');
    optionDiv.className = 'flex items-center space-x-2';
    optionDiv.innerHTML = `
        <input type="radio" name="questions[${questionId}][correct_answer]" value="${optionCount}"
               class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
        <input type="text" name="questions[${questionId}][options][]"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
               placeholder="Pilihan ${String.fromCharCode(65 + optionCount)}" required>
        <button type="button" onclick="this.parentElement.remove()"
                class="text-red-600 hover:text-red-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;
    optionsContainer.appendChild(optionDiv);
}

function toggleQuestionOptions(questionId) {
    const typeSelect = document.querySelector(`select[name="questions[${questionId}][type]"]`);
    const optionsDiv = document.getElementById(`options_${questionId}`);

    if (typeSelect.value === 'multiple_choice') {
        optionsDiv.style.display = 'block';
    } else {
        optionsDiv.style.display = 'none';
    }
}

// CSV Import functionality
let csvData = [];

// Helper function to format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showImportModal() {
    document.getElementById('csvImportModal').classList.remove('hidden');
    document.getElementById('csvImportModal').style.display = 'flex';
    document.getElementById('csvImportModal').style.alignItems = 'center';
    document.getElementById('csvImportModal').style.justifyContent = 'center';
}

function closeImportModal() {
    document.getElementById('csvImportModal').classList.add('hidden');
    document.getElementById('csvImportModal').style.display = 'none';
    clearCSVFile();
}



function handleCSVFile(input) {
    const file = input.files[0];
    if (file) {
        // Check file size (2MB limit)
        const maxSize = 2 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File terlalu besar! Maksimal 2MB.');
            input.value = '';
            return;
        }

        // Check file type
        if (!file.name.toLowerCase().endsWith('.csv')) {
            alert('Format file tidak didukung! Gunakan file CSV.');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('csvUploadArea').style.display = 'none';
        document.getElementById('csvFileInfo').classList.remove('hidden');
        document.getElementById('csvFileName').textContent = file.name;
        document.getElementById('csvFileSize').textContent = formatFileSize(file.size);

        // Read and parse CSV
        const reader = new FileReader();
        reader.onload = function(e) {
            const csv = e.target.result;
            parseCSV(csv);
        };
        reader.readAsText(file);
    }
}

function clearCSVFile() {
    document.getElementById('csvFile').value = '';
    document.getElementById('csvUploadArea').style.display = 'block';
    document.getElementById('csvFileInfo').classList.add('hidden');
    document.getElementById('csvPreview').classList.add('hidden');
    document.getElementById('importBtn').disabled = true;
    csvData = [];
}

function parseCSV(csv) {
    const lines = csv.split('\\n');
    const headers = lines[0].split(',').map(h => h.trim());

    // Validate headers
    const requiredHeaders = ['question', 'type', 'correct_answer', 'points'];
    const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));

    if (missingHeaders.length > 0) {
        alert('Header CSV tidak lengkap! Missing: ' + missingHeaders.join(', '));
        clearCSVFile();
        return;
    }

    csvData = [];
    let validRows = 0;

    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());
        if (values.length < headers.length) continue;

        const row = {};
        headers.forEach((header, index) => {
            row[header] = values[index] || '';
        });

        // Validate required fields
        if (row.question && row.type && row.points) {
            csvData.push(row);
            validRows++;
        }
    }

    if (validRows === 0) {
        alert('Tidak ada data valid yang ditemukan dalam file CSV!');
        clearCSVFile();
        return;
    }

    // Show preview
    showCSVPreview();
    document.getElementById('importBtn').disabled = false;
}

function showCSVPreview() {
    const previewDiv = document.getElementById('csvPreviewContent');
    let html = '<div class="space-y-3">';

    csvData.slice(0, 3).forEach((row, index) => {
        html += `
            <div class="border border-gray-200 rounded-lg p-3">
                <div class="font-medium text-gray-900">Soal ${index + 1}: ${row.question}</div>
                <div class="text-sm text-gray-600 mt-1">
                    <span class="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">${getTypeLabel(row.type)}</span>
                    <span class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs">${row.points} poin</span>
                </div>
            </div>
        `;
    });

    if (csvData.length > 3) {
        html += `<div class="text-center text-gray-500 text-sm">... dan ${csvData.length - 3} soal lainnya</div>`;
    }

    html += '</div>';
    html += `<div class="mt-4 p-3 bg-green-50 rounded-lg">
        <div class="text-sm text-green-800">
            <strong>Total: ${csvData.length} soal</strong> siap untuk diimport
        </div>
    </div>`;

    previewDiv.innerHTML = html;
    document.getElementById('csvPreview').classList.remove('hidden');
}

function getTypeLabel(type) {
    switch(type) {
        case 'multiple_choice': return 'Pilihan Ganda';
        case 'true_false': return 'Benar/Salah';
        case 'short_answer': return 'Jawaban Singkat';
        default: return type;
    }
}

function importQuestions() {
    if (csvData.length === 0) {
        alert('Tidak ada data untuk diimport!');
        return;
    }

    // Clear existing questions
    document.getElementById('quiz_questions_container').innerHTML = '';
    questionCounter = 0;

    // Import each question
    csvData.forEach(row => {
        questionCounter++;
        const container = document.getElementById('quiz_questions_container');
        const questionDiv = document.createElement('div');
        questionDiv.className = 'border border-gray-200 rounded-lg p-4';
        questionDiv.id = `question_${questionCounter}`;

        let optionsHtml = '';
        if (row.type === 'multiple_choice') {
            const options = [row.option_a, row.option_b, row.option_c, row.option_d].filter(opt => opt);
            const correctIndex = ['A', 'B', 'C', 'D'].indexOf(row.correct_answer.toUpperCase());

            optionsHtml = options.map((option, index) => `
                <div class="flex items-center space-x-2">
                    <input type="radio" name="questions[${questionCounter}][correct_answer]" value="${index}"
                           ${index === correctIndex ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <input type="text" name="questions[${questionCounter}][options][]" value="${option}"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Pilihan ${String.fromCharCode(65 + index)}" required>
                </div>
            `).join('');
        } else if (row.type === 'true_false') {
            const isTrue = row.correct_answer.toUpperCase() === 'A' || row.correct_answer.toLowerCase() === 'benar';
            optionsHtml = `
                <div class="flex items-center space-x-2">
                    <input type="radio" name="questions[${questionCounter}][correct_answer]" value="0"
                           ${isTrue ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <input type="text" name="questions[${questionCounter}][options][]" value="Benar"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Benar" required readonly>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="radio" name="questions[${questionCounter}][correct_answer]" value="1"
                           ${!isTrue ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <input type="text" name="questions[${questionCounter}][options][]" value="Salah"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Salah" required readonly>
                </div>
            `;
        }

        questionDiv.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h5 class="font-medium text-gray-900">Soal ${questionCounter}</h5>
                <button type="button" onclick="removeQuizQuestion(${questionCounter})"
                        class="text-red-600 hover:text-red-800">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan</label>
                    <textarea name="questions[${questionCounter}][question]" rows="3" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Masukkan pertanyaan...">${row.question}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                        <select name="questions[${questionCounter}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="multiple_choice" ${row.type === 'multiple_choice' ? 'selected' : ''}>Pilihan Ganda</option>
                            <option value="true_false" ${row.type === 'true_false' ? 'selected' : ''}>Benar/Salah</option>
                            <option value="short_answer" ${row.type === 'short_answer' ? 'selected' : ''}>Jawaban Singkat</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                        <input type="number" name="questions[${questionCounter}][points]" min="1" max="100" value="${row.points || 10}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>

                <!-- Options for multiple choice -->
                <div id="options_${questionCounter}" class="space-y-2" ${row.type === 'short_answer' ? 'style="display: none;"' : ''}>
                    <label class="block text-sm font-medium text-gray-700">Pilihan Jawaban</label>
                    <div class="space-y-2">
                        ${optionsHtml}
                    </div>
                    ${row.type === 'multiple_choice' ? `<button type="button" onclick="addQuestionOption(${questionCounter})" class="text-sm text-primary hover:text-primary-dark">+ Tambah Pilihan</button>` : ''}
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
                    <textarea name="questions[${questionCounter}][explanation]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Penjelasan jawaban yang benar...">${row.explanation || ''}</textarea>
                </div>
            </div>
        `;

        container.appendChild(questionDiv);
    });

    // Close modal
    closeImportModal();

    // Show success message
    alert(`Berhasil mengimport ${csvData.length} soal kuis!`);
}

// Video upload functionality
function handleVideoFileSelect(input) {
    const file = input.files[0];
    if (file) {
        // Check file size (100MB limit)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File terlalu besar! Maksimal 100MB.');
            input.value = '';
            return;
        }

        // Check file type
        const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/quicktime'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung! Gunakan MP4, MOV, atau AVI.');
            input.value = '';
            return;
        }

        // Show file info
        document.getElementById('video_upload_area').classList.add('hidden');
        document.getElementById('video_file_info').classList.remove('hidden');
        document.getElementById('video_file_name').textContent = file.name;
        document.getElementById('video_file_size').textContent = formatFileSize(file.size);
    }
}

function clearVideoFile() {
    document.getElementById('video_file').value = '';
    document.getElementById('uploaded_video_path').value = '';
    document.getElementById('video_upload_area').classList.remove('hidden');
    document.getElementById('video_file_info').classList.add('hidden');
}

function formatFileSize(bytes) {
    if (bytes >= 1024 * 1024) {
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    } else if (bytes >= 1024) {
        return (bytes / 1024).toFixed(1) + ' KB';
    } else {
        return bytes + ' bytes';
    }
}

// Load existing quiz question
function loadExistingQuizQuestion(question) {
    questionCounter++;
    const container = document.getElementById('quiz_questions_container');
    const questionDiv = document.createElement('div');
    questionDiv.className = 'border border-gray-200 rounded-lg p-4';
    questionDiv.id = `question_${questionCounter}`;

    // Determine correct answer for different question types
    let correctAnswer = '';
    let optionsHtml = '';

    if (question.type === 'multiple_choice') {
        const correctOption = question.options?.find(opt => opt.is_correct);
        correctAnswer = correctOption ? question.options.indexOf(correctOption) : 0;

        optionsHtml = `
            <div id="options_${questionCounter}" class="space-y-2">
                ${question.options?.map((option, index) => `
                    <div class="flex items-center space-x-2">
                        <input type="radio" name="quiz_questions[${questionCounter - 1}][correct_answer]" value="${index}"
                               ${option.is_correct ? 'checked' : ''}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                        <input type="text" name="quiz_questions[${questionCounter - 1}][options][]"
                               value="${option.option_text || ''}"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Pilihan ${String.fromCharCode(65 + index)}" required>
                        <button type="button" onclick="removeOption(this)" class="text-red-600 hover:text-red-800">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                `).join('') || ''}
            </div>
            <button type="button" onclick="addOption(${questionCounter})" class="mt-2 text-sm text-primary hover:text-primary-dark">
                + Tambah Pilihan
            </button>
        `;
    } else if (question.type === 'true_false') {
        const correctOption = question.options?.find(opt => opt.is_correct);
        correctAnswer = correctOption?.option_text === 'Benar' ? 'true' : 'false';

        optionsHtml = `
            <div id="options_${questionCounter}" class="space-y-2">
                <label class="flex items-center">
                    <input type="radio" name="quiz_questions[${questionCounter - 1}][correct_answer]" value="true"
                           ${correctAnswer === 'true' ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <span class="ml-2">Benar</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="quiz_questions[${questionCounter - 1}][correct_answer]" value="false"
                           ${correctAnswer === 'false' ? 'checked' : ''}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300">
                    <span class="ml-2">Salah</span>
                </label>
            </div>
        `;
    } else if (question.type === 'short_answer') {
        optionsHtml = `
            <div id="options_${questionCounter}">
                <input type="text" name="quiz_questions[${questionCounter - 1}][correct_answer]"
                       value="${question.options?.[0]?.option_text || ''}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                       placeholder="Jawaban yang benar">
            </div>
        `;
    }

    questionDiv.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h5 class="font-medium text-gray-900">Soal ${questionCounter}</h5>
            <button type="button" onclick="removeQuizQuestion(${questionCounter})"
                    class="text-red-600 hover:text-red-800">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </button>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipe Soal</label>
                <select name="quiz_questions[${questionCounter - 1}][type]" onchange="toggleQuestionOptions(${questionCounter})"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="multiple_choice" ${question.type === 'multiple_choice' ? 'selected' : ''}>Pilihan Ganda</option>
                    <option value="true_false" ${question.type === 'true_false' ? 'selected' : ''}>Benar/Salah</option>
                    <option value="short_answer" ${question.type === 'short_answer' ? 'selected' : ''}>Jawaban Singkat</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Poin</label>
                <input type="number" name="quiz_questions[${questionCounter - 1}][points]" min="1" max="100"
                       value="${question.points || 10}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Pertanyaan</label>
            <textarea name="quiz_questions[${questionCounter - 1}][question]" rows="3" required
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Tulis pertanyaan di sini...">${question.question || ''}</textarea>
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Pilihan Jawaban</label>
            ${optionsHtml}
        </div>

        <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Penjelasan (Opsional)</label>
            <textarea name="quiz_questions[${questionCounter - 1}][explanation]" rows="2"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Berikan penjelasan untuk jawaban yang benar...">${question.explanation || ''}</textarea>
        </div>
    `;

    container.appendChild(questionDiv);
}

// Form validation
document.getElementById('materialForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Explicit validation for required fields
    const title = document.getElementById('title').value.trim();
    const duration = document.getElementById('duration_minutes').value.trim();
    const type = document.getElementById('type').value;

    if (!title) {
        alert('Judul materi harus diisi.');
        return false;
    }

    if (!duration || duration < 1) {
        alert('Estimasi durasi harus diisi dan minimal 1 menit.');
        return false;
    }

    if (!type) {
        alert('Tipe materi harus dipilih.');
        return false;
    }

    if (type === 'video') {
        const videoSource = document.querySelector('input[name="video_source"]:checked');
        const videoUrl = document.getElementById('video_url').value;
        const videoFile = document.getElementById('video_file').files[0];

        if (!videoSource) {
            e.preventDefault();
            alert('Pilih sumber video (URL atau Upload).');
            return false;
        }

        if (videoSource.value === 'url' && !videoUrl) {
            e.preventDefault();
            alert('Masukkan URL video.');
            return false;
        }

        if (videoSource.value === 'upload' && !videoFile && !document.getElementById('uploaded_video_path').value) {
            // Only require new upload if there's no existing video file
            const hasExistingVideo = {{ $lesson->video_file ? 'true' : 'false' }};
            if (!hasExistingVideo) {
                e.preventDefault();
                alert('Upload video terlebih dahulu.');
                return false;
            }
        }
    }

    // Disable submit button to prevent double submission
    const submitBtn = document.getElementById('submit_btn');
    const submitText = document.getElementById('submit_text');
    submitBtn.disabled = true;
    submitText.textContent = 'Menyimpan...';

    // Submit the form
    this.submit();
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Show the appropriate content based on current lesson type
    const currentType = '{{ $lesson->type }}';
    if (currentType) {
        toggleMaterialFields();
    }
});
</script>
@endpush
