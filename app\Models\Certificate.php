<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Certificate extends Model
{
    use HasFactory, HasUuids;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'certificate_id',
        'user_id',
        'type',
        'certifiable_id',
        'certifiable_type',
        'user_name',
        'title',
        'instructor_name',
        'completion_date',
        'issue_date',
        'certificate_data',
        'is_active',
        'revocation_reason',
        'revoked_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'completion_date' => 'datetime',
        'issue_date' => 'datetime',
        'revoked_at' => 'datetime',
        'certificate_data' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the certificate.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent certifiable model (course or exam).
     */
    public function certifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include active certificates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include course certificates.
     */
    public function scopeCourse($query)
    {
        return $query->where('type', 'course');
    }

    /**
     * Scope a query to only include exam certificates.
     */
    public function scopeExam($query)
    {
        return $query->where('type', 'exam');
    }

    /**
     * Revoke the certificate.
     */
    public function revoke(?string $reason = null): void
    {
        $this->update([
            'is_active' => false,
            'revocation_reason' => $reason,
            'revoked_at' => now(),
        ]);
    }

    /**
     * Reactivate the certificate.
     */
    public function reactivate(): void
    {
        $this->update([
            'is_active' => true,
            'revocation_reason' => null,
            'revoked_at' => null,
        ]);
    }
}
