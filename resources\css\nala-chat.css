/* Nala Chat Component - Mobile-First Compact Design */

/* Container for centered chat */
.nala-chat-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 90vw;
    max-width: 400px;
    height: 85vh;
    max-height: 600px;
    min-height: 400px;
}

/* Chat toggle button - smaller and centered */
.nala-chat-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #8B5CF6, #EC4899);
    border-radius: 50%;
    border: none;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
}

.nala-chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(139, 92, 246, 0.4);
}

.nala-chat-toggle:active {
    transform: scale(0.95);
}

/* Avatar in toggle button */
.nala-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.nala-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main chat window */
.nala-chat-window {
    background: #1E293B;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transform: scale(0);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.nala-chat-window.open {
    transform: scale(1);
    opacity: 1;
}

/* Chat header */
.nala-chat-header {
    background: linear-gradient(135deg, #8B5CF6, #EC4899);
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 20px 20px 0 0;
}

.nala-chat-header-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nala-chat-header-avatar {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.nala-chat-header-text h3 {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.nala-chat-header-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    margin: 0;
}

.nala-chat-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.2s;
    color: white;
}

.nala-chat-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Messages area */
.nala-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #1E293B;
    display: flex;
    flex-direction: column;
    gap: 16px;
    -webkit-overflow-scrolling: touch;
}

.nala-chat-messages::-webkit-scrollbar {
    width: 4px;
}

.nala-chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.nala-chat-messages::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

/* Message bubbles */
.nala-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: slideUp 0.3s ease;
}

.nala-message.user {
    flex-direction: row-reverse;
}

.nala-message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    flex-shrink: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nala-message-avatar.ai {
    background: linear-gradient(135deg, #8B5CF6, #EC4899);
}

.nala-message-avatar.user {
    background: #475569;
}

.nala-message-content {
    background: #334155;
    color: white;
    padding: 12px 16px;
    border-radius: 16px;
    max-width: 70%;
    font-size: 14px;
    line-height: 1.4;
}

.nala-message.user .nala-message-content {
    background: linear-gradient(135deg, #8B5CF6, #EC4899);
    border-radius: 16px 16px 4px 16px;
}

.nala-message.ai .nala-message-content {
    border-radius: 16px 16px 16px 4px;
}

/* Input area */
.nala-chat-input {
    padding: 16px 20px;
    background: #1E293B;
    border-top: 1px solid #334155;
    border-radius: 0 0 20px 20px;
}

.nala-input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.nala-input-wrapper {
    flex: 1;
    position: relative;
}

.nala-input {
    width: 100%;
    background: #334155;
    border: 1px solid #475569;
    border-radius: 20px;
    padding: 12px 16px;
    color: white;
    font-size: 14px;
    resize: none;
    outline: none;
    transition: border-color 0.2s;
    min-height: 44px;
    max-height: 100px;
}

.nala-input:focus {
    border-color: #8B5CF6;
}

.nala-input::placeholder {
    color: #94A3B8;
}

.nala-send-button {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #8B5CF6, #EC4899);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    color: white;
    flex-shrink: 0;
}

.nala-send-button:hover:not(:disabled) {
    transform: scale(1.05);
}

.nala-send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Quick actions */
.nala-quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.nala-quick-action {
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: #A78BFA;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.nala-quick-action:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile optimizations */
@media (max-width: 480px) {
    .nala-chat-container {
        width: 95vw;
        height: 90vh;
        max-height: none;
    }
    
    .nala-chat-messages {
        padding: 16px;
    }
    
    .nala-message-content {
        max-width: 80%;
        font-size: 13px;
    }
    
    .nala-chat-input {
        padding: 12px 16px;
    }
}

/* Hidden state */
.nala-hidden {
    display: none !important;
}

/* Prevent body scroll when chat is open */
body.nala-chat-open {
    overflow: hidden;
}

@media (min-width: 769px) {
    body.nala-chat-open {
        overflow: auto;
    }
}

/* Typing animation */
@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}
