<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\MembershipPlan;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MembershipPlan>
 */
class MembershipPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MembershipPlan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->randomElement([
            'Free',
            'Basic',
            'Standard',
            'Premium',
            'Pro',
            'Enterprise'
        ]);

        $price = match($name) {
            'Free' => 0,
            'Basic' => 89000,
            'Standard' => 129000,
            'Premium' => 199000,
            'Pro' => 299000,
            'Enterprise' => 499000,
            default => $this->faker->numberBetween(50000, 500000)
        };

        $nalaPrompts = match($name) {
            'Free' => null,
            'Basic' => 100,
            'Standard' => 300,
            'Premium' => 500,
            'Pro' => 1000,
            'Enterprise' => null, // Unlimited
            default => $this->faker->numberBetween(50, 1000)
        };

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(['individual', 'team']),
            'price' => $price,
            'price_per_user' => $this->faker->optional(0.3)->numberBetween(50000, 200000),
            'duration_months' => $this->faker->randomElement([1, 3, 6, 12]),
            'is_free' => $price == 0,
            'nala_prompts' => $nalaPrompts,
            'has_unlimited_nala' => $name === 'Enterprise' || $this->faker->boolean(10),
            'has_ice_full' => $name !== 'Free',
            'has_ai_teaching_assistants_courses' => $name !== 'Free',
            'has_ai_teaching_assistants_tryout' => $name !== 'Free',
            'has_free_certifications' => $name !== 'Free',
            'has_blog_access' => $name !== 'Free',
            'career_path_predictor' => $this->faker->randomElement(['basic', 'enhanced', 'advanced']),
            'has_priority_support' => in_array($name, ['Premium', 'Pro', 'Enterprise']),
            'is_team_plan' => $this->faker->boolean(20),
            'minimum_users' => 1,
            'maximum_users' => $this->faker->optional(0.7)->numberBetween(5, 100),
            'is_active' => true,
            'is_featured' => $this->faker->boolean(30),
            'sort_order' => $this->faker->numberBetween(1, 10),
            'color' => $this->faker->hexColor(),
            'features_list' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
        ];
    }

    /**
     * Indicate that the plan is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Free',
            'slug' => 'free',
            'price' => 0,
            'is_free' => true,
            'nala_prompts' => null,
            'has_unlimited_nala' => false,
            'has_ice_full' => false,
            'has_ai_teaching_assistants_courses' => false,
            'has_ai_teaching_assistants_tryout' => false,
            'has_free_certifications' => false,
            'has_blog_access' => false,
            'career_path_predictor' => 'basic',
            'has_priority_support' => false,
        ]);
    }

    /**
     * Indicate that the plan is basic.
     */
    public function basic(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Basic',
            'slug' => 'basic',
            'price' => 89000,
            'is_free' => false,
            'nala_prompts' => 100,
            'has_unlimited_nala' => false,
            'has_ice_full' => true,
            'has_ai_teaching_assistants_courses' => true,
            'has_ai_teaching_assistants_tryout' => true,
            'has_free_certifications' => true,
            'has_blog_access' => true,
            'career_path_predictor' => 'basic',
            'has_priority_support' => false,
        ]);
    }

    /**
     * Indicate that the plan is standard.
     */
    public function standard(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Standard',
            'slug' => 'standard',
            'price' => 129000,
            'is_free' => false,
            'nala_prompts' => 300,
            'has_unlimited_nala' => false,
            'has_ice_full' => true,
            'has_ai_teaching_assistants_courses' => true,
            'has_ai_teaching_assistants_tryout' => true,
            'has_free_certifications' => true,
            'has_blog_access' => true,
            'career_path_predictor' => 'enhanced',
            'has_priority_support' => false,
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the plan is for teams.
     */
    public function team(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'team',
            'is_team_plan' => true,
            'minimum_users' => 5,
            'maximum_users' => 50,
            'price_per_user' => $this->faker->numberBetween(50000, 150000),
        ]);
    }

    /**
     * Indicate that the plan is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the plan is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
