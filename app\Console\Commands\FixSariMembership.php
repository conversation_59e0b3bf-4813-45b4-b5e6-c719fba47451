<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MembershipPlan;
use App\Models\UserMembership;

class FixSariMembership extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:sari-membership';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix Sari user membership to Standard plan';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::where('email', '<EMAIL>')->first();
        if (!$user) {
            $this->error('Sari user not found');
            return;
        }

        $standardPlan = MembershipPlan::where('slug', 'standard')->first();
        if (!$standardPlan) {
            $this->error('Standard membership plan not found');
            return;
        }

        // Remove existing memberships
        $this->info('Removing existing memberships...');
        $user->memberships()->delete();

        // Create new standard membership
        $this->info('Creating standard membership...');
        $startsAt = now();
        $expiresAt = $startsAt->copy()->addMonths($standardPlan->duration_months);

        UserMembership::create([
            'user_id' => $user->id,
            'membership_plan_id' => $standardPlan->id,
            'status' => 'active',
            'starts_at' => $startsAt,
            'expires_at' => $expiresAt,
            'nala_prompts_allocated' => $standardPlan->nala_prompts ?? 0,
            'nala_prompts_remaining' => $standardPlan->nala_prompts ?? 0,
            'has_unlimited_nala' => $standardPlan->has_unlimited_nala,
            'has_ice_full' => $standardPlan->has_ice_full,
            'has_ai_teaching_assistants_courses' => $standardPlan->has_ai_teaching_assistants_courses,
            'has_ai_teaching_assistants_tryout' => $standardPlan->has_ai_teaching_assistants_tryout,
            'has_free_certifications' => $standardPlan->has_free_certifications,
            'has_blog_access' => $standardPlan->has_blog_access,
            'career_path_predictor' => $standardPlan->career_path_predictor,
            'has_priority_support' => $standardPlan->has_priority_support,
        ]);

        $this->info('Successfully updated Sari user to Standard membership!');
    }
}
