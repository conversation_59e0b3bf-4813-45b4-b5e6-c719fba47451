/* Modern Course Learning Interface */
/* Enhanced design with better UX and responsive layout */

/* CSS Variables - Modern Color Palette */
:root {
    --primary-color: #ff6b35;
    --primary-hover: #e55a2b;
    --primary-light: #fff4f1;
    --secondary-color: #2c3e50;
    --accent-color: #3498db;
    --accent-hover: #2980b9;
    --text-primary: #2c3e50;
    --text-secondary: #5a6c7d;
    --text-muted: #95a5a6;
    --text-light: #ffffff;
    --border-color: #e1e8ed;
    --border-light: #f1f3f4;
    --background-white: #ffffff;
    --background-gray: #f8fafc;
    --background-light: #fafbfc;
    --background-dark: #1a202c;
    --success-color: #27ae60;
    --success-light: #d5f4e6;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --sidebar-width: 320px;
    --header-height: 70px;
    --font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
    --border-radius: 8px;
    --border-radius-sm: 6px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background: var(--background-gray);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Main Layout */
.coursera-learning-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--background-gray);
}

/* Top Navigation Bar */
.top-nav-bar {
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
    height: var(--header-height);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 32px;
    max-width: none;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    font-weight: 500;
}

.breadcrumb-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
}

.breadcrumb-link:hover {
    background: var(--primary-light);
    color: var(--primary-hover);
}

.breadcrumb-separator {
    width: 18px;
    height: 18px;
    color: var(--text-muted);
    opacity: 0.6;
}

.breadcrumb-current {
    color: var(--text-primary);
    font-weight: 600;
}

/* Navigation Controls */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    border: 2px solid var(--border-color);
    background: var(--background-white);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.nav-btn.primary {
    background: var(--gradient-primary);
    color: var(--text-light);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.nav-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.nav-btn svg {
    width: 18px;
    height: 18px;
    transition: var(--transition-fast);
}

.nav-btn:hover svg {
    transform: scale(1.1);
}

/* Main Layout Container */
.main-layout {
    display: flex;
    flex: 1;
    min-height: calc(100vh - var(--header-height));
}

/* Fixed Left Sidebar */
.course-sidebar {
    width: var(--sidebar-width);
    background: var(--background-white);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: sticky;
    top: var(--header-height);
    height: calc(100vh - var(--header-height));
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

/* Desktop Sidebar Hidden State (1024px+) */
@media (min-width: 1024px) {
    .course-sidebar.desktop-hidden {
        transform: translateX(-100%);
        width: 0;
        min-width: 0;
        border-right: none;
        box-shadow: none;
    }
}

/* Sidebar Header */
.sidebar-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-white);
}

.menu-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.menu-toggle:hover {
    color: var(--text-primary);
    background: var(--background-gray);
}

.menu-toggle svg {
    width: 20px;
    height: 20px;
    transition: var(--transition-fast);
}

.menu-toggle:hover svg {
    transform: rotate(180deg);
}

/* Course Navigation */
.course-navigation {
    flex: 1;
    overflow-y: auto;
    background: var(--background-light);
}

.course-navigation::-webkit-scrollbar {
    width: 8px;
}

.course-navigation::-webkit-scrollbar-track {
    background: transparent;
}

.course-navigation::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: var(--transition-fast);
}

.course-navigation::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Course Info Compact */
.course-info-compact {
    padding: 24px;
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
}

.course-title-compact {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 16px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.progress-indicator {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-light);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: 10px;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-sm);
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.progress-text::before {
    content: '📊';
    font-size: 12px;
}

/* Curriculum List */
.curriculum-list {
    padding: 0;
}

.curriculum-section {
    background: var(--background-white);
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 2px;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.section-header {
    padding: 18px 24px 14px;
    background: var(--background-gray);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.section-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
}

.section-title {
    font-size: 15px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 6px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-title::before {
    content: '📚';
    font-size: 14px;
}

.section-meta {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
}

/* Section Items */
.section-items {
    padding: 0;
}

.curriculum-item {
    display: flex;
    align-items: flex-start;
    gap: 14px;
    padding: 16px 24px;
    text-decoration: none;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-light);
    transition: var(--transition-normal);
    position: relative;
    background: var(--background-white);
}

.curriculum-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.curriculum-item:hover {
    background: var(--background-gray);
    transform: translateX(4px);
}

.curriculum-item:hover::before {
    width: 4px;
}

.curriculum-item.current {
    background: var(--primary-light);
    border-left: 4px solid var(--primary-color);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transform: translateX(4px);
}

.curriculum-item.current::before {
    width: 4px;
}

.curriculum-item.current .item-title {
    color: var(--primary-color);
    font-weight: 600;
}

.curriculum-item.completed {
    background: var(--success-light);
    opacity: 0.8;
}

.curriculum-item.completed .item-title {
    color: var(--text-secondary);
    position: relative;
}

.curriculum-item.completed .item-title::after {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    right: 0;
    height: 2px;
    background: var(--success-color);
    border-radius: 1px;
}

.curriculum-item.in-progress {
    background: linear-gradient(90deg, var(--background-white) 0%, var(--primary-light) 100%);
    border-left: 4px solid var(--warning-color);
}

/* Item Status */
.item-status {
    margin-top: 4px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.status-check {
    width: 20px;
    height: 20px;
    color: var(--success-color);
    background: var(--success-light);
    border-radius: 50%;
    padding: 2px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-color);
    transition: var(--transition-fast);
    position: relative;
}

.status-dot::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: var(--background-white);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: var(--transition-fast);
}

.curriculum-item.current .status-dot {
    background: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.curriculum-item.current .status-dot::after {
    opacity: 1;
}

.curriculum-item.in-progress .status-dot {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Item Content */
.item-content {
    flex: 1;
    min-width: 0;
}

.item-type {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.8px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.item-type::before {
    content: '▶️';
    font-size: 10px;
}

.item-title {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    transition: var(--transition-fast);
}

.curriculum-item:hover .item-title {
    color: var(--primary-color);
}

/* Content Area */
.content-area {
    flex: 1;
    background: var(--background-white);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    transition: var(--transition-normal);
}

/* Content Area when Desktop Sidebar is Hidden (1024px+) */
@media (min-width: 1024px) {
    .content-area.sidebar-hidden {
        margin-left: 0;
        width: 100%;
    }
}

.content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    z-index: 1;
}

/* Content Header */
.content-header {
    padding: 32px 40px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-white);
    position: relative;
}

.lesson-navigation {
    max-width: 900px;
}

.lesson-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    line-height: 1.3;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 20px;
}

.instructor-name {
    font-size: 15px;
    color: var(--text-secondary);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.instructor-name::before {
    content: '👨‍🏫';
    font-size: 16px;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 40px 40px;
    background: var(--background-gray);
}

.main-content::-webkit-scrollbar {
    width: 8px;
}

.main-content::-webkit-scrollbar-track {
    background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

/* Content Player */
.content-player {
    margin: 32px 0;
    max-width: 900px;
}

.preview-content {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.preview-content:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.preview-image {
    position: relative;
    aspect-ratio: 16/9;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--background-dark) 100%);
    overflow: hidden;
}

.course-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.preview-content:hover .course-image {
    transform: scale(1.05);
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
    backdrop-filter: blur(2px);
}

.preview-content:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.play-button svg {
    width: 32px;
    height: 32px;
    color: var(--text-light);
    margin-left: 4px;
}

.play-text {
    color: var(--text-light);
    font-size: 16px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.preview-info {
    padding: 32px;
    background: var(--background-white);
}

.lesson-badge {
    display: inline-block;
    background: var(--primary-light);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 16px;
}

.preview-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.preview-description {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 20px 0;
}

.preview-meta {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 14px;
    color: var(--text-muted);
}

.lesson-number {
    background: var(--background-gray);
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.lesson-duration {
    display: flex;
    align-items: center;
    gap: 4px;
}

.play-overlay {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.play-button:hover {
    background: white;
    transform: scale(1.1);
}

.play-button svg {
    width: 32px;
    height: 32px;
    color: var(--text-primary);
    margin-left: 4px;
}

.preview-info {
    padding: 20px;
}

.preview-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.preview-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
}

/* Completion Content */
.completion-content {
    text-align: center;
    padding: 60px 20px;
    background: var(--background-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.completion-icon {
    width: 80px;
    height: 80px;
    background: var(--success-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.completion-icon svg {
    width: 40px;
    height: 40px;
    color: white;
}

.completion-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;
}

.completion-description {
    font-size: 16px;
    color: var(--text-secondary);
    margin: 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Content Tabs */
.content-tabs {
    margin-top: 32px;
    max-width: 800px;
}

.tab-navigation {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 24px;
}

.tab-btn {
    padding: 12px 0;
    margin-right: 32px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.tab-btn:hover {
    color: var(--text-primary);
}

.tab-btn.active {
    color: var(--primary-blue);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-blue);
}

/* Tab Content */
.tab-content {
    min-height: 200px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Overview Content */
.overview-content {
    padding: 0;
}

.content-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 16px 0;
}

.course-description {
    margin-bottom: 32px;
}

.course-description p {
    font-size: 16px;
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
}

/* Course Stats */
.course-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--background-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
}

.stat-icon svg {
    width: 24px;
    height: 24px;
    color: var(--text-light);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

/* Start Learning */
.start-learning {
    text-align: center;
}

.start-btn {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 16px 32px;
    background: var(--gradient-primary);
    color: var(--text-light);
    text-decoration: none;
    font-size: 18px;
    font-weight: 700;
    border-radius: var(--border-radius-lg);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.start-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.start-btn:hover::before {
    left: 100%;
}

.start-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.start-btn svg {
    width: 20px;
    height: 20px;
    transition: var(--transition-fast);
}

.start-btn:hover svg {
    transform: scale(1.1);
}

/* Empty States */
.empty-state {
    font-size: 16px;
    color: var(--text-secondary);
    text-align: center;
    padding: 40px 20px;
    background: var(--background-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* Desktop Sidebar Hover Effects */
.course-sidebar:hover {
    box-shadow: var(--shadow-md);
}

.course-sidebar .curriculum-item:hover {
    background: var(--background-gray);
    transform: translateX(2px);
}

.course-sidebar .menu-toggle:hover {
    background: var(--background-gray);
    transform: scale(1.05);
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    backdrop-filter: blur(4px);
}

.mobile-sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Sidebar */
.mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 320px;
    height: 100vh;
    background: var(--background-white);
    z-index: 1000;
    transform: translateX(-100%);
    transition: var(--transition-normal);
    box-shadow: var(--shadow-xl);
    overflow-y: auto;
    display: none;
}

.mobile-sidebar.active {
    transform: translateX(0);
}

.mobile-sidebar-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    background: var(--background-gray);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 10;
}

.mobile-sidebar-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.mobile-sidebar-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.mobile-sidebar-close:hover {
    background: var(--background-white);
    color: var(--text-primary);
}

.mobile-sidebar-close svg {
    width: 24px;
    height: 24px;
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.mobile-menu-toggle:hover {
    background: var(--background-gray);
}

.mobile-menu-toggle svg {
    width: 24px;
    height: 24px;
}

/* Desktop Hamburger Toggle Button (1024px+) */
@media (min-width: 1024px) {
    .desktop-hamburger-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: var(--border-radius-sm);
        transition: var(--transition-fast);
        margin-right: 16px;
    }

    .desktop-hamburger-toggle:hover {
        background: var(--background-gray);
        color: var(--text-primary);
    }

    .desktop-hamburger-toggle:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
        border-radius: var(--border-radius-sm);
    }

    .desktop-hamburger-toggle svg {
        width: 18px;
        height: 18px;
        transition: var(--transition-fast);
        flex-shrink: 0;
    }

    .desktop-hamburger-toggle:hover svg {
        transform: scale(1.1);
    }

    .desktop-hamburger-toggle span {
        font-family: var(--font-family);
        white-space: nowrap;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    :root {
        --sidebar-width: 280px;
    }

    .nav-container {
        padding: 0 24px;
    }

    .main-content {
        padding: 0 32px 32px;
    }

    .content-header {
        padding: 24px 32px 20px;
    }
}

@media (max-width: 768px) {
    :root {
        --header-height: 60px;
    }

    /* Hide desktop sidebar */
    .course-sidebar {
        display: none;
    }

    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Show mobile sidebar */
    .mobile-sidebar {
        display: block;
    }

    /* Adjust main layout */
    .main-layout {
        flex-direction: column;
    }

    .content-area {
        width: 100%;
        margin-left: 0;
    }

    .nav-container {
        padding: 0 20px;
    }

    .main-content {
        padding: 0 20px 32px;
    }

    .content-header {
        padding: 20px 20px 16px;
    }

    .lesson-title {
        font-size: 24px;
    }

    .breadcrumb-nav {
        flex: 1;
        margin-right: 16px;
    }

    /* Adjust content spacing */
    .content-player {
        margin: 24px 0;
    }

    .preview-info {
        padding: 24px;
    }

    .course-stats {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 16px;
    }
}

/* Laptop optimizations (1024px and below but above 768px) */
@media (max-width: 1024px) and (min-width: 769px) {
    :root {
        --sidebar-width: 280px;
        --header-height: 60px;
    }

    /* Keep desktop sidebar visible for laptops */
    .course-sidebar {
        width: var(--sidebar-width);
    }

    /* Hide mobile elements for laptops */
    .mobile-menu-toggle {
        display: none;
    }

    .mobile-sidebar {
        display: none;
    }

    .nav-container {
        padding: 0 20px;
    }

    .main-content {
        padding: 0 24px 32px;
    }

    .content-header {
        padding: 20px 24px 16px;
    }

    .lesson-title {
        font-size: 26px;
    }

    .course-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .curriculum-item {
        padding: 14px 20px;
    }

    .section-header {
        padding: 16px 20px 12px;
    }
}

/* Additional mobile optimizations for smaller tablets/large phones */
@media (max-width: 768px) {
    :root {
        --header-height: 56px;
    }

    .nav-controls {
        display: none;
    }

    .breadcrumb-nav {
        font-size: 12px;
        gap: 6px;
    }

    .breadcrumb-link {
        padding: 2px 4px;
    }

    .mobile-sidebar {
        width: 280px;
    }

    .course-stats {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .tab-navigation {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding-bottom: 2px;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .tab-navigation::-webkit-scrollbar {
        display: none;
    }

    .tab-btn {
        margin-right: 20px;
        white-space: nowrap;
        font-size: 13px;
        padding: 8px 16px;
        min-width: auto;
    }

    .content-player {
        margin: 20px 0;
    }

    .preview-info {
        padding: 20px;
    }

    .preview-title {
        font-size: 20px;
    }

    .lesson-title {
        font-size: 22px;
        line-height: 1.2;
    }

    .nav-container {
        padding: 0 16px;
    }

    .main-content {
        padding: 0 16px 24px;
    }

    .content-header {
        padding: 16px;
    }

    /* Mobile sidebar content adjustments */
    .mobile-sidebar .course-info-compact {
        padding: 20px;
    }

    .mobile-sidebar .curriculum-item {
        padding: 12px 16px;
    }

    .mobile-sidebar .section-header {
        padding: 14px 16px 10px;
    }

    .start-btn {
        padding: 14px 24px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    :root {
        --header-height: 52px;
    }

    .lesson-title {
        font-size: 18px;
    }

    .mobile-sidebar {
        width: 260px;
    }

    .nav-container {
        padding: 0 12px;
    }

    .main-content {
        padding: 0 12px 20px;
    }

    .content-header {
        padding: 12px;
    }

    .mobile-sidebar .curriculum-item {
        padding: 10px 12px;
        gap: 10px;
    }

    .mobile-sidebar .item-title {
        font-size: 13px;
    }

    .mobile-sidebar .item-type {
        font-size: 10px;
    }

    .mobile-sidebar .section-header {
        padding: 12px;
    }

    .mobile-sidebar .section-title {
        font-size: 13px;
    }

    .breadcrumb-nav {
        font-size: 11px;
    }

    .tab-btn {
        font-size: 12px;
        padding: 6px 12px;
        margin-right: 16px;
    }

    .preview-info {
        padding: 16px;
    }

    .preview-title {
        font-size: 18px;
    }

    .start-btn {
        padding: 12px 20px;
        font-size: 14px;
    }
}

/* Focus States for Accessibility */
.nav-btn:focus,
.tab-btn:focus,
.start-btn:focus,
.curriculum-item:focus,
.menu-toggle:focus,
.breadcrumb-link:focus,
.mobile-menu-toggle:focus,
.mobile-sidebar-close:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* Mobile Sidebar Animations */
.mobile-sidebar .curriculum-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: slideInLeft 0.3s ease forwards;
}

.mobile-sidebar .curriculum-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-sidebar .curriculum-item:nth-child(2) { animation-delay: 0.15s; }
.mobile-sidebar .curriculum-item:nth-child(3) { animation-delay: 0.2s; }
.mobile-sidebar .curriculum-item:nth-child(4) { animation-delay: 0.25s; }
.mobile-sidebar .curriculum-item:nth-child(5) { animation-delay: 0.3s; }

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Mobile Sidebar Scrolling */
.mobile-sidebar .course-navigation {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.mobile-sidebar .course-navigation::-webkit-scrollbar {
    width: 6px;
}

.mobile-sidebar .course-navigation::-webkit-scrollbar-track {
    background: transparent;
}

.mobile-sidebar .course-navigation::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.mobile-sidebar .course-navigation::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Fade in animation for content */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide in animation for sidebar */
.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Hover effects for interactive elements */
.interactive-hover {
    transition: var(--transition-normal);
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Modern Visual Enhancements */
.content-tabs {
    background: var(--background-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    margin-top: 32px;
}

.tab-navigation {
    background: var(--background-gray);
    padding: 8px;
    display: flex;
    gap: 4px;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.tab-btn.active {
    background: var(--background-white);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.tab-btn:hover:not(.active) {
    background: var(--background-white);
    color: var(--text-primary);
}

.tab-content {
    padding: 32px;
}

.tab-pane {
    display: none;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition-normal);
}

.tab-pane.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Content Styling */
.content-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 20px 0;
    position: relative;
    padding-left: 20px;
}

.content-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Print Styles */
@media print {
    .course-sidebar,
    .top-nav-bar,
    .mobile-sidebar,
    .mobile-sidebar-overlay,
    .mobile-menu-toggle {
        display: none !important;
    }

    .content-area {
        margin: 0 !important;
    }

    .main-content {
        padding: 0 !important;
    }

    .tab-navigation {
        display: none !important;
    }

    .tab-pane {
        display: block !important;
        opacity: 1 !important;
        transform: none !important;
    }
}
