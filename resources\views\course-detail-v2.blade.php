@extends('layouts.app')

@section('title', $course->title . ' - Ngambiskuy')

@section('content')
<!-- Hero Section -->
<div class="bg-gradient-to-br from-orange-50 via-white to-orange-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-600">
                <li><a href="{{ route('home') }}" class="hover:text-primary transition-colors">Beranda</a></li>
                <li><span class="mx-2">/</span></li>
                <li><a href="{{ route('courses.index') }}" class="hover:text-primary transition-colors">Kursus</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-gray-500">{{ $course->category->name ?? 'Kategori' }}</li>
            </ol>
        </nav>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Course Info -->
            <div class="lg:col-span-2 lg:pr-8">
                <h1 class="text-3xl lg:text-4xl font-bold mb-4 leading-tight text-gray-900">{{ $course->title }}</h1>
                <p class="text-xl text-gray-700 mb-6 leading-relaxed">{{ $course->description }}</p>
                
                <!-- Course Meta -->
                <div class="flex flex-wrap items-center gap-6 mb-6">
                    @if($course->average_rating > 0)
                    <div class="flex items-center">
                        <span class="text-yellow-400 font-bold text-lg mr-2">{{ number_format($course->average_rating, 1) }}</span>
                        <div class="flex items-center mr-2">
                            @for($i = 1; $i <= 5; $i++)
                                @if($i <= floor($course->average_rating))
                                    <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4 text-gray-400 fill-current" viewBox="0 0 20 20">
                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                    </svg>
                                @endif
                            @endfor
                        </div>
                        <span class="text-gray-600 text-sm">({{ number_format($course->total_reviews) }} ulasan)</span>
                    </div>
                    @endif
                    
                    <div class="text-gray-700">
                        <span class="font-medium">{{ number_format($course->total_students) }}</span> siswa
                    </div>
                </div>

                <!-- Instructor Info -->
                <div class="flex items-center mb-6">
                    <span class="text-gray-700 mr-2">Dibuat oleh</span>
                    <div class="flex items-center">
                        @if($course->tutor->profile_photo)
                            <img src="{{ asset('storage/' . $course->tutor->profile_photo) }}" alt="{{ $course->tutor->name }}" class="w-8 h-8 rounded-full mr-3">
                        @else
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-sm font-medium">{{ substr($course->tutor->name, 0, 1) }}</span>
                            </div>
                        @endif
                        @if($course->tutor->tutorProfile && $course->tutor->tutorProfile->public_name_slug)
                            <a href="{{ route('tutor.public-profile', $course->tutor->tutorProfile->public_name_slug) }}" class="text-primary hover:text-primary-light transition-colors font-medium">{{ $course->tutor->name }}</a>
                        @else
                            <span class="text-primary font-medium">{{ $course->tutor->name }}</span>
                        @endif
                    </div>
                </div>

                <!-- Course Stats -->
                <div class="flex flex-wrap items-center gap-6 text-sm text-gray-600">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Terakhir diperbarui {{ $course->updated_at->format('M Y') }}
                    </div>
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                        {{ $courseFeatures['language'] }}
                    </div>
                </div>
            </div>

            <!-- Preview Video/Image -->
            <div class="lg:col-span-1">
                <div class="relative bg-white rounded-lg overflow-hidden shadow-2xl border border-gray-200">
                    @if($course->preview_video)
                        <div class="aspect-video">
                            <video class="w-full h-full object-cover" poster="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/course-placeholder.svg') }}" controls>
                                <source src="{{ $course->preview_video }}" type="video/mp4">
                                Browser Anda tidak mendukung video.
                            </video>
                        </div>
                    @else
                        <div class="aspect-video relative">
                            <img src="{{ $course->thumbnail ? asset('storage/' . $course->thumbnail) : asset('images/course-placeholder.svg') }}"
                                 alt="{{ $course->title }}" class="w-full h-full object-cover">
                            <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                                <button class="bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-300 rounded-full p-4">
                                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Spacer to align with hero content -->
                <div class="lg:hidden"></div>
                <!-- What You'll Learn -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Yang Akan Anda Pelajari</h2>
                    @if($course->learning_outcomes && count($course->learning_outcomes) > 0)
                        <div class="grid md:grid-cols-2 gap-4">
                            @foreach($course->learning_outcomes as $outcome)
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $outcome }}</span>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-600">Informasi pembelajaran akan segera tersedia.</p>
                    @endif
                </div>

                <!-- About This Course -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang Kursus Ini</h2>
                    <div class="prose max-w-none text-gray-700 leading-relaxed">
                        <p class="text-lg mb-6">{{ $course->long_description ?: $course->description }}</p>
                    </div>
                </div>

                <!-- Course Curriculum -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Kurikulum Kursus</h2>
                        <div class="text-sm text-gray-600">
                            {{ $course->chapters->count() }} bagian • {{ $totalLessons }} pelajaran • {{ $formattedDuration }}
                        </div>
                    </div>

                    @if($course->chapters->count() > 0)
                        <div class="space-y-4">
                            @foreach($course->chapters as $chapter)
                                <div class="border border-gray-200 rounded-lg">
                                    <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900">{{ $chapter->title }}</h3>
                                            <span class="text-sm text-gray-600">{{ $chapter->lessons->count() }} pelajaran</span>
                                        </div>
                                        @if($chapter->description)
                                            <p class="text-gray-600 text-sm mt-2">{{ $chapter->description }}</p>
                                        @endif
                                    </div>
                                    
                                    <div class="divide-y divide-gray-100">
                                        @foreach($chapter->lessons as $lesson)
                                            <div class="px-6 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
                                                <div class="flex items-center">
                                                    @if($lesson->type === 'video')
                                                        <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-10V7a3 3 0 01-3 3H6a3 3 0 01-3-3V4a3 3 0 013-3h7a3 3 0 013 3z"></path>
                                                        </svg>
                                                    @elseif($lesson->type === 'quiz')
                                                        <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                    @endif
                                                    <span class="text-gray-900">{{ $lesson->title }}</span>
                                                </div>
                                                <div class="flex items-center text-sm text-gray-500">
                                                    @if($lesson->duration_minutes)
                                                        <span>{{ $lesson->duration_minutes }} menit</span>
                                                    @endif
                                                    @if($isEnrolled || $isTutor)
                                                        <svg class="w-4 h-4 ml-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    @else
                                                        <svg class="w-4 h-4 ml-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                                        </svg>
                                                    @endif
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-12">
                            <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Kurikulum Sedang Disiapkan</h3>
                            <p class="text-gray-600">Materi pembelajaran akan segera tersedia.</p>
                        </div>
                    @endif
                </div>

                <!-- Requirements -->
                @if($course->requirements && count($course->requirements) > 0)
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Persyaratan</h2>
                    <ul class="space-y-3">
                        @foreach($course->requirements as $requirement)
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">{{ $requirement }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Target Audience -->
                @if($course->target_audience && count($course->target_audience) > 0)
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Untuk Siapa Kursus Ini</h2>
                    <ul class="space-y-3">
                        @foreach($course->target_audience as $audience)
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-primary mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">{{ $audience }}</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Instructor Profile -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang Instruktur</h2>
                    <div class="flex items-start space-x-4">
                        @if($course->tutor->profile_photo)
                            <img src="{{ asset('storage/' . $course->tutor->profile_photo) }}" alt="{{ $course->tutor->name }}" class="w-20 h-20 rounded-full">
                        @else
                            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center">
                                <span class="text-white text-2xl font-bold">{{ substr($course->tutor->name, 0, 1) }}</span>
                            </div>
                        @endif
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $course->tutor->name }}</h3>
                            @if($course->tutor->bio)
                                <p class="text-gray-600 mb-4">{{ $course->tutor->bio }}</p>
                            @endif

                            <!-- Instructor Stats -->
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Total Kursus:</span>
                                    <span class="font-medium text-gray-900">{{ $course->tutor->courses()->published()->count() }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Total Siswa:</span>
                                    <span class="font-medium text-gray-900">{{ number_format($course->tutor->courses()->published()->sum('total_students')) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Floating Sidebar -->
            <div class="lg:col-span-1">
                <div class="sticky top-8">
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
                        <!-- Course Price & CTA -->
                        <div class="p-6">
                            <div class="text-center mb-6">
                                @if($course->is_free)
                                    <div class="text-3xl font-bold text-green-600 mb-2">Gratis</div>
                                    <p class="text-gray-600 text-sm">Akses selamanya</p>
                                @else
                                    <div class="text-3xl font-bold text-gray-900 mb-2">
                                        Rp {{ number_format($course->price, 0, ',', '.') }}
                                    </div>
                                    <p class="text-gray-600 text-sm">Pembayaran satu kali</p>
                                @endif
                            </div>

                            <!-- Action Buttons -->
                            @auth
                                @if($isTutor)
                                    <a href="{{ route('tutor.curriculum.index', $course) }}" class="w-full btn btn-primary btn-lg mb-3 text-center block">
                                        Kelola Kurikulum
                                    </a>
                                @elseif($isEnrolled)
                                    <a href="{{ route('course.learn', $course) }}" class="w-full btn btn-primary btn-lg mb-3 text-center block">
                                        Lanjutkan Belajar
                                    </a>
                                @else
                                    <form action="{{ route('course.enroll', $course) }}" method="POST" class="w-full mb-3">
                                        @csrf
                                        <button type="submit" class="w-full btn btn-primary btn-lg">
                                            @if($course->is_free)
                                                Mulai Belajar Gratis
                                            @else
                                                Daftar Sekarang
                                            @endif
                                        </button>
                                    </form>
                                @endif
                            @else
                                <a href="{{ route('login') }}" class="w-full btn btn-primary btn-lg mb-3 text-center block">
                                    Masuk untuk Mendaftar
                                </a>
                            @endauth

                            <!-- Wishlist & Share -->
                            <div class="flex space-x-2">
                                <button class="flex-1 btn btn-outline text-sm py-2">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    Wishlist
                                </button>
                                <button class="flex-1 btn btn-outline text-sm py-2">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                    Bagikan
                                </button>
                            </div>
                        </div>

                        <!-- Course Features -->
                        <div class="border-t border-gray-200 p-6">
                            <h3 class="font-semibold text-gray-900 mb-4">Kursus ini termasuk:</h3>
                            <ul class="space-y-3 text-sm">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $courseFeatures['total_lessons'] }} pelajaran video</span>
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-700">{{ $courseFeatures['total_duration'] }} konten</span>
                                </li>
                                @if($courseFeatures['mobile_access'])
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 011 1v11a1 1 0 01-1 1H5a1 1 0 01-1-1V7zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"></path>
                                    </svg>
                                    <span class="text-gray-700">Akses di mobile dan TV</span>
                                </li>
                                @endif
                                @if($courseFeatures['lifetime_access'])
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-700">Akses seumur hidup</span>
                                </li>
                                @endif
                                @if($courseFeatures['certificate'])
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 011 1v3a1 1 0 11-2 0v-3a1 1 0 011-1zm-3 3a1 1 0 100 2h.01a1 1 0 100-2H10zm-4 1a1 1 0 011-1h.01a1 1 0 110 2H7a1 1 0 01-1-1zm1-4a1 1 0 100 2h.01a1 1 0 100-2H7zm2 0a1 1 0 100 2h.01a1 1 0 100-2H9zm2 0a1 1 0 100 2h.01a1 1 0 100-2H11z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-700">Sertifikat penyelesaian</span>
                                </li>
                                @endif
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-gray-700">Garansi 30 hari uang kembali</span>
                                </li>
                            </ul>
                        </div>

                        <!-- Course Stats -->
                        <div class="border-t border-gray-200 p-6">
                            <h3 class="font-semibold text-gray-900 mb-4">Detail Kursus:</h3>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Level:</span>
                                    <span class="font-medium text-gray-900">{{ $courseFeatures['level'] }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Bahasa:</span>
                                    <span class="font-medium text-gray-900">{{ $courseFeatures['language'] }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Siswa:</span>
                                    <span class="font-medium text-gray-900">{{ number_format($course->total_students) }}</span>
                                </div>
                                @if($course->average_rating > 0)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Rating:</span>
                                    <div class="flex items-center">
                                        <span class="font-medium text-gray-900 mr-1">{{ number_format($course->average_rating, 1) }}</span>
                                        <div class="flex">
                                            @for($i = 1; $i <= 5; $i++)
                                                <svg class="w-3 h-3 {{ $i <= floor($course->average_rating) ? 'text-yellow-400' : 'text-gray-300' }} fill-current" viewBox="0 0 20 20">
                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                </svg>
                                            @endfor
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Training for Teams -->
                        <div class="border-t border-gray-200 p-6 bg-gray-50">
                            <h3 class="font-semibold text-gray-900 mb-2">Pelatihan untuk Tim</h3>
                            <p class="text-sm text-gray-600 mb-4">Dapatkan akses tim untuk 5 orang atau lebih dengan harga khusus.</p>
                            <button class="w-full btn btn-outline text-sm py-2">
                                Coba Ngambiskuy Business
                            </button>
                        </div>
                    </div>

                    <!-- Tutor's Other Courses -->
                    @if($tutorCourses->count() > 0)
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden mt-6">
                        <div class="p-6">
                            <h3 class="font-semibold text-gray-900 mb-4">Kursus Lain dari {{ $course->tutor->name }}</h3>
                            <div class="space-y-4">
                                @foreach($tutorCourses as $tutorCourse)
                                    <div class="flex space-x-3">
                                        <div class="flex-shrink-0">
                                            <img src="{{ $tutorCourse->thumbnail ? asset('storage/' . $tutorCourse->thumbnail) : asset('images/course-placeholder.svg') }}"
                                                 alt="{{ $tutorCourse->title }}" class="w-16 h-12 object-cover rounded">
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-sm font-medium text-gray-900 line-clamp-2 mb-1">{{ $tutorCourse->title }}</h4>
                                            <div class="flex items-center justify-between">
                                                @if($tutorCourse->average_rating > 0)
                                                    <div class="flex items-center">
                                                        <span class="text-yellow-500 text-xs font-medium mr-1">{{ number_format($tutorCourse->average_rating, 1) }}</span>
                                                        <div class="flex">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <svg class="w-2.5 h-2.5 {{ $i <= floor($tutorCourse->average_rating) ? 'text-yellow-400' : 'text-gray-300' }} fill-current" viewBox="0 0 20 20">
                                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                                </svg>
                                                            @endfor
                                                        </div>
                                                    </div>
                                                @endif
                                                <span class="text-primary font-bold text-sm">
                                                    @if($tutorCourse->is_free)
                                                        Gratis
                                                    @else
                                                        Rp {{ number_format($tutorCourse->price, 0, ',', '.') }}
                                                    @endif
                                                </span>
                                            </div>
                                            <a href="{{ route('course.show', $tutorCourse) }}" class="text-xs text-primary hover:text-primary-dark transition-colors">
                                                Lihat Kursus →
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Related Courses -->
                    @if($relatedCourses->count() > 0)
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden mt-6">
                        <div class="p-6">
                            <h3 class="font-semibold text-gray-900 mb-4">Kursus Terkait</h3>
                            <div class="space-y-4">
                                @foreach($relatedCourses as $relatedCourse)
                                    <div class="flex space-x-3">
                                        <div class="flex-shrink-0">
                                            <img src="{{ $relatedCourse->thumbnail ? asset('storage/' . $relatedCourse->thumbnail) : asset('images/course-placeholder.svg') }}"
                                                 alt="{{ $relatedCourse->title }}" class="w-16 h-12 object-cover rounded">
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-sm font-medium text-gray-900 line-clamp-2 mb-1">{{ $relatedCourse->title }}</h4>
                                            <p class="text-xs text-gray-600 mb-1">{{ $relatedCourse->tutor->name }}</p>
                                            <div class="flex items-center justify-between">
                                                @if($relatedCourse->average_rating > 0)
                                                    <div class="flex items-center">
                                                        <span class="text-yellow-500 text-xs font-medium mr-1">{{ number_format($relatedCourse->average_rating, 1) }}</span>
                                                        <div class="flex">
                                                            @for($i = 1; $i <= 5; $i++)
                                                                <svg class="w-2.5 h-2.5 {{ $i <= floor($relatedCourse->average_rating) ? 'text-yellow-400' : 'text-gray-300' }} fill-current" viewBox="0 0 20 20">
                                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                                </svg>
                                                            @endfor
                                                        </div>
                                                    </div>
                                                @endif
                                                <span class="text-primary font-bold text-sm">
                                                    @if($relatedCourse->is_free)
                                                        Gratis
                                                    @else
                                                        Rp {{ number_format($relatedCourse->price, 0, ',', '.') }}
                                                    @endif
                                                </span>
                                            </div>
                                            <a href="{{ route('course.show', $relatedCourse) }}" class="text-xs text-primary hover:text-primary-dark transition-colors">
                                                Lihat Kursus →
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .aspect-video {
        aspect-ratio: 16 / 9;
    }

    /* Sticky sidebar behavior */
    @media (min-width: 1024px) {
        .sticky {
            position: sticky;
        }
    }
</style>
@endpush

@push('scripts')
<script>
// Add course context for Nala AI
window.courseContext = {
    course: {
        id: {{ $course->id }},
        title: @json($course->title),
        description: @json($course->description),
        long_description: @json($course->long_description),
        level: @json($course->level),
        price: {{ $course->price ?? 0 }},
        is_free: {{ $course->is_free ? 'true' : 'false' }},
        tutor: @json($course->tutor->name),
        tutor_slug: @json($course->tutor->tutorProfile->public_name_slug ?? null),
        category: @json($course->category->name ?? null),
        students_count: {{ $course->total_students ?? 0 }},
        rating: {{ $course->average_rating ?? 0 }},
        total_lessons: {{ $totalLessons ?? 0 }},
        duration: @json($formattedDuration ?? ''),
        language: @json($courseFeatures['language'] ?? 'Bahasa Indonesia'),
        url: @json(url()->current())
    }
};
</script>
@endpush
